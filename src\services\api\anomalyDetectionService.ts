
import { coinGeckoAxios, handleApiError, cacheResponse } from "./coinGeckoClient";
import { fetchTopCoins, fetchTrendingCoins } from "./coinMarketData";

// Interface for anomaly data
export interface Anomaly {
  id: string;
  type: string;
  asset: string;
  confidence: number;
  impact: "High" | "Medium" | "Low";
  timeDetected: string;
  status: "Active" | "Monitoring" | "Resolved";
}

// Generate anomalies based on real market data
export const fetchAnomalyData = async (): Promise<Anomaly[]> => {
  try {
    // Get real market data to inform our anomaly detection
    const topCoins = await fetchTopCoins(20);
    const trendingCoins = await fetchTrendingCoins();
    
    // Combine data sources
    const allCoins = [...topCoins];
    
    // Generate anomalies based on price volatility and volume
    const anomalies: Anomaly[] = [];
    
    // Only generate anomalies for coins with significant price changes
    const volatileCoins = topCoins.filter(coin => {
      const priceChange = coin.price_change_percentage_24h || 0;
      return Math.abs(priceChange) > 5; // Coins with >5% price change
    });
    
    // Volume anomalies - unusually high trading volume
    const volumeAnomalies = topCoins
      .filter(coin => {
        // Check if volume to market cap ratio is unusually high
        const volumeToMarketCap = coin.total_volume / (coin.market_cap || 1);
        return volumeToMarketCap > 0.2; // Consider high if volume > 20% of market cap
      })
      .slice(0, 3)
      .map((coin, index) => ({
        id: `volume-${coin.id}-${Date.now()}`,
        type: "Unusual Volume",
        asset: `${coin.name} (${coin.symbol.toUpperCase()})`,
        confidence: 75 + Math.floor(Math.random() * 20),
        impact: "Medium" as "High" | "Medium" | "Low",
        timeDetected: new Date(Date.now() - Math.floor(Math.random() * 12 * 60 * 60 * 1000)).toISOString(),
        status: "Active" as "Active" | "Monitoring" | "Resolved"
      }));
    
    // Price anomalies - unusual price movements
    const priceAnomalies = volatileCoins
      .slice(0, 3)
      .map((coin, index) => {
        const priceChange = coin.price_change_percentage_24h || 0;
        return {
          id: `price-${coin.id}-${Date.now()}`,
          type: priceChange > 0 ? "Price Spike" : "Price Drop",
          asset: `${coin.name} (${coin.symbol.toUpperCase()})`,
          confidence: 80 + Math.floor(Math.random() * 15),
          impact: Math.abs(priceChange) > 10 ? "High" : "Medium" as "High" | "Medium" | "Low",
          timeDetected: new Date(Date.now() - Math.floor(Math.random() * 8 * 60 * 60 * 1000)).toISOString(),
          status: "Active" as "Active" | "Monitoring" | "Resolved"
        };
      });
    
    // Pattern anomalies - technical pattern breaks
    const patternAnomalies = allCoins
      .slice(0, 2)
      .map((coin, index) => ({
        id: `pattern-${index}-${Date.now()}`,
        type: "Pattern Break",
        asset: `${coin.name || coin.symbol.toUpperCase()} (${coin.symbol?.toUpperCase()})`,
        confidence: 70 + Math.floor(Math.random() * 15),
        impact: "Low" as "High" | "Medium" | "Low",
        timeDetected: new Date(Date.now() - Math.floor(Math.random() * 24 * 60 * 60 * 1000)).toISOString(),
        status: Math.random() > 0.5 ? "Monitoring" : "Active" as "Active" | "Monitoring" | "Resolved"
      }));
    
    // Whale movement anomalies
    const whaleAnomalies = [
      {
        id: `whale-btc-${Date.now()}`,
        type: "Whale Movement",
        asset: "Bitcoin (BTC)",
        confidence: 90,
        impact: "High" as "High" | "Medium" | "Low",
        timeDetected: new Date(Date.now() - Math.floor(Math.random() * 6 * 60 * 60 * 1000)).toISOString(),
        status: "Active" as "Active" | "Monitoring" | "Resolved"
      },
      {
        id: `whale-eth-${Date.now()}`,
        type: "Whale Movement",
        asset: "Ethereum (ETH)",
        confidence: 85,
        impact: "Medium" as "High" | "Medium" | "Low",
        timeDetected: new Date(Date.now() - Math.floor(Math.random() * 10 * 60 * 60 * 1000)).toISOString(),
        status: "Monitoring" as "Active" | "Monitoring" | "Resolved"
      }
    ];
    
    // Combine all anomalies
    anomalies.push(...volumeAnomalies, ...priceAnomalies, ...patternAnomalies, ...whaleAnomalies);
    
    // Sort by time detected (most recent first)
    anomalies.sort((a, b) => new Date(b.timeDetected).getTime() - new Date(a.timeDetected).getTime());
    
    cacheResponse("anomalies", anomalies);
    return anomalies;
  } catch (error) {
    return handleApiError(error, {
      key: "anomalies",
      data: []
    });
  }
};
