
import { cacheResponse, handleApiError } from "../coinGeckoClient";
import { GasInfo } from "./types";

// Fetch gas data and optimization recommendations
export const fetchGasData = async (): Promise<GasInfo[]> => {
  try {
    // In real app, this would call Etherscan API, ETH Gas Station, etc.
    
    // Networks to fetch gas data for
    const networks = [
      { id: "ethereum", name: "Ethereum" },
      { id: "polygon", name: "Polygon" },
      { id: "bsc", name: "BSC" },
      { id: "arbitrum", name: "Arbitrum" },
      { id: "optimism", name: "Optimism" }
    ];
    
    // Generate historical gas prices (past 24h)
    const generateHistoricalGas = (baseGwei: number, volatility: number) => {
      const hours = 24;
      const data = [];
      const now = Date.now();
      
      for (let i = 0; i < hours; i++) {
        const timestamp = now - (hours - i) * 3600 * 1000;
        const hourOfDay = new Date(timestamp).getHours();
        
        // Simulate gas patterns - higher during US business hours
        const hourFactor = (hourOfDay >= 12 && hourOfDay <= 20) ? 1.2 : 0.8;
        const randomFactor = 1 + (Math.random() * volatility * 2 - volatility);
        
        data.push({
          timestamp,
          price: baseGwei * hourFactor * randomFactor
        });
      }
      
      return data;
    };
    
    // Generate gas price forecast for next 24h
    const generateGasForecast = (baseGwei: number, historicalData: any[]) => {
      const hours = 24;
      const forecast = [];
      const now = new Date();
      
      for (let i = 0; i < hours; i++) {
        const forecastHour = (now.getHours() + i) % 24;
        const hourFactor = (forecastHour >= 12 && forecastHour <= 20) ? 1.2 : 0.8;
        const randomVariation = Math.random() * 0.3 - 0.15;
        
        forecast.push({
          hour: forecastHour,
          price: baseGwei * hourFactor * (1 + randomVariation),
          confidence: 0.9 - (i * 0.02) // Confidence decreases further into future
        });
      }
      
      return forecast;
    };
    
    // Generate recommendations based on forecast
    const generateRecommendations = (forecast: any[]) => {
      // Find lowest gas times in next 24h
      const sortedForecast = [...forecast].sort((a, b) => a.price - b.price);
      
      const slowIndex = 0; // Cheapest
      const standardIndex = Math.floor(sortedForecast.length * 0.3); // 30th percentile
      const fastIndex = Math.floor(sortedForecast.length * 0.7); // 70th percentile
      
      const formatTime = (hour: number) => {
        const now = new Date();
        const forecastDate = new Date(now);
        forecastDate.setHours(hour, 0, 0, 0);
        if (hour < now.getHours()) {
          forecastDate.setDate(forecastDate.getDate() + 1); // Next day
        }
        return forecastDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
      };
      
      return {
        fast: {
          time: formatTime(sortedForecast[fastIndex].hour),
          price: sortedForecast[fastIndex].price
        },
        standard: {
          time: formatTime(sortedForecast[standardIndex].hour),
          price: sortedForecast[standardIndex].price
        },
        slow: {
          time: formatTime(sortedForecast[slowIndex].hour),
          price: sortedForecast[slowIndex].price
        }
      };
    };
    
    // Generate network-specific gas data
    const gasData = networks.map(network => {
      // Different networks have different base gas costs and volatility
      let baseGwei, volatility, baseFee, priorityFee;
      
      switch (network.id) {
        case "ethereum":
          baseGwei = 25 + Math.random() * 15;
          volatility = 0.4;
          baseFee = baseGwei * 0.8;
          priorityFee = baseGwei * 0.2;
          break;
        case "polygon":
          baseGwei = 100 + Math.random() * 50;
          volatility = 0.3;
          baseFee = baseGwei * 0.85;
          priorityFee = baseGwei * 0.15;
          break;
        case "bsc":
          baseGwei = 5 + Math.random() * 3;
          volatility = 0.2;
          baseFee = baseGwei * 0.9;
          priorityFee = baseGwei * 0.1;
          break;
        case "arbitrum":
          baseGwei = 0.3 + Math.random() * 0.2;
          volatility = 0.25;
          baseFee = baseGwei * 0.85;
          priorityFee = baseGwei * 0.15;
          break;
        case "optimism":
          baseGwei = 0.2 + Math.random() * 0.1;
          volatility = 0.2;
          baseFee = baseGwei * 0.9;
          priorityFee = baseGwei * 0.1;
          break;
        default:
          baseGwei = 10 + Math.random() * 10;
          volatility = 0.3;
          baseFee = baseGwei * 0.8;
          priorityFee = baseGwei * 0.2;
      }
      
      const currentGwei = baseGwei * (1 + (Math.random() * volatility * 2 - volatility));
      const historicalData = generateHistoricalGas(baseGwei, volatility);
      const forecast = generateGasForecast(baseGwei, historicalData);
      const recommendations = generateRecommendations(forecast);
      
      // Different estimated times based on gas price
      const estimatedTimes = {
        fast: 1 + Math.floor(Math.random() * 2),
        standard: 3 + Math.floor(Math.random() * 5),
        slow: 10 + Math.floor(Math.random() * 15)
      };
      
      return {
        networkId: network.id,
        networkName: network.name,
        currentGwei: Math.round(currentGwei * 100) / 100,
        baseFee: Math.round(baseFee * 100) / 100,
        priorityFee: Math.round(priorityFee * 100) / 100,
        estimatedTimes,
        historicalData,
        forecast,
        recommendations
      };
    });
    
    cacheResponse("gas_data", gasData);
    return gasData;
    
  } catch (error) {
    return handleApiError(error, {
      key: "gas_data",
      data: []
    });
  }
};
