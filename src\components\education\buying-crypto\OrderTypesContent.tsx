
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le, CardDescription, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ArrowRight } from "lucide-react";

interface OrderTypesContentProps {
  onContinue: () => void;
}

export default function OrderTypesContent({ onContinue }: OrderTypesContentProps) {
  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-semibold mb-4">Understanding Different Order Types</h2>
      <p className="mb-4">Order types allow you to specify exactly how you want to buy or sell cryptocurrencies. Using the right order type can help you execute trades at your preferred price and protect you from market volatility.</p>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <div className="rounded-lg overflow-hidden h-64">
          <img 
            src="https://images.unsplash.com/photo-1642104704074-907c0698cbd9?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80" 
            alt="Cryptocurrency trading chart with various order types visualization" 
            className="w-full h-full object-cover"
          />
        </div>
        
        <div>
          <p className="mb-4">While DEXs typically offer only basic market and limit orders, centralized exchanges provide a wider variety of order options for more strategic trading.</p>
          <p>Understanding these order types can help you:</p>
          <ul className="list-disc pl-6 space-y-2 mt-2">
            <li>Get better execution prices</li>
            <li>Automate your trading strategy</li>
            <li>Protect yourself from unexpected market movements</li>
            <li>Reduce trading fees in some cases</li>
            <li>Take emotion out of trading decisions</li>
          </ul>
        </div>
      </div>

      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Market Order</CardTitle>
            <CardDescription>Buy or sell immediately at the best available current price</CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <div>
              <h4 className="font-medium text-sm">How it works:</h4>
              <p className="text-sm text-muted-foreground">Executes instantly at the current market price, guaranteeing execution but not price.</p>
            </div>
            <div>
              <h4 className="font-medium text-sm">Best used when:</h4>
              <p className="text-sm text-muted-foreground">You want immediate execution and price isn't your primary concern.</p>
            </div>
            <div>
              <h4 className="font-medium text-sm">Example:</h4>
              <p className="text-sm text-muted-foreground">"I want to buy 0.1 BTC right now at whatever the current price is."</p>
            </div>
            <div className="flex items-center">
              <h4 className="font-medium text-sm mr-2">Available on:</h4>
              <Badge className="mr-1">CEX</Badge>
              <Badge variant="outline">DEX</Badge>
            </div>
            <div>
              <h4 className="font-medium text-sm">Fee impact:</h4>
              <p className="text-sm text-muted-foreground">Usually incurs "taker" fees, which are typically higher than "maker" fees.</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Limit Order</CardTitle>
            <CardDescription>Set a specific price at which you want to buy or sell</CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <div>
              <h4 className="font-medium text-sm">How it works:</h4>
              <p className="text-sm text-muted-foreground">Your order will only execute if the market reaches your specified price or better.</p>
            </div>
            <div>
              <h4 className="font-medium text-sm">Best used when:</h4>
              <p className="text-sm text-muted-foreground">You have a specific price target and are willing to wait for it to be reached.</p>
            </div>
            <div>
              <h4 className="font-medium text-sm">Example:</h4>
              <p className="text-sm text-muted-foreground">"I want to buy 0.1 BTC, but only if the price drops to $40,000 or lower."</p>
            </div>
            <div className="flex items-center">
              <h4 className="font-medium text-sm mr-2">Available on:</h4>
              <Badge className="mr-1">CEX</Badge>
              <Badge variant="outline">DEX</Badge>
            </div>
            <div>
              <h4 className="font-medium text-sm">Fee impact:</h4>
              <p className="text-sm text-muted-foreground">Usually qualifies for lower "maker" fees as your order adds liquidity to the order book.</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Stop-Loss Order</CardTitle>
            <CardDescription>Create an automatic market sell order if price falls below your set level</CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <div>
              <h4 className="font-medium text-sm">How it works:</h4>
              <p className="text-sm text-muted-foreground">When the market price hits your trigger price, a market sell order is automatically created.</p>
            </div>
            <div>
              <h4 className="font-medium text-sm">Best used when:</h4>
              <p className="text-sm text-muted-foreground">You want to limit potential losses or protect profits on a position.</p>
            </div>
            <div>
              <h4 className="font-medium text-sm">Example:</h4>
              <p className="text-sm text-muted-foreground">"If BTC drops to $38,000, sell my holdings to prevent further losses."</p>
            </div>
            <div className="flex items-center">
              <h4 className="font-medium text-sm mr-2">Available on:</h4>
              <Badge>CEX</Badge>
            </div>
            <div>
              <h4 className="font-medium text-sm">Fee impact:</h4>
              <p className="text-sm text-muted-foreground">Typically incurs taker fees as it executes as a market order when triggered.</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Stop-Limit Order</CardTitle>
            <CardDescription>Combines features of stop-loss and limit orders</CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <div>
              <h4 className="font-medium text-sm">How it works:</h4>
              <p className="text-sm text-muted-foreground">When the market hits your stop price, a limit order is placed at your specified limit price.</p>
            </div>
            <div>
              <h4 className="font-medium text-sm">Best used when:</h4>
              <p className="text-sm text-muted-foreground">You want to limit losses but also control the execution price of the stop order.</p>
            </div>
            <div>
              <h4 className="font-medium text-sm">Example:</h4>
              <p className="text-sm text-muted-foreground">"If BTC drops to $38,000, place a limit sell order at $37,800."</p>
            </div>
            <div className="flex items-center">
              <h4 className="font-medium text-sm mr-2">Available on:</h4>
              <Badge>CEX</Badge>
            </div>
            <div>
              <h4 className="font-medium text-sm">Fee impact:</h4>
              <p className="text-sm text-muted-foreground">May qualify for maker fees if the limit order doesn't execute immediately after being placed.</p>
            </div>
          </CardContent>
        </Card>
      </div>

      <Button variant="outline" className="mt-8" onClick={onContinue}>
        Try the Fee Calculator <ArrowRight className="ml-2 h-4 w-4" />
      </Button>
    </div>
  );
}
