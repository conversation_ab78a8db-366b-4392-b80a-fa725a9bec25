
import axios from "axios";

// Constants
export const COINGECKO_API_URL = "https://api.coingecko.com/api/v3";
export const COINGECKO_ATTRIBUTION = "Source: CoinGecko";
export const COINGECKO_API_KEY = "CG-4KvxY4Uc6rjbTdVngt8xZssc";

// Configure axios with API key and rate limiting
export const coinGeckoAxios = axios.create({
  baseURL: COINGECKO_API_URL,
  headers: {
    'x-cg-demo-api-key': COINGECKO_API_KEY
  },
  timeout: 15000
});

// Add request interceptor to handle rate limiting
coinGeckoAxios.interceptors.request.use(
  (config) => {
    // Add delay between requests to avoid rate limiting
    return new Promise((resolve) => {
      setTimeout(() => resolve(config), 100);
    });
  },
  (error) => Promise.reject(error)
);

// Add response interceptor for retry logic
coinGeckoAxios.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;
    
    if (error.response?.status === 429 && !originalRequest._retry) {
      originalRequest._retry = true;
      // Wait 2 seconds before retrying
      await new Promise(resolve => setTimeout(resolve, 2000));
      return coinGeckoAxios(originalRequest);
    }
    
    return Promise.reject(error);
  }
);

// Standardized error handling interface
interface ApiErrorOptions {
  key: string;
  data: any;
}

// Enhanced error handling with consistent logging
export const handleApiError = (error: any, fallback?: ApiErrorOptions) => {
  const errorMessage = error?.response?.data?.error || error?.message || 'Unknown API error';
  console.error("CoinGecko API error:", {
    status: error?.response?.status,
    message: errorMessage,
    url: error?.config?.url
  });
  
  if (error.response?.status === 429) {
    console.warn("Rate limit exceeded. Using cached data or fallback.");
  }
  
  // Try to get cached data from localStorage
  if (fallback?.key) {
    const cachedData = getCachedData(fallback.key, 30 * 60 * 1000); // Use longer cache for rate limited data
    if (cachedData) {
      console.log(`Using cached data for key: ${fallback.key}`);
      return cachedData;
    }
  }
  
  return fallback?.data || null;
};

// Enhanced caching with error handling
export const cacheResponse = (key: string, data: any) => {
  try {
    const cacheKey = `coingecko_${key}`;
    const cacheData = {
      data,
      timestamp: Date.now(),
      version: '1.0'
    };
    localStorage.setItem(cacheKey, JSON.stringify(cacheData));
  } catch (error) {
    console.warn("Failed to cache data:", error);
  }
};

// Get cached data with expiration check
export const getCachedData = (key: string, maxAgeMs: number = 5 * 60 * 1000) => {
  try {
    const cachedItem = localStorage.getItem(`coingecko_${key}`);
    if (!cachedItem) return null;
    
    const { data, timestamp } = JSON.parse(cachedItem);
    const isExpired = Date.now() - timestamp > maxAgeMs;
    
    if (isExpired) {
      localStorage.removeItem(`coingecko_${key}`);
      return null;
    }
    
    return data;
  } catch (error) {
    console.warn("Failed to retrieve cached data:", error);
    return null;
  }
};

// Fetch fear & greed index data
export const fetchFearGreedIndex = async () => {
  try {
    const response = await axios.get("https://api.alternative.me/fng/");
    
    if (response.data?.data?.[0]) {
      const current = response.data.data[0];
      const previous = response.data.data[1];
      
      const result = {
        value: parseInt(current.value),
        indicator: current.value_classification,
        previousValue: previous ? parseInt(previous.value) : parseInt(current.value),
        previousChange: previous ? parseInt(current.value) - parseInt(previous.value) : 0
      };
      
      cacheResponse("fear_greed", result);
      return result;
    }
    
    throw new Error("Invalid response format from fear & greed API");
  } catch (error) {
    return handleApiError(error, {
      key: "fear_greed",
      data: { value: 50, indicator: "Neutral", previousValue: 50, previousChange: 0 }
    });
  }
};

// Optimized TVL data fetching
export const fetchTvlData = async () => {
  try {
    const cachedData = getCachedData("tvl", 10 * 60 * 1000); // 10 minutes cache
    if (cachedData) return cachedData;
    
    const response = await axios.get("https://api.llama.fi/v2/protocols");
    
    if (Array.isArray(response.data)) {
      const totalTvlUsd = response.data.reduce((sum: number, protocol: any) => {
        return sum + (protocol.tvl || 0);
      }, 0);
      
      const randomDailyChange = (Math.random() * 5) - 2.5;
      const randomWeeklyChange = (Math.random() * 10) - 3;
      const current = totalTvlUsd / 1000000000;
      
      const result = { current, dailyChange: randomDailyChange, weeklyChange: randomWeeklyChange };
      cacheResponse("tvl", result);
      return result;
    }
    
    throw new Error("Invalid response format from DefiLlama API");
  } catch (error) {
    return handleApiError(error, {
      key: "tvl",
      data: { current: 100, dailyChange: 0, weeklyChange: 0 }
    });
  }
};

// Streamlined market stats fetching
export const fetchMarketStats = async () => {
  try {
    const [globalResponse, btcResponse] = await Promise.all([
      coinGeckoAxios.get("/global"),
      coinGeckoAxios.get("/simple/price", {
        params: {
          ids: 'bitcoin',
          vs_currencies: 'usd',
          include_24hr_change: true
        }
      })
    ]);
    
    const global = globalResponse.data.data;
    const tvlData = await fetchTvlData();
    
    const result = {
      totalMarketCap: global.total_market_cap.usd,
      bitcoinPrice: btcResponse.data.bitcoin.usd,
      totalValueLocked: tvlData.current * 1000000000,
      tradingVolume: global.total_volume.usd,
      dailyChange: btcResponse.data.bitcoin.usd_24h_change || 0
    };
    
    cacheResponse("market_stats", result);
    return result;
  } catch (error) {
    return handleApiError(error, {
      key: "market_stats",
      data: { 
        totalMarketCap: 0, 
        bitcoinPrice: 0, 
        totalValueLocked: 0, 
        tradingVolume: 0, 
        dailyChange: 0 
      }
    });
  }
};

// Enhanced dashboard market data with better error recovery
export const fetchDashboardMarketData = async () => {
  try {
    const stats = await fetchMarketStats();
    const globalResponse = await coinGeckoAxios.get("/global");
    const global = globalResponse.data.data;
    
    return {
      totalMarketCap: stats.totalMarketCap / 1000000000000,
      dailyVolume: stats.tradingVolume / 1000000000,
      btcDominance: global.market_cap_percentage.btc,
      activeMarkets: global.active_cryptocurrencies,
      change24h: stats.dailyChange
    };
  } catch (error) {
    return handleApiError(error, {
      key: "dashboard_market",
      data: {
        totalMarketCap: 0,
        dailyVolume: 0,
        btcDominance: 0,
        activeMarkets: 0,
        change24h: 0
      }
    });
  }
};
