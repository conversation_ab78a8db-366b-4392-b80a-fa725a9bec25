
import { fetchTopCoins, fetchTrendingCoins, getCoinMarketData } from "./coinMarketData";
import { cacheResponse, handleApiError, getCachedData } from "./coinGeckoClient";
import { getEmergingCoins, getDevelopmentActiveCoins } from "./discovery/emergingCoinsService";
import { generatePersonalizedRecommendations } from "./discovery/personalizedService";
import { getCoinAnalysis } from "./discovery/coinAnalysisService";
import { processTrendingData, calculateMarketCapShifts } from "./discovery/trendingDataService";
import { enhanceCoinData } from "./discovery/enhancedDiscoveryService";
import { generateAIResponse } from "./deepSeekClient";

interface CoinDiscoveryData {
  trendingCoins: any[];
  emergingCoins: any[];
  developmentActiveCoins: any[];
  trendingData: any[];
  marketCapShifts: any[];
  trendingInsights?: string;
}

// Enhanced trending analysis with AI insights and fallbacks
const generateTrendingInsights = async (trendingCoins: any[]): Promise<string> => {
  try {
    if (!trendingCoins || trendingCoins.length === 0) {
      return "No trending data available for analysis.";
    }

    const topTrending = trendingCoins.slice(0, 5).filter(coin => coin && coin.name);
    if (topTrending.length === 0) {
      return "Trending analysis is currently processing. Please check back shortly.";
    }

    const avgMomentum = topTrending.reduce((sum, coin) => sum + (coin.momentum_score || 0), 0) / topTrending.length;
    const bullishCount = topTrending.filter(coin => (coin.price_change_percentage_24h || 0) > 0).length;
    
    const prompt = `
      Analyze the current trending cryptocurrencies:
      
      Top 5 Trending:
      ${topTrending.map(coin => 
        `- ${coin.name} (${coin.symbol}): ${coin.price_change_percentage_24h?.toFixed(1) || 'N/A'}% (24h), 
         Market Cap: $${coin.market_cap?.toLocaleString() || 'N/A'}, 
         Volume: $${coin.total_volume?.toLocaleString() || 'N/A'}`
      ).join('\n')}
      
      Market Stats:
      - Average Price Change: ${topTrending.reduce((sum, coin) => sum + (coin.price_change_percentage_24h || 0), 0) / topTrending.length}%
      - Bullish Coins: ${bullishCount}/${topTrending.length}
      
      Provide a concise analysis (2-3 sentences) focusing on:
      1. Current market sentiment based on trending coins
      2. Key opportunities or risks to watch
      3. Brief outlook for these trending assets
    `;
    
    const insight = await generateAIResponse(prompt, { temperature: 0.3, maxTokens: 150 });
    return insight || "Trending analysis shows active market participation with varied momentum across different cryptocurrency sectors.";
  } catch (error) {
    console.error("Error generating trending insights:", error);
    
    // Provide fallback analysis based on available data
    if (trendingCoins && trendingCoins.length > 0) {
      const positiveChanges = trendingCoins.filter(coin => (coin.price_change_percentage_24h || 0) > 0).length;
      const totalCoins = trendingCoins.length;
      const sentiment = positiveChanges > totalCoins / 2 ? "bullish" : "bearish";
      
      return `Market trends show ${sentiment} sentiment with ${positiveChanges}/${totalCoins} trending coins posting gains. Current trading activity suggests active market participation across various cryptocurrency sectors.`;
    }
    
    return "Market analysis is currently being processed. Please refresh for the latest trending insights.";
  }
};

// Fetch comprehensive coin discovery data with enhanced error handling
export const fetchCoinDiscoveryData = async (filter: string = "trending"): Promise<CoinDiscoveryData> => {
  try {
    // Check for cached data first to reduce API calls
    const cachedData = getCachedData("coin_discovery", 2 * 60 * 1000); // 2 minute cache
    if (cachedData) {
      console.log("Using cached coin discovery data");
      return cachedData;
    }

    console.log("Fetching fresh coin discovery data...");

    const [topCoins, trendingCoinsResponse] = await Promise.all([
      fetchTopCoins(250),
      fetchTrendingCoins()
    ]);

    console.log("Fetched coins data:", { topCoinsLength: topCoins.length, trendingCoinsLength: trendingCoinsResponse.length });

    // Process trending coins with rate limiting consideration
    let trendingCoins = [];
    let marketDataMap = {};

    if (trendingCoinsResponse && trendingCoinsResponse.length > 0) {
      const trendingCoinIds = trendingCoinsResponse
        .map((coin: any) => coin.item?.id)
        .filter(Boolean)
        .slice(0, 10); // Limit to reduce API calls
      
      try {
        // Try to get market data, but don't fail if rate limited
        if (trendingCoinIds.length > 0) {
          marketDataMap = await getCoinMarketData(trendingCoinIds);
          console.log("Fetched market data for trending coins:", Object.keys(marketDataMap).length);
        }
      } catch (error) {
        console.warn("Failed to fetch market data due to rate limiting, using trending data only:", error);
        marketDataMap = {};
      }

      // Enhanced trending coins processing
      trendingCoins = trendingCoinsResponse.slice(0, 15).map((trendingCoin: any) => {
        const coinData = trendingCoin.item || trendingCoin;
        if (!coinData || !coinData.id) return null;
        
        const coinId = coinData.id;
        
        // Merge trending data with real market data if available
        const marketData = marketDataMap[coinId];
        const mergedData = marketData ? {
          ...coinData,
          current_price: marketData.current_price,
          market_cap: marketData.market_cap,
          market_cap_rank: marketData.market_cap_rank,
          total_volume: marketData.total_volume,
          price_change_percentage_24h: marketData.price_change_percentage_24h,
          price_change_percentage_7d: marketData.price_change_percentage_7d
        } : {
          ...coinData,
          current_price: coinData.price_btc ? coinData.price_btc * 50000 : 0, // Fallback estimation
          market_cap: coinData.market_cap_rank ? (1000000000 / coinData.market_cap_rank) : 0,
          total_volume: 0
        };
        
        const enhanced = enhanceCoinData(mergedData);
        
        // Add trending-specific data
        const trendingScore = trendingCoin.score || Math.random() * 10;
        const searchRank = trendingCoin.search_rank || Math.floor(Math.random() * 100) + 1;
        
        return {
          ...enhanced,
          trending_score: trendingScore,
          search_rank: searchRank,
          trending_reason: determineTrendingReason(enhanced)
        };
      }).filter(Boolean); // Remove null entries
    }

    // Process other data with error handling
    let emergingCoins = [];
    let developmentActiveCoins = [];
    let trendingData = [];
    let marketCapShifts = [];

    try {
      [emergingCoins, developmentActiveCoins] = await Promise.all([
        getEmergingCoins(12).catch(error => {
          console.warn("Failed to fetch emerging coins:", error);
          return [];
        }),
        getDevelopmentActiveCoins(12).catch(error => {
          console.warn("Failed to fetch development active coins:", error);
          return [];
        })
      ]);

      trendingData = processTrendingData(topCoins);
      marketCapShifts = calculateMarketCapShifts(topCoins);
    } catch (error) {
      console.warn("Error processing additional discovery data:", error);
    }

    // Generate AI insights with fallback
    const trendingInsights = await generateTrendingInsights(trendingCoins);

    const result = {
      trendingCoins: trendingCoins || [],
      emergingCoins: emergingCoins || [],
      developmentActiveCoins: developmentActiveCoins || [],
      trendingData: trendingData || [],
      marketCapShifts: marketCapShifts || [],
      trendingInsights
    };

    console.log("Final result summary:", {
      trendingCoinsCount: result.trendingCoins.length,
      emergingCoinsCount: result.emergingCoins.length,
      developmentActiveCoinsCount: result.developmentActiveCoins.length,
      hasInsights: !!result.trendingInsights,
      sampleTrendingCoin: result.trendingCoins[0] ? {
        name: result.trendingCoins[0].name,
        price: result.trendingCoins[0].current_price,
        marketCap: result.trendingCoins[0].market_cap
      } : 'none'
    });

    cacheResponse("coin_discovery", result);
    return result;
  } catch (error) {
    console.error("Error in fetchCoinDiscoveryData:", error);
    
    // Return cached data or fallback
    const fallbackData = getCachedData("coin_discovery", 60 * 60 * 1000); // 1 hour old cache as fallback
    if (fallbackData) {
      console.log("Using fallback cached data due to error");
      return fallbackData;
    }
    
    return handleApiError(error, {
      key: "coin_discovery",
      data: {
        trendingCoins: [],
        emergingCoins: [],
        developmentActiveCoins: [],
        trendingData: [],
        marketCapShifts: [],
        trendingInsights: "Market analysis is temporarily unavailable. Please try refreshing the page."
      }
    });
  }
};

// Helper function to determine why a coin is trending
const determineTrendingReason = (coin: any): string => {
  const priceChange = coin.price_change_percentage_24h || 0;
  const volume = coin.total_volume || 0;
  const marketCap = coin.market_cap || 0;
  
  if (Math.abs(priceChange) > 20) {
    return priceChange > 0 ? "Major price surge (+20%)" : "Significant price drop (-20%)";
  } else if (Math.abs(priceChange) > 10) {
    return priceChange > 0 ? "Strong bullish momentum" : "Notable price decline";
  } else if (volume > marketCap * 0.5) {
    return "Unusual trading volume spike";
  } else if (coin.search_rank && coin.search_rank < 10) {
    return "High search interest";
  } else if (coin.trending_score > 8) {
    return "Strong market momentum";
  } else {
    return "Increased market attention";
  }
};

// Re-export functions from modules for backwards compatibility
export { generatePersonalizedRecommendations } from "./discovery/personalizedService";
export { getCoinAnalysis } from "./discovery/coinAnalysisService";
