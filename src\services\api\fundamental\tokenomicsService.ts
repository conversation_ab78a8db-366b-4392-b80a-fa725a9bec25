
import axios from "axios";
import { coinGeckoAxios, handleApiError, cacheResponse } from "../coinGeckoClient";

// Get detailed tokenomics data for a specific coin
export const fetchTokenomicsData = async (coinId: string) => {
  try {
    const response = await coinGeckoAxios.get(`/coins/${coinId}`, {
      params: {
        localization: false,
        tickers: false,
        market_data: true,
        community_data: false,
        developer_data: false,
      }
    });
    
    const { market_data, detail_platforms } = response.data;
    
    // Extract tokenomics information
    const tokenomics = {
      circulating_supply: market_data.circulating_supply,
      total_supply: market_data.total_supply || market_data.circulating_supply,
      max_supply: market_data.max_supply,
      supply_percentage: market_data.total_supply ? 
        (market_data.circulating_supply / market_data.total_supply) * 100 : 100,
      market_cap: market_data.market_cap.usd,
      fully_diluted_valuation: market_data.fully_diluted_valuation?.usd,
      platforms: detail_platforms
    };
    
    cacheResponse(`tokenomics_${coinId}`, tokenomics);
    return tokenomics;
  } catch (error) {
    return handleApiError(error, {
      key: `tokenomics_${coinId}`,
      data: null
    });
  }
};
