
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { TechnicalAnalysisChart } from "./analysis/TechnicalAnalysisChart";
import { FundamentalAnalysisChart } from "./analysis/FundamentalAnalysisChart";
import { PricePredictionChart } from "./analysis/PricePredictionChart";
import { useQuery } from "@tanstack/react-query";
import { generateCoinAnalysis } from "@/services/api/analysis/coinAnalysisService";
import { CoinData, PriceHistoryPoint, FundamentalMetrics, PriceTargets } from "./analysis/types";

interface CoinAnalysisModalProps {
  coin: CoinData | null;
  isOpen: boolean;
  onClose: () => void;
}

export function CoinAnalysisModal({ coin, isOpen, onClose }: CoinAnalysisModalProps) {
  const { data: analysisData, isLoading } = useQuery({
    queryKey: ['coinAnalysis', coin?.id],
    queryFn: () => coin ? generateCoinAnalysis(coin) : Promise.resolve(null),
    enabled: !!coin && isOpen,
  });

  if (!coin) return null;

  // Generate mock data for charts based on coin data
  const priceHistory: PriceHistoryPoint[] = Array.from({ length: 30 }, (_, i) => {
    const basePrice = coin.current_price;
    const randomVariation = (Math.random() - 0.5) * 0.1;
    const trendFactor = (coin.price_change_percentage_24h || 0) / 100;

    return {
      date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      price: basePrice * (1 + (randomVariation + trendFactor * (i / 30))),
      volume: (coin.total_volume || 1000000) * (0.8 + Math.random() * 0.4),
      rsi: 30 + Math.random() * 40,
      macd: (Math.random() - 0.5) * 0.02,
      sma20: basePrice * (1 + trendFactor * 0.5),
      sma50: basePrice * (1 + trendFactor * 0.3)
    };
  });

  const fundamentalMetrics: FundamentalMetrics = {
    tokenomics: { score: 70 + Math.random() * 20 },
    development: { score: 60 + Math.random() * 30, stars: '2.1k' },
    adoption: { score: 50 + Math.random() * 40 },
    community: { score: 65 + Math.random() * 25 }
  };

  const priceTargets: PriceTargets = {
    conservative: coin.current_price * (1 + (coin.price_change_percentage_24h || 0) / 100 * 0.5),
    optimistic: coin.current_price * (1 + (coin.price_change_percentage_24h || 0) / 100 * 2),
    bearish: coin.current_price * (1 - Math.abs(coin.price_change_percentage_24h || 0) / 100 * 0.8)
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-7xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              {coin.image && (
                <img src={coin.image} alt={coin.name} className="w-8 h-8 rounded-full" />
              )}
              <span>{coin.name} ({coin.symbol?.toUpperCase()})</span>
            </div>
            <Badge variant={coin.opportunity_score > 7 ? 'default' : 'secondary'}>
              Opportunity Score: {coin.opportunity_score.toFixed(1)}
            </Badge>
          </DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="technical" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="technical">Technical Analysis</TabsTrigger>
            <TabsTrigger value="fundamental">Fundamental Analysis</TabsTrigger>
            <TabsTrigger value="prediction">Price Prediction</TabsTrigger>
          </TabsList>

          <TabsContent value="technical" className="mt-6">
            <TechnicalAnalysisChart
              coinData={coin}
              priceHistory={priceHistory}
              isLoading={isLoading}
            />
          </TabsContent>

          <TabsContent value="fundamental" className="mt-6">
            <FundamentalAnalysisChart
              coinData={coin}
              fundamentalMetrics={fundamentalMetrics}
              isLoading={isLoading}
            />
          </TabsContent>

          <TabsContent value="prediction" className="mt-6">
            <PricePredictionChart
              coinData={coin}
              priceTargets={priceTargets}
              isLoading={isLoading}
            />
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
