
import {
  BarChart3,
  LineChart,
  Wallet,
  Newspaper,
  BookOpen,
  BrainCircuit,
  GanttChartSquare,
  BadgeDollarSign,
  AlertCircle,
  Award,
  Lightbulb,
  AreaChart,
  Users,
  Settings,
  Network,
  Compass,
  Shield,
  Search,
} from "lucide-react";
import { ReactNode } from "react";

export interface NavigationLink {
  title: string;
  href: string;
  icon: ReactNode;
  isNew?: boolean;
  isUpdated?: boolean;
}

export const marketLinks: NavigationLink[] = [
  {
    title: "Market Overview",
    href: "/",
    icon: <BarChart3 className="h-5 w-5" />,
  },
  {
    title: "Market Insights",
    href: "/market-insights",
    icon: <LineChart className="h-5 w-5" />,
  },
  {
    title: "Coin Discovery",
    href: "/coin-discovery",
    icon: <Search className="h-5 w-5" />,
    isNew: true,
  },
  {
    title: "Portfolio",
    href: "/portfolio",
    icon: <Wallet className="h-5 w-5" />,
  },
  {
    title: "News & Sentiment",
    href: "/news-sentiment",
    icon: <Newspaper className="h-5 w-5" />,
  },
  {
    title: "Education",
    href: "/education",
    icon: <BookOpen className="h-5 w-5" />,
  },
];

export const advancedAnalysisLinks: NavigationLink[] = [
  {
    title: "AI Insights",
    href: "/ai-insights",
    icon: <BrainCircuit className="h-5 w-5" />,
    isNew: true,
  },
  {
    title: "Scam Detector",
    href: "/token-scam-detector",
    icon: <Shield className="h-5 w-5" />,
    isNew: true,
  },
  {
    title: "DeFi Opportunities",
    href: "/defi-opportunities",
    icon: <BadgeDollarSign className="h-5 w-5" />,
  },
  {
    title: "On-Chain Analytics",
    href: "/on-chain-analytics",
    icon: <Network className="h-5 w-5" />,
  },
  {
    title: "Smart Money",
    href: "/smart-money",
    icon: <Compass className="h-5 w-5" />,
  },
  {
    title: "Fundamental Analysis",
    href: "/fundamental-analysis",
    icon: <GanttChartSquare className="h-5 w-5" />,
  },
  {
    title: "Anomaly Detection",
    href: "/anomalies",
    icon: <AlertCircle className="h-5 w-5" />,
  },
  {
    title: "Token Ratings",
    href: "/ratings",
    icon: <Award className="h-5 w-5" />,
  },
  {
    title: "Price Forecasting",
    href: "/forecasting",
    icon: <Lightbulb className="h-5 w-5" />,
    isUpdated: true,
  },
  {
    title: "Advanced Visualizations",
    href: "/advanced-visualizations",
    icon: <AreaChart className="h-5 w-5" />,
  },
];

export const otherLinks: NavigationLink[] = [
  {
    title: "Profile Settings",
    href: "/profile-settings",
    icon: <Settings className="h-5 w-5" />,
  },
];
