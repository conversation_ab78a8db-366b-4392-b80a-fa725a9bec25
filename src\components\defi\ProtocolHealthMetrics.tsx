
import { useMemo, useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from "@/components/ui/card";
import { useDefiData } from "@/hooks/useDefiData";

// Import new refactored components
import ProtocolSelector from "./protocol/ProtocolSelector";
import TVLMetricsCard from "./protocol/TVLMetricsCard";
import UserGrowthCard from "./protocol/UserGrowthCard";
import SecurityRatingCard from "./protocol/SecurityRatingCard";
import SecurityAuditTabs from "./protocol/SecurityAuditTabs";
import { formatCurrency, processSecurityPieData } from "./protocol/formatUtils";

export default function ProtocolHealthMetrics({ isLoading }: { isLoading: boolean }) {
  const { protocolHealth } = useDefiData();
  const [selectedProtocol, setSelectedProtocol] = useState<string | null>(null);

  // Select default protocol or the user-selected one
  const currentProtocol = useMemo(() => {
    if (!protocolHealth || protocolHealth.length === 0) return null;
    if (!selectedProtocol) return protocolHealth[0];
    return protocolHealth.find(p => p.id === selectedProtocol) || protocolHealth[0];
  }, [protocolHealth, selectedProtocol]);

  // Data for security audit overview
  const securityPieData = useMemo(() =>
    processSecurityPieData(currentProtocol),
  [currentProtocol]);

  return (
    <div className="space-y-6">
      {/* Protocol selector */}
      <ProtocolSelector
        protocols={protocolHealth}
        selectedProtocol={selectedProtocol}
        onSelectProtocol={setSelectedProtocol}
        isLoading={isLoading}
      />

      {/* Main metrics display */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* TVL and Trend */}
        <TVLMetricsCard
          protocol={currentProtocol}
          isLoading={isLoading}
          formatCurrency={formatCurrency}
        />

        {/* User Metrics */}
        <UserGrowthCard
          protocol={currentProtocol}
          isLoading={isLoading}
        />

        {/* Security Metrics */}
        <SecurityRatingCard
          protocol={currentProtocol}
          isLoading={isLoading}
        />
      </div>

      {/* Security Audits Detail */}
      <Card>
        <CardHeader>
          <CardTitle>Security Audits & Incidents</CardTitle>
          <CardDescription>
            Security audit history, findings, and past incidents for {currentProtocol?.name || 'the protocol'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading || !currentProtocol ? (
            <div className="space-y-4">
              <div className="h-10 w-full bg-secondary animate-pulse rounded-md"></div>
              <div className="h-64 w-full bg-secondary animate-pulse rounded-md"></div>
            </div>
          ) : (
            <SecurityAuditTabs
              protocol={currentProtocol}
              isLoading={isLoading}
              securityPieData={securityPieData}
              formatCurrency={formatCurrency}
            />
          )}
        </CardContent>
      </Card>
    </div>
  );
}
