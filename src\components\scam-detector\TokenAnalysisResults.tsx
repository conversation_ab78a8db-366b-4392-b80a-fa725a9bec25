
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { AlertTriangle, CheckCircle, XCircle, Info } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface TokenAnalysisData {
  tokenName: string;
  symbol: string;
  contractAddress: string;
  redFlags: number;
  liquidityLocked: boolean;
  auditScore: number;
  communityScore: number;
  riskFactors: string[];
  warnings: string[];
  recommendations: string[];
}

interface TokenAnalysisResultsProps {
  analysis: TokenAnalysisData;
  isLoading: boolean;
}

export function TokenAnalysisResults({ analysis, isLoading }: TokenAnalysisResultsProps) {
  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="animate-pulse">
          <div className="h-32 bg-muted rounded-lg"></div>
        </div>
      </div>
    );
  }

  if (!analysis) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <p className="text-muted-foreground">No analysis data available</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Token Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <span>{analysis.tokenName}</span>
              <Badge variant="outline">{analysis.symbol}</Badge>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium">Contract Address</label>
              <p className="text-sm text-muted-foreground font-mono bg-muted p-2 rounded mt-1">
                {analysis.contractAddress}
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="text-sm font-medium">Audit Score</label>
                <div className="mt-2">
                  <Progress value={analysis.auditScore} className="h-2" />
                  <span className="text-sm text-muted-foreground">{analysis.auditScore}/100</span>
                </div>
              </div>

              <div>
                <label className="text-sm font-medium">Community Trust</label>
                <div className="mt-2">
                  <Progress value={analysis.communityScore} className="h-2" />
                  <span className="text-sm text-muted-foreground">{analysis.communityScore}/100</span>
                </div>
              </div>

              <div>
                <label className="text-sm font-medium">Liquidity Status</label>
                <div className="mt-2 flex items-center gap-2">
                  {analysis.liquidityLocked ? (
                    <>
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span className="text-sm text-green-600">Locked</span>
                    </>
                  ) : (
                    <>
                      <XCircle className="h-4 w-4 text-red-500" />
                      <span className="text-sm text-red-600">Not Locked</span>
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Risk Factors */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-orange-500" />
            Risk Factors
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {analysis.riskFactors.map((factor, index) => (
              <Alert key={index}>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>{factor}</AlertDescription>
              </Alert>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Warnings */}
      {analysis.warnings.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <XCircle className="h-5 w-5 text-red-500" />
              Security Warnings
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {analysis.warnings.map((warning, index) => (
                <Alert key={index} variant="destructive">
                  <XCircle className="h-4 w-4" />
                  <AlertDescription>{warning}</AlertDescription>
                </Alert>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Recommendations */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Info className="h-5 w-5 text-chart-1" />
            Recommendations
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {analysis.recommendations.map((recommendation, index) => (
              <Alert key={index}>
                <Info className="h-4 w-4" />
                <AlertDescription>{recommendation}</AlertDescription>
              </Alert>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
