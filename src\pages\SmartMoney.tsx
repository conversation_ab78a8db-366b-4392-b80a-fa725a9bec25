
import { Header<PERSON><PERSON> } from "@/components/HeaderBar";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useState } from "react";
import { AlertTriangle, ArrowDown, Arrow<PERSON><PERSON>, ArrowR<PERSON>, <PERSON><PERSON><PERSON>, Filter, Clock, RefreshCw } from "lucide-react";
import { cn } from "@/lib/utils";
import { Table, TableHeader, TableBody, TableRow, TableHead, TableCell } from "@/components/ui/table";
import { WalletActivityChart } from "@/components/WalletActivityChart";
import { TokenHeatmap } from "@/components/TokenHeatmap";
import { useRealSmartMoney } from "@/hooks/useRealSmartMoney";
import { toast } from "@/hooks/use-toast";

export function SmartMoney() {
  const {
    movements,
    walletDistribution,
    isLoading,
    setTimeframe,
    timeframe,
    error,
    refresh
  } = useRealSmartMoney();

  const [filterActive, setFilterActive] = useState(false);

  const handleRefresh = () => {
    refresh();
    toast({
      title: "Refreshing data",
      description: "Fetching latest whale movements and market data..."
    });
  };

  return (
    <div className="flex-1 flex flex-col min-w-0">
      <HeaderBar
        title="Smart Money Tracker"
        description="Monitor movements of whale wallets and institutional investors"
      />

      <main className="flex-1 overflow-y-auto p-4 md:p-6 lg:p-8">
        <div className="max-w-screen-2xl mx-auto space-y-6">
          <div className="flex flex-col md:flex-row gap-4 md:items-center md:justify-between">
            <div>
              <h2 className="text-2xl font-bold">Live Whale Activity</h2>
              <p className="text-muted-foreground">Real-time tracking of large transactions and whale movements</p>
            </div>

            <div className="flex flex-wrap gap-2">
              <Button
                variant={timeframe === "24h" ? "default" : "outline"}
                onClick={() => setTimeframe("24h")}
                size="sm"
                className="flex items-center gap-1"
              >
                <Clock size={14} />
                24h
              </Button>
              <Button
                variant={timeframe === "7d" ? "default" : "outline"}
                onClick={() => setTimeframe("7d")}
                size="sm"
                className="flex items-center gap-1"
              >
                <Clock size={14} />
                7d
              </Button>
              <Button
                variant={timeframe === "30d" ? "default" : "outline"}
                onClick={() => setTimeframe("30d")}
                size="sm"
                className="flex items-center gap-1"
              >
                <Clock size={14} />
                30d
              </Button>
              <Button
                variant="outline"
                onClick={handleRefresh}
                size="sm"
                className="flex items-center gap-1"
                disabled={isLoading}
              >
                <RefreshCw size={14} className={isLoading ? "animate-spin" : ""} />
                Refresh
              </Button>
              <Button
                variant={filterActive ? "default" : "outline"}
                onClick={() => setFilterActive(!filterActive)}
                size="sm"
                className="flex items-center gap-1"
              >
                <Filter size={14} />
                Filter
              </Button>
              <Button size="sm" className="flex items-center gap-1">
                <AlertTriangle size={14} />
                Set Alerts
              </Button>
            </div>
          </div>

          {error && (
            <Card className="border-red-200 bg-red-50">
              <CardContent className="p-4">
                <div className="flex items-center gap-2 text-red-700">
                  <AlertTriangle size={16} />
                  <span>{error}</span>
                </div>
              </CardContent>
            </Card>
          )}

          <Card className="border-border">
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>Real-Time Whale Movements</CardTitle>
              <div className="text-sm text-muted-foreground">
                Live data • Updates every 30 seconds
              </div>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="h-48 flex items-center justify-center">
                  <div className="flex items-center gap-2 text-muted-foreground">
                    <RefreshCw size={16} className="animate-spin" />
                    Loading live whale data...
                  </div>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Wallet</TableHead>
                        <TableHead>Action</TableHead>
                        <TableHead>Asset</TableHead>
                        <TableHead className="text-right">Amount</TableHead>
                        <TableHead className="text-right">Value (USD)</TableHead>
                        <TableHead className="text-right">Time</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {movements.map((item) => (
                        <TableRow key={item.id} className="hover:bg-secondary/30">
                          <TableCell>
                            <div>
                              <div className="font-medium flex items-center">
                                {item.wallet}
                                <span className="ml-2 px-2 py-0.5 text-xs rounded-full bg-secondary">
                                  {item.label}
                                </span>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className={cn(
                              "flex items-center",
                              item.action === "Buy" ? "text-crypto-positive" :
                              item.action === "Sell" ? "text-crypto-negative" :
                              "text-muted-foreground"
                            )}>
                              {item.action === "Buy" ? <ArrowUp size={16} className="mr-1" /> :
                               item.action === "Sell" ? <ArrowDown size={16} className="mr-1" /> :
                               <ArrowRight size={16} className="mr-1" />}
                              {item.action}
                            </div>
                          </TableCell>
                          <TableCell>{item.asset}</TableCell>
                          <TableCell className="text-right">{item.amount.toLocaleString()}</TableCell>
                          <TableCell className="text-right">${item.valueUSD.toLocaleString()}</TableCell>
                          <TableCell className="text-right text-muted-foreground">{item.timeAgo}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle>Live Wallet Activity Analysis</CardTitle>
                <div className="text-sm text-muted-foreground">
                  Real-time on-chain transaction patterns
                </div>
              </CardHeader>
              <CardContent className="p-4">
                <WalletActivityChart />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <PieChart size={18} className="mr-2" />
                  Live Asset Allocation
                </CardTitle>
                <div className="text-sm text-muted-foreground">
                  Real-time whale portfolio distribution
                </div>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="h-48 flex items-center justify-center">
                    <p className="text-muted-foreground">Loading allocation data...</p>
                  </div>
                ) : (
                  <div className="mt-4 space-y-3">
                    {walletDistribution.map((item) => (
                      <div key={item.name}>
                        <div className="flex justify-between text-sm mb-1">
                          <span>{item.name}</span>
                          <span className={cn(
                            item.change > 0 ? "text-crypto-positive" :
                            item.change < 0 ? "text-crypto-negative" :
                            "text-muted-foreground"
                          )}>
                            {item.change > 0 ? "+" : ""}{item.change}%
                          </span>
                        </div>
                        <div className="h-2 bg-secondary rounded-full overflow-hidden">
                          <div
                            className={`h-full ${item.color}`}
                            style={{ width: `${item.percentage}%` }}
                          ></div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Live Token Interest Heatmap</CardTitle>
              <div className="text-sm text-muted-foreground">
                Real-time whale sentiment and activity levels
              </div>
            </CardHeader>
            <CardContent>
              <TokenHeatmap />
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}
