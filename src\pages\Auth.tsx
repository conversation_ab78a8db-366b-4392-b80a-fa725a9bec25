import { useState, useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/auth/useAuth';
import { supabase } from '@/integrations/supabase/client';
import { AuthLayout } from '@/components/auth/AuthLayout';
import { AuthTabs } from '@/components/auth/AuthTabs';
import { SignInForm } from '@/components/auth/SignInForm';
import { SignUpForm } from '@/components/auth/SignUpForm';
import { SocialAuth } from '@/components/auth/SocialAuth';
import { AuthAlerts } from '@/components/auth/AuthAlerts';

export default function Auth() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [username, setUsername] = useState('');
  const [fullName, setFullName] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [authMode, setAuthMode] = useState<'sign_in' | 'sign_up'>('sign_in');
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const { signIn, signUp, user, isLoading } = useAuth();
  const location = useLocation();
  const from = location.state?.from?.pathname || '/dashboard';
  const [hasChecked, setHasChecked] = useState(false);

  // Clear error when switching tabs
  useEffect(() => {
    setError(null);
    setSuccessMessage(null);
  }, [authMode]);

  // Set hasChecked to true after initial auth check
  useEffect(() => {
    if (!isLoading) {
      setHasChecked(true);
    }
  }, [isLoading]);

  // Only redirect when auth state is confirmed and user is authenticated
  if (hasChecked && !isLoading && user) {
    console.log("User is authenticated, redirecting to:", from);
    return <Navigate to={from} replace />;
  }

  const handleEmailAuth = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);

    try {
      if (authMode === 'sign_in') {
        await signIn(email, password);
      } else {
        const metadata = {
          username,
          full_name: fullName,
        };
        await signUp(email, password, metadata);

        // Show success message after sign up
        setSuccessMessage("Account created. Please check your email for verification link.");
        setAuthMode('sign_in');
      }
    } catch (error: any) {
      console.error('Authentication error:', error);

      if (error.message?.includes('User already registered')) {
        setError('This email is already registered. Please sign in instead.');
      } else if (error.message?.includes('Email link is invalid or has expired')) {
        setError('The verification link is invalid or has expired. Please request a new one.');
      } else if (error.message?.includes('provider is not enabled')) {
        setError('This login method is not available. Please use email/password or contact support.');
      } else if (error.message?.includes('Invalid login credentials')) {
        setError('Invalid email or password. Please try again.');
      } else {
        setError(error.message || 'An error occurred during authentication.');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSocialAuth = async (provider: 'google' | 'apple' | 'facebook' | 'github') => {
    try {
      setError(null);
      setIsSubmitting(true);

      // Log for debugging
      console.log(`Attempting ${provider} sign in`);

      const { data, error } = await supabase.auth.signInWithOAuth({
        provider,
        options: {
          redirectTo: `${window.location.origin}/auth`,
          queryParams: provider === 'google' ? {
            // Add access_type=offline to get a refresh token
            access_type: 'offline',
            prompt: 'consent',
          } : undefined,
        },
      });

      if (error) {
        console.error(`${provider} auth error:`, error);
        throw error;
      }

      console.log(`${provider} auth initiated:`, data);
      // No need to set success message as the redirect will happen automatically
    } catch (error: any) {
      console.error(`${provider} sign in error:`, error);

      if (error.message?.includes('provider is not enabled')) {
        setError(`${provider} sign in is not configured. Please check your Supabase settings.`);
      } else if (error.message?.includes('popup_closed_by_user') || error.message?.includes('popup closed')) {
        setError(`${provider} sign in was cancelled. Please try again.`);
      } else if (error.message?.includes('redirect_uri_mismatch')) {
        setError(`Authentication error: The redirect URI doesn't match what's configured in ${provider}. Please contact support.`);
      } else if (error.message?.includes('invalid_client')) {
        setError(`Authentication error: Invalid client configuration for ${provider}. Please check your OAuth settings.`);
      } else {
        setError(`${provider} sign in failed: ${error.message || 'Unknown error'}`);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handlePasswordReset = async () => {
    if (!email) {
      setError('Please enter your email address to reset your password.');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/auth`,
      });

      if (error) throw error;

      setSuccessMessage('Password reset instructions have been sent to your email.');
    } catch (error: any) {
      setError(error.message || 'Failed to send password reset email.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const getAuthTitle = () => {
    return authMode === 'sign_in' ? 'Welcome Back' : 'Create an Account';
  };

  const getAuthDescription = () => {
    return authMode === 'sign_in'
      ? 'Sign in to access your crypto analytics dashboard'
      : 'Sign up to start tracking crypto assets and market trends';
  };

  return (
    <AuthLayout
      title={getAuthTitle()}
      description={getAuthDescription()}
    >
      <AuthAlerts error={error} successMessage={successMessage} />

      <AuthTabs
        value={authMode}
        onValueChange={(v) => setAuthMode(v as 'sign_in' | 'sign_up')}
        signInContent={
          <SignInForm
            email={email}
            setEmail={setEmail}
            password={password}
            setPassword={setPassword}
            isSubmitting={isSubmitting}
            onSubmit={handleEmailAuth}
            onPasswordReset={handlePasswordReset}
          />
        }
        signUpContent={
          <SignUpForm
            email={email}
            setEmail={setEmail}
            username={username}
            setUsername={setUsername}
            fullName={fullName}
            setFullName={setFullName}
            password={password}
            setPassword={setPassword}
            isSubmitting={isSubmitting}
            onSubmit={handleEmailAuth}
          />
        }
      />

      <SocialAuth
        onSocialAuth={handleSocialAuth}
        isDisabled={isSubmitting}
      />
    </AuthLayout>
  );
}
