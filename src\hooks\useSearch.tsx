
import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { searchCoins } from '@/services/api/searchService';
import { useNavigate } from 'react-router-dom';
import { useToast } from '@/hooks/use-toast';

export function useSearch() {
  const [searchQuery, setSearchQuery] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const navigate = useNavigate();
  const { toast } = useToast();
  
  const { data, isLoading, error } = useQuery({
    queryKey: ['search', searchQuery],
    queryFn: () => searchCoins(searchQuery),
    enabled: searchQuery.length > 1,
    staleTime: 60000, // 1 minute
  });
  
  const handleSearch = (query: string) => {
    setSearchQuery(query);
  };
  
  const handleSelect = (coinId: string, coinName: string) => {
    // Close the search dialog
    setIsOpen(false);
    setSearchQuery('');
    
    // Navigate to appropriate page or show details
    navigate(`/forecasting?coin=${coinId}`);
    
    toast({
      title: "Coin selected",
      description: `Showing details for ${coinName}`,
    });
  };
  
  return {
    searchQuery,
    searchResults: data?.coins || [],
    isLoading,
    error,
    isOpen,
    setIsOpen,
    handleSearch,
    handleSelect
  };
}
