
/**
 * Format date for display in charts and tooltips
 */
export const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
};

/**
 * Format price for display in charts and tooltips
 */
export const formatPrice = (price: number) => {
  if (price > 1000) {
    return `$${price.toLocaleString(undefined, { maximumFractionDigits: 0 })}`;
  } else if (price > 1) {
    return `$${price.toLocaleString(undefined, { maximumFractionDigits: 2 })}`;
  } else {
    return `$${price.toLocaleString(undefined, { maximumFractionDigits: 6 })}`;
  }
};

/**
 * Calculate potential return percentage
 */
export const calculatePotentialReturn = (lastHistoricalPrice: number, lastForecastPrice: number): number => {
  return ((lastForecastPrice - lastHistoricalPrice) / lastHistoricalPrice) * 100;
};
