
import { fetchCoinHistoricalData, transformToCandlestick } from './historyClient';
import { calculateSMA, calculateRSI, calculateBollingerBands, calculateMACD } from './technicalIndicators';
import { generatePredictions, calculateModelMetrics } from './predictions';

// Get full price forecast data including historical and predictions with technical indicators
export const getPriceForecastData = async (coinId: string, days: number = 30) => {
  try {
    const historyData = await fetchCoinHistoricalData(coinId, days);
    const candlestickData = transformToCandlestick(historyData);
    const predictions = generatePredictions(candlestickData);
    
    // Calculate technical indicators
    const prices = candlestickData.map(d => d.close);
    
    // Add technical indicators
    const sma20 = calculateSMA(prices, 20);
    const sma50 = calculateSMA(prices, 50);
    const sma200 = calculateSMA(prices, 200);
    const rsi = calculateRSI(prices);
    const bollingerBands = calculateBollingerBands(prices);
    const macd = calculateMACD(prices);
    
    // Add indicators to candlestick data
    const enhancedData = candlestickData.map((item, index) => ({
      ...item,
      sma20: sma20[index],
      sma50: sma50[index],
      sma200: sma200[index],
      rsi: rsi[index],
      upperBand: bollingerBands.upper[index],
      middleBand: bollingerBands.middle[index],
      lowerBand: bollingerBands.lower[index],
      macd: macd.macd[index],
      signal: macd.signal[index],
      histogram: macd.histogram[index]
    }));
    
    // Combine historical and prediction data
    const fullData = [...enhancedData, ...predictions];
    const metrics = calculateModelMetrics(candlestickData, predictions);
    
    return { 
      data: fullData,
      metrics
    };
  } catch (error) {
    console.error("Error generating forecast data:", error);
    return { 
      data: [],
      metrics: {
        rmse: 0,
        mae: 0,
        rSquared: 0,
        mape: 0
      }
    };
  }
};

// Get educational content about technical indicators
export const getTechnicalIndicatorEducation = () => {
  return {
    sma: {
      name: "Simple Moving Average",
      description: "Average price over a specific period, used to identify trend direction",
      interpretation: "Price above SMA indicates uptrend, below indicates downtrend"
    },
    rsi: {
      name: "Relative Strength Index",
      description: "Momentum oscillator measuring speed and magnitude of price changes",
      interpretation: "Above 70 suggests overbought, below 30 suggests oversold"
    },
    bollinger: {
      name: "Bollinger Bands",
      description: "Price channels based on standard deviation from moving average",
      interpretation: "Price touching upper band suggests overbought, lower band suggests oversold"
    },
    macd: {
      name: "Moving Average Convergence Divergence",
      description: "Trend-following momentum indicator showing relationship between two moving averages",
      interpretation: "MACD above signal line suggests bullish momentum, below suggests bearish"
    }
  };
};
