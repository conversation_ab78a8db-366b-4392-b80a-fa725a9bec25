
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { useState, useEffect } from "react";
import { HeaderBar } from "@/components/HeaderBar";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  BookOpen,
  GraduationCap,
  Code,
  Layers,
  Search,
  BookText,
  ChartLine,
  Shield,
  Database
} from "lucide-react";

import { useEducation } from "@/hooks/useEducation";
import { useToast } from "@/hooks/use-toast";
import TutorialsTab from "@/components/education/tabs/TutorialsTab";
import TokensTab from "@/components/education/tabs/TokensTab";
import Glossary from "@/components/education/Glossary";
import ProtocolExplainers from "@/components/education/ProtocolExplainers";
import StrategyEducation from "@/components/education/StrategyEducation";
import WalletSecurity from "@/components/education/WalletSecurity";
import BlockchainEducation from "@/components/education/BlockchainEducation";
import TutorialContentRenderer from "@/components/education/TutorialContentRenderer";

export default function Education() {
  const { section, id } = useParams();
  const [activeTab, setActiveTab] = useState(section || "tutorials");
  const [searchQuery, setSearchQuery] = useState("");
  const { toast } = useToast();

  const {
    tutorials,
    tokenDeepDives,
    glossaryTerms,
    protocolExplainers,
    knowledgeArticles,
    loading,
    progress,
    getTutorialById,
    getTokenDeepDiveById,
    getProtocolExplainerById,
    searchEducation
  } = useEducation();

  // If we have an ID param, redirect to the appropriate content
  useEffect(() => {
    if (section) {
      setActiveTab(section);
    }
  }, [section]);

  // Render the appropriate component based on the current section
  const renderContent = () => {
    // Handle specific tutorial
    if (section === "tutorial" && id) {
      return <TutorialContentRenderer
        tutorialId={id}
        getTutorialById={getTutorialById}
        loading={loading}
      />;
    }

    // Handle specific token deep dive
    if (section === "token" && id) {
      const token = getTokenDeepDiveById(id);
      if (!token) {
        return <div className="p-8 text-center">Token information not found</div>;
      }
      return <TokenExplorer token={token} />;
    }

    // Handle tabs content
    switch (activeTab) {
      case "tutorials":
        return <TutorialsTab tutorials={tutorials} progress={progress} loading={loading} />;
      case "tokens":
        return <TokensTab tokens={tokenDeepDives} loading={loading} />;
      case "glossary":
        return <Glossary terms={glossaryTerms} loading={loading} />;
      case "protocols":
        return <ProtocolExplainers protocols={protocolExplainers} loading={loading} />;
      case "strategy":
        return <StrategyEducation loading={loading} />;
      case "security":
        return <WalletSecurity loading={loading} />;
      case "blockchain":
        return <BlockchainEducation loading={loading} />;
      default:
        return <TutorialsTab tutorials={tutorials} progress={progress} loading={loading} />;
    }
  };

  return (
    <div className="flex-1 flex flex-col min-w-0">
      <HeaderBar
        title="Cryptocurrency Education"
        description="Learn about blockchain technology, cryptocurrencies, and decentralized finance"
      />

      <main className="flex-1 overflow-y-auto p-4 md:p-6 lg:p-8">
        <div className="max-w-7xl mx-auto space-y-8">
          {/* Education Navigation */}
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 mb-6">
              <TabsList className="grid grid-cols-4 md:grid-cols-7 gap-1">
                <TabsTrigger value="tutorials" className="flex items-center gap-1">
                  <BookOpen className="h-4 w-4" />
                  <span className="hidden sm:inline">Tutorials</span>
                </TabsTrigger>
                <TabsTrigger value="tokens" className="flex items-center gap-1">
                  <Code className="h-4 w-4" />
                  <span className="hidden sm:inline">Token Dives</span>
                </TabsTrigger>
                <TabsTrigger value="glossary" className="flex items-center gap-1">
                  <BookText className="h-4 w-4" />
                  <span className="hidden sm:inline">Glossary</span>
                </TabsTrigger>
                <TabsTrigger value="protocols" className="flex items-center gap-1">
                  <Layers className="h-4 w-4" />
                  <span className="hidden sm:inline">Protocols</span>
                </TabsTrigger>
                <TabsTrigger value="strategy" className="flex items-center gap-1">
                  <ChartLine className="h-4 w-4" />
                  <span className="hidden sm:inline">Strategy</span>
                </TabsTrigger>
                <TabsTrigger value="security" className="flex items-center gap-1">
                  <Shield className="h-4 w-4" />
                  <span className="hidden sm:inline">Security</span>
                </TabsTrigger>
                <TabsTrigger value="blockchain" className="flex items-center gap-1">
                  <Database className="h-4 w-4" />
                  <span className="hidden sm:inline">Blockchain</span>
                </TabsTrigger>
              </TabsList>

              {/* Search field */}
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <input
                  type="search"
                  placeholder="Search educational content..."
                  className="pl-9 h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>

            {renderContent()}
          </Tabs>
        </div>
      </main>
    </div>
  );
}

// Import TokenExplorer separately since we removed it from the initial imports
import { TokenExplorer } from "@/components/education/TokenExplorer";
