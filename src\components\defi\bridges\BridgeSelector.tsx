
import { useMemo } from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";

interface BridgeSelectorProps {
  bridges: any[];
  sourceChain: string;
  setSourceChain: (chain: string) => void;
  targetChain: string;
  setTargetChain: (chain: string) => void;
  amount: string;
  setAmount: (amount: string) => void;
}

const BridgeSelector = ({
  bridges,
  sourceChain,
  setSourceChain,
  targetChain,
  setTargetChain,
  amount,
  setAmount,
}: BridgeSelectorProps) => {
  // Get available networks from bridge data
  const availableNetworks = useMemo(() => {
    if (!bridges || bridges.length === 0) return [];

    const networksSet = new Set<string>();
    bridges.forEach(bridge => {
      bridge.supportedNetworks.forEach((network: string) => {
        networksSet.add(network);
      });
    });

    return Array.from(networksSet);
  }, [bridges]);

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      <div className="space-y-2">
        <Label htmlFor="source-chain">Source Chain</Label>
        <Select value={sourceChain} onValueChange={setSourceChain}>
          <SelectTrigger id="source-chain">
            <SelectValue placeholder="Select source" />
          </SelectTrigger>
          <SelectContent>
            {availableNetworks.map(network => (
              <SelectItem key={`source-${network}`} value={network}>
                {network}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="target-chain">Target Chain</Label>
        <Select value={targetChain} onValueChange={setTargetChain}>
          <SelectTrigger id="target-chain">
            <SelectValue placeholder="Select target" />
          </SelectTrigger>
          <SelectContent>
            {availableNetworks.map(network => (
              <SelectItem
                key={`target-${network}`}
                value={network}
                disabled={network === sourceChain}
              >
                {network}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="amount">Amount (USD)</Label>
        <Input
          id="amount"
          type="number"
          value={amount}
          onChange={(e) => setAmount(e.target.value)}
          placeholder="Enter amount"
          min={1}
        />
      </div>
    </div>
  );
};

// Add loading skeleton as a static property
BridgeSelector.LoadingSkeleton = function LoadingSkeleton() {
  return (
    <div className="space-y-4">
      <Skeleton className="h-10 w-full" />
      <Skeleton className="h-20 w-full" />
    </div>
  );
};

export default BridgeSelector;
