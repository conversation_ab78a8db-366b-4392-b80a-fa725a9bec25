
import { coinGeckoAxios, handleApiError, cacheResponse, getCachedData } from "./coinGeckoClient";

// Standardized interfaces
export interface CoinMarketData {
  id: string;
  name: string;
  symbol: string;
  current_price: number;
  market_cap: number;
  market_cap_rank: number;
  total_volume: number;
  price_change_percentage_24h: number;
  image?: string;
}

export interface GlobalMarketData {
  total_market_cap: { usd: number };
  total_volume: { usd: number };
  market_cap_percentage: { btc: number };
  active_cryptocurrencies: number;
}

// Optimized top coins fetching with caching
export const fetchTopCoins = async (limit = 10): Promise<CoinMarketData[]> => {
  try {
    const cacheKey = `top_coins_${limit}`;
    const cachedData = getCachedData(cacheKey, 2 * 60 * 1000); // 2 minutes cache
    if (cachedData) return cachedData;
    
    const response = await coinGeckoAxios.get("/coins/markets", {
      params: {
        vs_currency: 'usd',
        order: 'market_cap_desc',
        per_page: limit,
        page: 1,
        sparkline: false,
        price_change_percentage: '24h'
      }
    });
    
    cacheResponse(cacheKey, response.data);
    return response.data;
  } catch (error) {
    return handleApiError(error, {
      key: `top_coins_${limit}`, 
      data: []
    });
  }
};

// Enhanced coin data fetching
export const fetchCoinData = async (coinId: string) => {
  try {
    const cacheKey = `coin_${coinId}`;
    const cachedData = getCachedData(cacheKey, 5 * 60 * 1000); // 5 minutes cache
    if (cachedData) return cachedData;
    
    const response = await coinGeckoAxios.get(`/coins/${coinId}`, {
      params: {
        localization: false,
        tickers: false,
        market_data: true,
        community_data: true,
        developer_data: true
      }
    });
    
    cacheResponse(cacheKey, response.data);
    return response.data;
  } catch (error) {
    return handleApiError(error, {
      key: `coin_${coinId}`,
      data: null
    });
  }
};

// Streamlined global data fetching
export const fetchGlobalData = async (): Promise<{ data: GlobalMarketData } | null> => {
  try {
    const cachedData = getCachedData("global", 5 * 60 * 1000);
    if (cachedData) return cachedData;
    
    const response = await coinGeckoAxios.get("/global");
    cacheResponse("global", response.data);
    return response.data;
  } catch (error) {
    return handleApiError(error, {
      key: "global",
      data: { data: {} as GlobalMarketData }
    });
  }
};

// Improved trending coins with better error handling
export const fetchTrendingCoins = async () => {
  try {
    const cachedData = getCachedData("trending", 10 * 60 * 1000); // 10 minutes cache
    if (cachedData) return cachedData;
    
    const response = await coinGeckoAxios.get("/search/trending");
    
    if (response.data?.coins && Array.isArray(response.data.coins)) {
      const coins = response.data.coins;
      cacheResponse("trending", coins);
      return coins;
    } else {
      console.warn("Unexpected trending response format:", response.data);
      throw new Error("Invalid response format from trending API");
    }
  } catch (error) {
    console.error("Error fetching trending coins:", error);
    return handleApiError(error, {
      key: "trending",
      data: []
    });
  }
};

// Optimized market data for multiple coins
export const getCoinMarketData = async (coinIds: string[]) => {
  try {
    const cacheKey = `market_data_${coinIds.sort().join(',')}`;
    const cachedData = getCachedData(cacheKey, 3 * 60 * 1000); // 3 minutes cache
    if (cachedData) return cachedData;
    
    const idsParam = coinIds.join(',');
    const response = await coinGeckoAxios.get("/coins/markets", {
      params: {
        vs_currency: 'usd',
        ids: idsParam,
        order: 'market_cap_desc',
        per_page: Math.min(coinIds.length, 100),
        page: 1,
        sparkline: false,
        price_change_percentage: '24h,7d,30d'
      }
    });
    
    // Convert array to object with coin ids as keys
    const marketDataMap = response.data.reduce((acc: any, coin: any) => {
      acc[coin.id] = coin;
      return acc;
    }, {});
    
    cacheResponse(cacheKey, marketDataMap);
    return marketDataMap;
  } catch (error) {
    return handleApiError(error, {
      key: `market_data_${coinIds.join(',')}`,
      data: {}
    });
  }
};

// Utility function to get recently added coins
export const fetchRecentlyAdded = async (limit = 10) => {
  try {
    const cacheKey = `recently_added_${limit}`;
    const cachedData = getCachedData(cacheKey, 15 * 60 * 1000); // 15 minutes cache
    if (cachedData) return cachedData;
    
    // Get newest coins by market cap
    const response = await coinGeckoAxios.get("/coins/markets", {
      params: {
        vs_currency: 'usd',
        order: 'market_cap_desc',
        per_page: limit * 2, // Get more to filter
        page: 1,
        sparkline: false
      }
    });
    
    // Filter for coins that are relatively new (simple heuristic)
    const recentCoins = response.data
      .filter((coin: any) => coin.market_cap_rank > 50) // Exclude very established coins
      .slice(0, limit);
    
    cacheResponse(cacheKey, recentCoins);
    return recentCoins;
  } catch (error) {
    return handleApiError(error, {
      key: `recently_added_${limit}`,
      data: []
    });
  }
};
