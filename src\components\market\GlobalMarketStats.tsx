
import { ArrowDown, ArrowUp, Bitcoin, DollarSign, <PERSON><PERSON>hart, TrendingUp } from "lucide-react";
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { StatCard } from "@/components/StatCard";

interface GlobalMarketStatsProps {
  globalData: any;
}

export function GlobalMarketStats({ globalData }: GlobalMarketStatsProps) {
  const marketData = globalData.data;

  if (!marketData) return null;

  const formatMarketCap = (cap: number) => {
    return `$${(cap / **********).toFixed(2)}T`;
  };

  const formatPercentage = (value: number) => {
    return value?.toFixed(2);
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <StatCard
        title="Global Market Cap"
        value={formatMarketCap(marketData.total_market_cap.usd)}
        change={marketData.market_cap_change_percentage_24h_usd}
        icon={<DollarSign className="h-5 w-5 text-primary" />}
        animationDelay="0ms"
      />

      <StatCard
        title="24h Trading Volume"
        value={`$${(marketData.total_volume.usd / **********).toFixed(2)}B`}
        change={(marketData.total_volume.usd / marketData.total_market_cap.usd) * 100 - 5}
        icon={<LineChart className="h-5 w-5 text-primary" />}
        animationDelay="100ms"
      />

      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div>
              <StatCard
                title="BTC Dominance"
                value={`${marketData.market_cap_percentage.btc.toFixed(1)}%`}
                change={marketData.market_cap_percentage.btc - 42}
                icon={<Bitcoin className="h-5 w-5 text-primary" />}
                animationDelay="200ms"
              />
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <p>Percentage of total market capitalization</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div>
              <StatCard
                title="Active Cryptocurrencies"
                value={marketData.active_cryptocurrencies.toLocaleString()}
                change={0.8}
                icon={<TrendingUp className="h-5 w-5 text-primary" />}
                animationDelay="300ms"
              />
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <p>Number of active cryptocurrencies tracked</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </div>
  );
}
