
import { PredictionModel } from "@/types/aiInsights";

interface MetricsDisplayProps {
  prediction: PredictionModel;
  potentialReturn: number;
}

export default function MetricsDisplay({ prediction, potentialReturn }: MetricsDisplayProps) {
  return (
    <div className="mt-6 grid grid-cols-2 md:grid-cols-4 gap-4">
      <div className="p-3 bg-secondary/30 rounded-md">
        <div className="text-sm text-muted-foreground">Model Confidence</div>
        <div className="text-xl font-bold mt-1">
          {Math.round(prediction.metrics.confidence * 100)}%
        </div>
      </div>
      
      <div className="p-3 bg-secondary/30 rounded-md">
        <div className="text-sm text-muted-foreground">Potential Return</div>
        <div className={`text-xl font-bold mt-1 ${potentialReturn >= 0 ? 'text-crypto-positive' : 'text-crypto-negative'}`}>
          {potentialReturn >= 0 ? '+' : ''}{potentialReturn.toFixed(2)}%
        </div>
      </div>
      
      <div className="p-3 bg-secondary/30 rounded-md">
        <div className="text-sm text-muted-foreground">30D Volatility</div>
        <div className="text-xl font-bold mt-1">
          {prediction.metrics.volatility.toFixed(1)}%
        </div>
      </div>
      
      <div className="p-3 bg-secondary/30 rounded-md">
        <div className="text-sm text-muted-foreground">Forecast Error</div>
        <div className="text-xl font-bold mt-1">
          {prediction.metrics.mape.toFixed(1)}%
        </div>
      </div>
    </div>
  );
}
