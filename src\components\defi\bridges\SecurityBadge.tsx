
// React import removed - using new JSX transform
import { Badge } from "@/components/ui/badge";

interface SecurityBadgeProps {
  score: number;
  showText?: boolean;
}

export const SecurityBadge = ({ score, showText = false }: SecurityBadgeProps) => {
  let bgColor = "bg-crypto-positive/20 text-crypto-positive";
  let label = "High";

  if (score <= 4) {
    bgColor = "bg-destructive/20 text-destructive";
    label = "Low";
  } else if (score <= 7) {
    bgColor = "bg-chart-3/20 text-chart-3";
    label = "Medium";
  }

  return (
    <Badge variant="outline" className={`${bgColor} font-medium`}>
      {score}/10 {showText && `- ${label}`}
    </Badge>
  );
};


