
import { useState, useEffect } from "react";
import { HeaderBar } from "@/components/HeaderBar";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { fetchAnomalyData, Anomaly } from "@/services/api/anomalyDetectionService";
import { cn } from "@/lib/utils";
import { AlertCircle, Bell, BellOff, BarChart } from "lucide-react";
import { toast } from "@/components/ui/use-toast";

export function Anomalies() {
  const [anomalyData, setAnomalyData] = useState<Anomaly[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  useEffect(() => {
    const loadAnomalyData = async () => {
      setIsLoading(true);
      try {
        const data = await fetchAnomalyData();
        setAnomalyData(data);
      } catch (error) {
        console.error("Failed to fetch anomaly data:", error);
        toast({
          title: "Error",
          description: "Failed to load anomaly data. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadAnomalyData();
  }, []);

  return (
    <div className="flex-1 flex flex-col min-w-0">
      <HeaderBar
        title="Anomaly Detection"
        description="Real-time market anomaly detection powered by machine learning"
        isLoading={isLoading}
        onRefresh={() => {
          fetchAnomalyData().then(data => {
            setAnomalyData(data);
            toast({
              title: "Data refreshed",
              description: "Latest anomaly data has been loaded.",
            });
          });
        }}
      />

      <main className="flex-1 overflow-y-auto p-4 md:p-6 lg:p-8">
        <div className="max-w-screen-2xl mx-auto space-y-6">
          <Card className="border-border">
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <AlertCircle size={18} className="text-crypto-negative" />
                Active Anomalies
              </CardTitle>
              <div className="flex items-center gap-2">
                <Button size="sm" variant="outline">
                  <Bell size={16} className="mr-2" />
                  Notifications
                </Button>
                <Button size="sm">
                  <BellOff size={16} className="mr-2" />
                  Mute All
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-border">
                      <th className="text-left p-3 font-medium text-muted-foreground">Type</th>
                      <th className="text-left p-3 font-medium text-muted-foreground">Asset</th>
                      <th className="text-left p-3 font-medium text-muted-foreground">Confidence</th>
                      <th className="text-left p-3 font-medium text-muted-foreground">Impact</th>
                      <th className="text-left p-3 font-medium text-muted-foreground">Detected</th>
                      <th className="text-right p-3 font-medium text-muted-foreground">Status</th>
                    </tr>
                  </thead>
                  <tbody>
                    {anomalyData.map((anomaly) => (
                      <tr key={anomaly.id} className="border-b border-border/50 hover:bg-secondary/30">
                        <td className="p-3">
                          <div className="font-medium">{anomaly.type}</div>
                        </td>
                        <td className="p-3">{anomaly.asset}</td>
                        <td className="p-3">
                          <div className="flex items-center gap-2">
                            <div className="h-2 w-24 bg-secondary rounded-full overflow-hidden">
                              <div
                                className={cn(
                                  "h-full",
                                  anomaly.confidence > 90 ? "bg-crypto-positive" :
                                  anomaly.confidence > 80 ? "bg-amber-500" :
                                  "bg-crypto-negative"
                                )}
                                style={{ width: `${anomaly.confidence}%` }}
                              ></div>
                            </div>
                            <span>{anomaly.confidence}%</span>
                          </div>
                        </td>
                        <td className="p-3">
                          <span className={cn(
                            "px-2 py-0.5 text-xs rounded-full",
                            anomaly.impact === "High" ? "bg-crypto-negative/20 text-crypto-negative" :
                            anomaly.impact === "Medium" ? "bg-amber-500/20 text-amber-500" :
                            "bg-blue-500/20 text-blue-500"
                          )}>
                            {anomaly.impact}
                          </span>
                        </td>
                        <td className="p-3 text-muted-foreground">
                          {new Date(anomaly.timeDetected).toLocaleString()}
                        </td>
                        <td className="p-3 text-right">
                          <span className={cn(
                            "px-2 py-0.5 text-xs rounded-full",
                            anomaly.status === "Active" ? "bg-crypto-negative/20 text-crypto-negative" :
                            anomaly.status === "Monitoring" ? "bg-amber-500/20 text-amber-500" :
                            "bg-crypto-positive/20 text-crypto-positive"
                          )}>
                            {anomaly.status}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <BarChart size={18} className="mr-2" />
                  Anomaly Detection Model
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-center h-64 border border-dashed border-border rounded-md">
                  <div className="text-center">
                    <p className="text-muted-foreground mb-4">
                      Connect to Glassnode API to enable anomaly detection
                    </p>
                    <Button disabled>
                      Connect Glassnode API
                    </Button>
                  </div>
                </div>

                <div className="mt-6 text-sm">
                  <h4 className="font-medium mb-2">Model Information:</h4>
                  <p className="text-muted-foreground mb-2">
                    Our anomaly detection engine uses Isolation Forest algorithms to identify unusual patterns in market data.
                  </p>
                  <ul className="list-disc pl-5 text-muted-foreground space-y-1">
                    <li>Analysis based on 40+ on-chain metrics</li>
                    <li>Trained on 5 years of historical data</li>
                    <li>Recall rate over 90% for significant events</li>
                    <li>Updated every 5 minutes with latest data</li>
                  </ul>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Detection Settings</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h4 className="text-sm font-medium mb-2">Sensitivity</h4>
                    <input
                      type="range"
                      min="1"
                      max="10"
                      defaultValue="7"
                      className="w-full h-2 bg-secondary rounded-lg appearance-none cursor-pointer"
                      disabled
                    />
                    <div className="flex justify-between text-xs text-muted-foreground mt-1">
                      <span>Low</span>
                      <span>High</span>
                    </div>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium mb-2">Alert Types</h4>
                    <div className="space-y-2">
                      <div className="flex items-center">
                        <input type="checkbox" id="volatility" defaultChecked disabled />
                        <label htmlFor="volatility" className="ml-2 text-sm">Volatility Spikes</label>
                      </div>
                      <div className="flex items-center">
                        <input type="checkbox" id="volume" defaultChecked disabled />
                        <label htmlFor="volume" className="ml-2 text-sm">Unusual Volume</label>
                      </div>
                      <div className="flex items-center">
                        <input type="checkbox" id="whale" defaultChecked disabled />
                        <label htmlFor="whale" className="ml-2 text-sm">Whale Movements</label>
                      </div>
                      <div className="flex items-center">
                        <input type="checkbox" id="manipulation" defaultChecked disabled />
                        <label htmlFor="manipulation" className="ml-2 text-sm">Price Manipulation</label>
                      </div>
                      <div className="flex items-center">
                        <input type="checkbox" id="pattern" defaultChecked disabled />
                        <label htmlFor="pattern" className="ml-2 text-sm">Pattern Breaks</label>
                      </div>
                    </div>
                  </div>

                  <Button variant="outline" size="sm" className="w-full mt-2" disabled>
                    Update Settings
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  );
}
