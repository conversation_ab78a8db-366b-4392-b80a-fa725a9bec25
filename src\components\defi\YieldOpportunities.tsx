
import React, { useMemo } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useDefiData } from "@/hooks/useDefiData";
import { Skeleton } from "@/components/ui/skeleton";
import { Info } from "lucide-react";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

export default function YieldOpportunities({ isLoading }: { isLoading: boolean }) {
  const { opportunities } = useDefiData();

  // Sort opportunities by real APY
  const sortedOpportunities = useMemo(() => {
    return [...(opportunities || [])]
      .sort((a, b) => b.realApy - a.realApy);
  }, [opportunities]);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          High-Yield DeFi Opportunities
        </CardTitle>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <YieldTableSkeleton />
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Protocol</TableHead>
                  <TableHead>Pool</TableHead>
                  <TableHead>Chain</TableHead>
                  <TableHead className="text-right">Base APY</TableHead>
                  <TableHead className="text-right">Rewards APR</TableHead>
                  <TableHead className="text-right">Real APY</TableHead>
                  <TableHead className="text-right">
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger className="flex items-center gap-1">
                          Risk Score <Info className="h-3.5 w-3.5" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p className="w-60">Risk score ranges from 1 (lowest risk) to 10 (highest risk)</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </TableHead>
                  <TableHead className="text-right">TVL</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {sortedOpportunities.map((opportunity, index) => (
                  <TableRow key={`${opportunity.protocol}-${opportunity.pool}-${index}`}>
                    <TableCell className="font-medium">{opportunity.protocol}</TableCell>
                    <TableCell>{opportunity.pool}</TableCell>
                    <TableCell>{opportunity.chain}</TableCell>
                    <TableCell className="text-right">{opportunity.baseApy.toFixed(2)}%</TableCell>
                    <TableCell className="text-right">{opportunity.rewardsApr.toFixed(2)}%</TableCell>
                    <TableCell className="font-medium text-right text-green-600 dark:text-green-400">
                      {opportunity.realApy.toFixed(2)}%
                    </TableCell>
                    <TableCell className="text-right">
                      <RiskBadge score={opportunity.riskScore} />
                    </TableCell>
                    <TableCell className="text-right">${formatTVL(opportunity.tvl)}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

function formatTVL(value: number): string {
  if (value >= 1_000_000_000) {
    return (value / 1_000_000_000).toFixed(2) + 'B';
  } else if (value >= 1_000_000) {
    return (value / 1_000_000).toFixed(2) + 'M';
  } else if (value >= 1_000) {
    return (value / 1_000).toFixed(2) + 'K';
  } else {
    return value.toFixed(2);
  }
}

function RiskBadge({ score }: { score: number }) {
  let bgColor = "bg-crypto-positive/20 text-crypto-positive";
  if (score >= 7) {
    bgColor = "bg-destructive/20 text-destructive";
  } else if (score >= 4) {
    bgColor = "bg-chart-3/20 text-chart-3";
  }

  return (
    <span className={`inline-block px-2 py-1 text-xs font-medium rounded-full ${bgColor}`}>
      {score}
    </span>
  );
}

function YieldTableSkeleton() {
  return (
    <div className="w-full space-y-3">
      {Array(6).fill(0).map((_, i) => (
        <div key={i} className="flex space-x-4">
          <Skeleton className="h-6 w-20" />
          <Skeleton className="h-6 w-28" />
          <Skeleton className="h-6 w-16" />
          <Skeleton className="h-6 w-12" />
          <Skeleton className="h-6 w-12" />
          <Skeleton className="h-6 w-12" />
          <Skeleton className="h-6 w-8" />
          <Skeleton className="h-6 w-16" />
        </div>
      ))}
    </div>
  );
}
