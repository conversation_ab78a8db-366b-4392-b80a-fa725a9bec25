
import { DollarSign, Activity, BarChart3, Target } from "lucide-react";

interface KeyMetricsBarProps {
  stats: any;
  fearGreed: any;
}

export function KeyMetricsBar({ stats, fearGreed }: KeyMetricsBarProps) {
  const formatCurrency = (value: number) => {
    if (value >= 1e12) return `$${(value / 1e12).toFixed(2)}T`;
    if (value >= 1e9) return `$${(value / 1e9).toFixed(2)}B`;
    if (value >= 1e6) return `$${(value / 1e6).toFixed(2)}M`;
    return `$${value.toLocaleString()}`;
  };

  return (
    <div className="grid grid-cols-4 gap-4">
      <div className="bg-card border border-border p-4 rounded">
        <div className="flex items-center justify-between">
          <div>
            <div className="text-xs text-muted-foreground uppercase tracking-wide">Market Cap</div>
            <div className="text-xl font-bold text-foreground">{formatCurrency(stats?.totalMarketCap * 1e12 || 0)}</div>
            <div className="text-sm text-green-500 font-semibold">+{stats?.change24h?.toFixed(2) || 0}%</div>
          </div>
          <DollarSign className="h-8 w-8 text-orange-500" />
        </div>
      </div>

      <div className="bg-card border border-border p-4 rounded">
        <div className="flex items-center justify-between">
          <div>
            <div className="text-xs text-muted-foreground uppercase tracking-wide">24H Volume</div>
            <div className="text-xl font-bold text-foreground">{formatCurrency(stats?.dailyVolume * 1e9 || 0)}</div>
            <div className="text-sm text-blue-500 font-semibold">+3.2%</div>
          </div>
          <Activity className="h-8 w-8 text-blue-500" />
        </div>
      </div>

      <div className="bg-card border border-border p-4 rounded">
        <div className="flex items-center justify-between">
          <div>
            <div className="text-xs text-muted-foreground uppercase tracking-wide">Active Markets</div>
            <div className="text-xl font-bold text-foreground">{stats?.activeMarkets?.toLocaleString() || "0"}</div>
            <div className="text-sm text-purple-500 font-semibold">+1.5%</div>
          </div>
          <BarChart3 className="h-8 w-8 text-purple-500" />
        </div>
      </div>

      <div className="bg-card border border-border p-4 rounded">
        <div className="flex items-center justify-between">
          <div>
            <div className="text-xs text-muted-foreground uppercase tracking-wide">Fear & Greed</div>
            <div className="text-xl font-bold text-foreground">{fearGreed?.value || 50}</div>
            <div className="text-sm text-yellow-500 font-semibold">{fearGreed?.indicator || "Neutral"}</div>
          </div>
          <Target className="h-8 w-8 text-yellow-500" />
        </div>
      </div>
    </div>
  );
}
