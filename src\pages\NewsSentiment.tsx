
import { useState } from "react";
import { useToast } from "@/hooks/use-toast";
import { HeaderBar } from "@/components/HeaderBar";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { useNewsSentiment } from "@/hooks/useNewsSentiment";
import { COINGECKO_ATTRIBUTION } from "@/services/api/coinGeckoClient";
import NewsAggregator from "@/components/news/NewsAggregator";
import SentimentAnalysis from "@/components/news/SentimentAnalysis";
import AlertBuilder from "@/components/news/AlertBuilder";

export function NewsSentiment() {
  const [refreshing, setRefreshing] = useState(false);
  const { toast } = useToast();
  const {
    newsData,
    sentimentData,
    alertRules,
    refreshData,
    isLoading
  } = useNewsSentiment();

  const handleRefresh = () => {
    setRefreshing(true);
    refreshData();

    // Simulate minimum loading time for better UX
    setTimeout(() => {
      setRefreshing(false);
      toast({
        title: "Data refreshed",
        description: "All news and sentiment metrics have been updated from CoinGecko.",
      });
    }, 1500);
  };

  // Combine loading states for UI
  const displayLoading = isLoading || refreshing;

  return (
    <div className="flex-1 flex flex-col min-w-0">
      <HeaderBar
        title="News Sentiment Analysis"
        description="Track market sentiment and news impact across crypto assets"
        onRefresh={handleRefresh}
        isLoading={displayLoading}
      />

      <main className="flex-1 overflow-y-auto p-4 md:p-6 lg:p-8">
        <div className="max-w-screen-2xl mx-auto space-y-6">
          <Card className="animate-scale-in">
            <CardHeader className="pb-2">
              <CardTitle>Market Sentiment Dashboard</CardTitle>
              <CardDescription>
                Analyze cryptocurrency news sentiment from real market data
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="news" className="w-full">
                <TabsList className="grid w-full md:w-auto grid-cols-3 h-auto">
                  <TabsTrigger value="news">News Feed</TabsTrigger>
                  <TabsTrigger value="sentiment">Sentiment Analysis</TabsTrigger>
                  <TabsTrigger value="alerts">Alert Builder</TabsTrigger>
                </TabsList>

                <TabsContent value="news" className="mt-6">
                  <NewsAggregator data={newsData || []} isLoading={displayLoading} />
                </TabsContent>

                <TabsContent value="sentiment" className="mt-6">
                  <SentimentAnalysis data={sentimentData || {
                    overallMarketSentiment: 0,
                    sentimentByAsset: [],
                    topPositiveStories: [],
                    topNegativeStories: [],
                    sentimentTrend: []
                  }} isLoading={displayLoading} />
                </TabsContent>

                <TabsContent value="alerts" className="mt-6">
                  <AlertBuilder rules={alertRules} isLoading={displayLoading} />
                </TabsContent>
              </Tabs>
            </CardContent>
            <CardFooter className="text-xs text-muted-foreground">
              {COINGECKO_ATTRIBUTION}
            </CardFooter>
          </Card>
        </div>
      </main>
    </div>
  );
}
