

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ontent } from "@/components/ui/card";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>cle2, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { <PERSON><PERSON>, AlertTitle, AlertDescription } from "@/components/ui/alert";

interface SecurityRatingCardProps {
  protocol: any | null;
  isLoading: boolean;
}

const SecurityRatingCard = ({ protocol, isLoading }: SecurityRatingCardProps) => {
  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-lg flex items-center gap-1">
          <Gauge className="h-4 w-4" /> Security Rating
        </CardTitle>
      </CardHeader>
      <CardContent>
        {isLoading || !protocol ? (
          <div className="space-y-4">
            <Skeleton className="h-28 w-28 rounded-full mx-auto" />
            <Skeleton className="h-5 w-full" />
            <Skeleton className="h-5 w-2/3 mx-auto" />
          </div>
        ) : (
          <div className="text-center space-y-3">
            <div className="relative inline-block">
              <div className="w-28 h-28 rounded-full border-8 border-primary/20 flex items-center justify-center mx-auto">
                <span className="text-4xl font-bold">
                  {protocol.securityScore}/10
                </span>
              </div>
              <div className={`absolute -top-1 -right-1 p-1 rounded-full ${
                protocol.securityScore >= 8 ? 'bg-green-500' :
                protocol.securityScore >= 6 ? 'bg-yellow-500' : 'bg-red-500'
              }`}>
                {protocol.securityScore >= 8 ? (
                  <CheckCircle2 className="h-5 w-5 text-white" />
                ) : protocol.securityScore >= 6 ? (
                  <AlertTriangle className="h-5 w-5 text-white" />
                ) : (
                  <XCircle className="h-5 w-5 text-white" />
                )}
              </div>
            </div>

            <div>
              <p className="font-medium">
                {protocol.securityScore >= 8 ? 'Excellent Security' :
                 protocol.securityScore >= 6 ? 'Good Security' : 'Needs Improvement'}
              </p>
              <p className="text-sm text-muted-foreground mt-1">
                {protocol.audits?.length || 0} audits ·
                {protocol.bugBounty ? ' Has bug bounty' : ' No bug bounty'}
              </p>
            </div>

            {protocol.exploits && protocol.exploits.length > 0 && (
              <Alert variant="destructive" className="mt-2">
                <AlertTriangle className="h-4 w-4" />
                <AlertTitle>Security Incidents</AlertTitle>
                <AlertDescription>
                  {protocol.exploits.length} past security {protocol.exploits.length === 1 ? 'incident' : 'incidents'} reported
                </AlertDescription>
              </Alert>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default SecurityRatingCard;
