
// Get a fundamental analysis score based on all available metrics
export const calculateFundamentalScore = (
  tokenomics: any, 
  developerData: any, 
  onChainMetrics: any,
  marketData: any
) => {
  // Ensure we have all the required data
  if (!tokenomics || !developerData || !onChainMetrics || !marketData) {
    console.log("Missing data for scoring:", { 
      hasTokenomics: !!tokenomics, 
      hasDeveloperData: !!developerData, 
      hasOnChainMetrics: !!onChainMetrics,
      hasMarketData: !!marketData
    });
    return null;
  }
  
  console.log("Calculating score with data:", {
    tokenomics: JSON.stringify(tokenomics).slice(0, 100) + "...",
    developerData: JSON.stringify(developerData).slice(0, 100) + "...",
    onChainMetrics: JSON.stringify(onChainMetrics).slice(0, 100) + "...",
    marketData: JSON.stringify(marketData).slice(0, 100) + "..."
  });
  
  // Tokenomics score (0-10)
  let tokenomicsScore = 5;
  
  // Reward circulation percentage (more circulating = more decentralized = better)
  if (tokenomics.supply_percentage > 80) tokenomicsScore += 1;
  else if (tokenomics.supply_percentage < 50) tokenomicsScore -= 1;
  
  // Max supply (having a max supply = better tokenomics)
  if (tokenomics.max_supply) tokenomicsScore += 1;
  
  // Market cap to fully diluted ratio (closer to 1 = better)
  if (tokenomics.fully_diluted_valuation) {
    const ratio = tokenomics.market_cap / tokenomics.fully_diluted_valuation;
    if (ratio > 0.9) tokenomicsScore += 2;
    else if (ratio > 0.7) tokenomicsScore += 1;
  }
  
  // Development score (0-10)
  let developmentScore = 5;
  
  if (developerData) {
    // GitHub stats
    if (developerData.forks > 1000) developmentScore += 2;
    else if (developerData.forks > 500) developmentScore += 1;
    
    if (developerData.stars > 5000) developmentScore += 2;
    else if (developerData.stars > 2000) developmentScore += 1;
    
    // Commit activity
    if (developerData.commit_count_4_weeks > 100) developmentScore += 2;
    else if (developerData.commit_count_4_weeks > 50) developmentScore += 1;
    else if (developerData.commit_count_4_weeks < 10) developmentScore -= 1;
  }
  
  // Adoption/utility score (0-10)
  let utilityScore = 5;
  
  // Market cap as proxy for adoption
  if (marketData.market_cap > 10000000000) utilityScore += 2; // >$10B
  else if (marketData.market_cap > 1000000000) utilityScore += 1; // >$1B
  
  // Trading volume as proxy for utility
  const volumeToMarketCapRatio = marketData.total_volume / marketData.market_cap;
  if (volumeToMarketCapRatio > 0.1) utilityScore += 1;
  else if (volumeToMarketCapRatio < 0.01) utilityScore -= 1;
  
  // Cap final scores between 1-10
  tokenomicsScore = Math.max(1, Math.min(10, tokenomicsScore));
  developmentScore = Math.max(1, Math.min(10, developmentScore));
  utilityScore = Math.max(1, Math.min(10, utilityScore));
  
  // Calculate final score (weighted average)
  const finalScore = (
    tokenomicsScore * 0.4 +
    developmentScore * 0.3 +
    utilityScore * 0.3
  ).toFixed(1);
  
  console.log("Calculated scores:", {
    total: parseFloat(finalScore),
    tokenomics: tokenomicsScore,
    development: developmentScore,
    utility: utilityScore
  });
  
  return {
    total: parseFloat(finalScore),
    tokenomics: tokenomicsScore,
    development: developmentScore,
    utility: utilityScore
  };
};
