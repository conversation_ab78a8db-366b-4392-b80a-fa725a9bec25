
import { useState } from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from '@/components/ui/separator';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { ArrowRight, CheckCircle, XCircle, LineChart, TrendingUp, Clock, Shuffle, ShieldAlert, Percent } from 'lucide-react';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from '@/components/ui/progress';

export function CryptoInvestmentStrategies({ loading = false }: { loading?: boolean }) {
  const [selectedTab, setSelectedTab] = useState("overview");
  const [quizStarted, setQuizStarted] = useState(false);
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [score, setScore] = useState(0);
  const [quizCompleted, setQuizCompleted] = useState(false);
  const [showExplanation, setShowExplanation] = useState(false);

  // Portfolio simulator
  const [simulationStep, setSimulationStep] = useState(1);
  const [riskLevel, setRiskLevel] = useState("moderate");
  const [investmentAmount, setInvestmentAmount] = useState(10000);
  const [portfolioAllocation, setPortfolioAllocation] = useState({
    bitcoin: 40,
    ethereum: 30,
    stablecoins: 20,
    altcoins: 10
  });
  const [simulationResults, setSimulationResults] = useState({
    bullish: 0,
    neutral: 0,
    bearish: 0
  });

  // Quiz questions
  const quizQuestions = [
    {
      question: "What is 'dollar-cost averaging' in cryptocurrency investing?",
      options: [
        "Buying cryptocurrency with only US dollars",
        "Investing the same amount at regular intervals regardless of price",
        "Converting all profits to dollars immediately",
        "Selling crypto when it reaches a specific dollar value"
      ],
      correctAnswer: 1,
      explanation: "Dollar-cost averaging is an investment strategy where you invest a fixed amount at regular intervals, regardless of price. This reduces the impact of volatility and eliminates the need to time the market perfectly."
    },
    {
      question: "Which investment approach typically involves holding assets for many years?",
      options: [
        "Day trading",
        "Swing trading",
        "HODLing",
        "Scalping"
      ],
      correctAnswer: 2,
      explanation: "HODLing (a misspelling of 'holding' that became popular in crypto culture) refers to buying and holding cryptocurrency for long periods, typically years, regardless of price fluctuations."
    },
    {
      question: "What is the primary purpose of portfolio diversification?",
      options: [
        "To maximize returns in bull markets",
        "To reduce risk by spreading investments across different assets",
        "To simplify tax reporting",
        "To increase leverage and potential returns"
      ],
      correctAnswer: 1,
      explanation: "Diversification involves spreading investments across various assets to reduce risk. When some assets perform poorly, others might perform well, potentially reducing the overall volatility of the portfolio."
    },
    {
      question: "What is a 'stop-loss order' in cryptocurrency trading?",
      options: [
        "An order to buy more when prices fall below a certain level",
        "An automatic order to sell when price falls to a specified level",
        "A strategy to minimize taxes on crypto gains",
        "A method to predict market bottoms"
      ],
      correctAnswer: 1,
      explanation: "A stop-loss order is an automatic order to sell an asset when its price drops to a certain level, helping investors limit potential losses during market downturns."
    },
    {
      question: "Which of the following is NOT typically considered a fundamental analysis factor for cryptocurrencies?",
      options: [
        "Development team background and experience",
        "Network activity and user adoption",
        "Technical indicators like RSI and MACD",
        "Tokenomics and supply distribution"
      ],
      correctAnswer: 2,
      explanation: "Technical indicators like RSI (Relative Strength Index) and MACD (Moving Average Convergence Divergence) are part of technical analysis, not fundamental analysis. Fundamental analysis focuses on intrinsic factors like team quality, use case, adoption, and tokenomics."
    }
  ];

  const handleQuizStart = () => {
    setQuizStarted(true);
    setCurrentQuestion(0);
    setScore(0);
    setQuizCompleted(false);
    setShowExplanation(false);
    toast.info("Investment Strategies Quiz started! Test your knowledge.");
  };

  const handleAnswerSubmit = (selectedAnswer: number) => {
    const isCorrect = selectedAnswer === quizQuestions[currentQuestion].correctAnswer;
    setShowExplanation(true);

    if (isCorrect) {
      setScore(score + 1);
      toast.success("Correct answer!");
    } else {
      toast.error("Not quite right!");
    }

    // Move to next question or end quiz after delay to show explanation
    setTimeout(() => {
      if (currentQuestion < quizQuestions.length - 1) {
        setCurrentQuestion(currentQuestion + 1);
        setShowExplanation(false);
      } else {
        setQuizCompleted(true);
      }
    }, 3000);
  };

  const handleRiskLevelChange = (level: string) => {
    setRiskLevel(level);

    // Adjust portfolio allocation based on risk level
    if (level === "conservative") {
      setPortfolioAllocation({
        bitcoin: 30,
        ethereum: 20,
        stablecoins: 40,
        altcoins: 10
      });
    } else if (level === "moderate") {
      setPortfolioAllocation({
        bitcoin: 40,
        ethereum: 30,
        stablecoins: 20,
        altcoins: 10
      });
    } else if (level === "aggressive") {
      setPortfolioAllocation({
        bitcoin: 35,
        ethereum: 30,
        stablecoins: 5,
        altcoins: 30
      });
    }
  };

  const handleInvestmentChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(e.target.value);
    if (!isNaN(value) && value >= 1000 && value <= 100000) {
      setInvestmentAmount(value);
    }
  };

  const handleNextSimulationStep = () => {
    if (simulationStep < 3) {
      setSimulationStep(simulationStep + 1);

      // Calculate simulation results in step 2
      if (simulationStep === 2) {
        // Simulate portfolio performance in different market scenarios
        const results = {
          bullish: calculateBullishReturn(),
          neutral: calculateNeutralReturn(),
          bearish: calculateBearishReturn()
        };
        setSimulationResults(results);
      }
    } else {
      // Reset simulator
      setSimulationStep(1);
      setRiskLevel("moderate");
      setInvestmentAmount(10000);
      setPortfolioAllocation({
        bitcoin: 40,
        ethereum: 30,
        stablecoins: 20,
        altcoins: 10
      });
    }
  };

  const calculateBullishReturn = () => {
    // Simplified simulation for educational purposes
    const btcReturn = portfolioAllocation.bitcoin * 2.5; // 150% gain
    const ethReturn = portfolioAllocation.ethereum * 3; // 200% gain
    const stableReturn = portfolioAllocation.stablecoins * 1; // No gain
    const altReturn = portfolioAllocation.altcoins * 4; // 300% gain

    const totalReturn = (btcReturn + ethReturn + stableReturn + altReturn) / 100;
    return investmentAmount * totalReturn;
  };

  const calculateNeutralReturn = () => {
    // Simplified simulation for educational purposes
    const btcReturn = portfolioAllocation.bitcoin * 1.2; // 20% gain
    const ethReturn = portfolioAllocation.ethereum * 1.1; // 10% gain
    const stableReturn = portfolioAllocation.stablecoins * 1.05; // 5% gain (staking)
    const altReturn = portfolioAllocation.altcoins * 0.9; // 10% loss

    const totalReturn = (btcReturn + ethReturn + stableReturn + altReturn) / 100;
    return investmentAmount * totalReturn;
  };

  const calculateBearishReturn = () => {
    // Simplified simulation for educational purposes
    const btcReturn = portfolioAllocation.bitcoin * 0.6; // 40% loss
    const ethReturn = portfolioAllocation.ethereum * 0.5; // 50% loss
    const stableReturn = portfolioAllocation.stablecoins * 1; // No loss
    const altReturn = portfolioAllocation.altcoins * 0.3; // 70% loss

    const totalReturn = (btcReturn + ethReturn + stableReturn + altReturn) / 100;
    return investmentAmount * totalReturn;
  };

  if (loading) {
    return (
      <Card className="animate-pulse">
        <CardHeader className="pb-2">
          <div className="h-5 w-3/4 bg-muted rounded mb-1"></div>
          <div className="h-4 w-full bg-muted rounded"></div>
        </CardHeader>
        <CardContent>
          <div className="h-[400px] bg-muted rounded mb-2"></div>
        </CardContent>
      </Card>
    );
  }

  // Quiz component
  if (quizStarted) {
    return (
      <Card className="max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle>Crypto Investment Strategies Quiz</CardTitle>
          <CardDescription>
            {!quizCompleted
              ? `Question ${currentQuestion + 1} of ${quizQuestions.length}`
              : "Quiz Completed"}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {!quizCompleted ? (
            <div className="space-y-4">
              <div className="text-xl font-medium">{quizQuestions[currentQuestion].question}</div>
              <div className="space-y-2">
                {quizQuestions[currentQuestion].options.map((option, index) => (
                  <Button
                    key={index}
                    variant={showExplanation ? (index === quizQuestions[currentQuestion].correctAnswer ? "default" : "outline") : "outline"}
                    className={`w-full justify-start text-left h-auto py-3 px-4 ${
                      showExplanation && index === quizQuestions[currentQuestion].correctAnswer ? "bg-green-500/20 hover:bg-green-500/20 border-green-500" : ""
                    }`}
                    onClick={() => !showExplanation && handleAnswerSubmit(index)}
                    disabled={showExplanation}
                  >
                    {option}
                    {showExplanation && index === quizQuestions[currentQuestion].correctAnswer && (
                      <CheckCircle className="ml-auto h-4 w-4 text-green-500" />
                    )}
                  </Button>
                ))}
              </div>

              {showExplanation && (
                <div className="p-4 bg-background border rounded-md mt-4">
                  <div className="flex gap-2">
                    <CheckCircle className="text-primary mt-0.5" size={18} />
                    <div>
                      <h4 className="font-medium mb-1">Explanation:</h4>
                      <p className="text-sm">{quizQuestions[currentQuestion].explanation}</p>
                    </div>
                  </div>
                </div>
              )}

              <div className="pt-4">
                <p className="text-muted-foreground text-sm">
                  {!showExplanation ? "Select the best answer. Your score will be calculated at the end." : "Moving to the next question shortly..."}
                </p>
              </div>
            </div>
          ) : (
            <div className="space-y-6 py-4">
              <div className="text-center">
                <div className="text-3xl font-bold mb-2">
                  Your Score: {score}/{quizQuestions.length}
                </div>
                <div className="text-muted-foreground">
                  {score === quizQuestions.length ?
                    "Perfect! You're an investment strategy expert!" :
                    score >= quizQuestions.length / 2 ?
                    "Good job! You have a solid understanding of crypto investment strategies." :
                    "Keep learning! Investment strategies take time to master."}
                </div>
              </div>

              <div className="space-y-4 mt-6">
                <h3 className="font-medium text-lg">Review:</h3>
                {quizQuestions.map((q, idx) => (
                  <div key={idx} className="p-4 border rounded-md">
                    <div className="flex gap-2">
                      <div className="mt-1">
                        {score > idx ? <CheckCircle className="text-green-500" size={18} /> : <XCircle className="text-red-500" size={18} />}
                      </div>
                      <div>
                        <p className="font-medium">{q.question}</p>
                        <p className="text-sm text-muted-foreground mt-1">Correct answer: {q.options[q.correctAnswer]}</p>
                        <p className="text-sm mt-1">{q.explanation}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              <div className="flex justify-center pt-4">
                <Button onClick={() => setQuizStarted(false)} className="mr-2">
                  Return to Lesson
                </Button>
                <Button onClick={handleQuizStart} variant="outline">
                  Retry Quiz
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Crypto Investment Strategies</CardTitle>
          <CardDescription>Learn different approaches to investing in cryptocurrency markets</CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="overview" value={selectedTab} onValueChange={setSelectedTab}>
            <TabsList className="grid grid-cols-3 mb-6">
              <TabsTrigger value="overview">Core Strategies</TabsTrigger>
              <TabsTrigger value="advanced">Advanced Techniques</TabsTrigger>
              <TabsTrigger value="simulator">Portfolio Simulator</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              <section>
                <h3 className="text-lg font-medium mb-2">Investment Approaches</h3>
                <p className="text-muted-foreground mb-4">
                  Cryptocurrency markets offer various investment strategies, each with different risk profiles,
                  time commitments, and potential returns. Understanding these approaches can help you develop
                  a strategy aligned with your financial goals and risk tolerance.
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader className="pb-2">
                      <div className="flex items-center gap-2">
                        <Clock size={18} className="text-primary" />
                        <CardTitle className="text-base">Long-term Investing (HODLing)</CardTitle>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground mb-3">
                        The "buy and hold" strategy involves purchasing cryptocurrencies and holding them for extended periods
                        (months to years), regardless of short-term price volatility. This approach is based on the belief in
                        the long-term value and adoption of crypto technology.
                      </p>
                      <div className="space-y-1 text-sm">
                        <p><span className="font-medium">Risk Level:</span> Moderate</p>
                        <p><span className="font-medium">Time Commitment:</span> Low</p>
                        <p><span className="font-medium">Best For:</span> Investors who believe in crypto's long-term potential</p>
                        <p><span className="font-medium">Key Tip:</span> Focus on projects with strong fundamentals and real-world utility</p>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <div className="flex items-center gap-2">
                        <TrendingUp size={18} className="text-primary" />
                        <CardTitle className="text-base">Dollar-Cost Averaging (DCA)</CardTitle>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground mb-3">
                        This strategy involves investing a fixed amount at regular intervals, regardless of price.
                        By spreading purchases over time, DCA helps mitigate the impact of volatility and eliminates
                        the stress of trying to time the market.
                      </p>
                      <div className="space-y-1 text-sm">
                        <p><span className="font-medium">Risk Level:</span> Low to Moderate</p>
                        <p><span className="font-medium">Time Commitment:</span> Low</p>
                        <p><span className="font-medium">Best For:</span> Beginners and risk-averse investors</p>
                        <p><span className="font-medium">Key Tip:</span> Set up automatic recurring purchases to maintain discipline</p>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <div className="flex items-center gap-2">
                        <Shuffle size={18} className="text-primary" />
                        <CardTitle className="text-base">Diversification</CardTitle>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground mb-3">
                        Diversification involves spreading investments across multiple cryptocurrencies to reduce risk.
                        This can include assets with different use cases, market caps, and risk profiles. The goal is to
                        create a balanced portfolio that can withstand volatility in specific sectors.
                      </p>
                      <div className="space-y-1 text-sm">
                        <p><span className="font-medium">Risk Level:</span> Varies based on selection</p>
                        <p><span className="font-medium">Time Commitment:</span> Moderate</p>
                        <p><span className="font-medium">Best For:</span> All investors</p>
                        <p><span className="font-medium">Key Tip:</span> Include assets with different correlation patterns</p>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <div className="flex items-center gap-2">
                        <LineChart size={18} className="text-primary" />
                        <CardTitle className="text-base">Trading Strategies</CardTitle>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground mb-3">
                        Active trading involves buying and selling cryptocurrencies based on short-term price movements.
                        This can include day trading, swing trading, or position trading, each with different timeframes
                        and techniques.
                      </p>
                      <div className="space-y-1 text-sm">
                        <p><span className="font-medium">Risk Level:</span> High</p>
                        <p><span className="font-medium">Time Commitment:</span> High</p>
                        <p><span className="font-medium">Best For:</span> Experienced investors with technical analysis skills</p>
                        <p><span className="font-medium">Key Tip:</span> Use stop-loss orders to manage risk and protect capital</p>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </section>

              <Separator />

              <section>
                <h3 className="text-lg font-medium mb-2">Risk Management Fundamentals</h3>
                <p className="text-muted-foreground mb-4">
                  Effective risk management is crucial for long-term success in cryptocurrency investing, given the market's
                  high volatility and unique risks.
                </p>

                <div className="space-y-4">
                  <div className="p-4 border rounded-md">
                    <div className="flex items-start gap-3">
                      <span className="p-2 bg-primary/10 rounded-full">
                        <ShieldAlert size={20} className="text-primary" />
                      </span>
                      <div>
                        <h4 className="font-medium">Position Sizing</h4>
                        <p className="text-sm text-muted-foreground">
                          Determine how much of your portfolio to allocate to each cryptocurrency. A common guideline is
                          to limit any single position to 1-5% of your total portfolio, depending on risk tolerance. More
                          speculative assets should generally have smaller allocations.
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="p-4 border rounded-md">
                    <div className="flex items-start gap-3">
                      <span className="p-2 bg-primary/10 rounded-full">
                        <ShieldAlert size={20} className="text-primary" />
                      </span>
                      <div>
                        <h4 className="font-medium">Stop-Loss Orders</h4>
                        <p className="text-sm text-muted-foreground">
                          Use stop-loss orders to automatically sell a cryptocurrency if it falls below a predetermined price.
                          This helps limit potential losses and removes emotional decision-making during market downturns. Common
                          stop-loss levels range from 10-30% below purchase price, depending on volatility and investment timeframe.
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="p-4 border rounded-md">
                    <div className="flex items-start gap-3">
                      <span className="p-2 bg-primary/10 rounded-full">
                        <ShieldAlert size={20} className="text-primary" />
                      </span>
                      <div>
                        <h4 className="font-medium">Taking Profits</h4>
                        <p className="text-sm text-muted-foreground">
                          Establish a profit-taking strategy to lock in gains during market upswings. Consider selling a percentage
                          of your position when it reaches predetermined profit targets. This can include the "take your initial investment
                          off the table" approach, where you sell enough to recover your initial capital when an investment doubles.
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="p-4 border rounded-md">
                    <div className="flex items-start gap-3">
                      <span className="p-2 bg-primary/10 rounded-full">
                        <ShieldAlert size={20} className="text-primary" />
                      </span>
                      <div>
                        <h4 className="font-medium">The 1% Rule</h4>
                        <p className="text-sm text-muted-foreground">
                          Never risk more than 1% of your total portfolio on a single trade. This rule helps ensure that no
                          single loss can significantly impact your overall financial position. For higher-risk cryptocurrencies,
                          consider reducing this percentage even further.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </section>

              <Separator />

              <section>
                <h3 className="text-lg font-medium mb-2">Portfolio Construction</h3>
                <p className="text-muted-foreground mb-4">
                  Building a balanced cryptocurrency portfolio involves combining assets with different risk profiles and
                  use cases. Here are sample allocations based on risk tolerance:
                </p>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">Conservative</CardTitle>
                      <CardDescription>Lower risk, potentially lower returns</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div>
                          <div className="flex justify-between mb-1 text-sm">
                            <span>Bitcoin (BTC)</span>
                            <span>40%</span>
                          </div>
                          <Progress value={40} className="h-2" />
                        </div>
                        <div>
                          <div className="flex justify-between mb-1 text-sm">
                            <span>Ethereum (ETH)</span>
                            <span>20%</span>
                          </div>
                          <Progress value={20} className="h-2" />
                        </div>
                        <div>
                          <div className="flex justify-between mb-1 text-sm">
                            <span>Stablecoins</span>
                            <span>35%</span>
                          </div>
                          <Progress value={35} className="h-2" />
                        </div>
                        <div>
                          <div className="flex justify-between mb-1 text-sm">
                            <span>Large-cap Altcoins</span>
                            <span>5%</span>
                          </div>
                          <Progress value={5} className="h-2" />
                        </div>
                      </div>
                      <p className="text-xs text-muted-foreground mt-4">
                        Focuses on established cryptocurrencies with higher market caps and maintains a significant
                        stablecoin position for stability and opportunity funds.
                      </p>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">Moderate</CardTitle>
                      <CardDescription>Balanced risk and return potential</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div>
                          <div className="flex justify-between mb-1 text-sm">
                            <span>Bitcoin (BTC)</span>
                            <span>30%</span>
                          </div>
                          <Progress value={30} className="h-2" />
                        </div>
                        <div>
                          <div className="flex justify-between mb-1 text-sm">
                            <span>Ethereum (ETH)</span>
                            <span>25%</span>
                          </div>
                          <Progress value={25} className="h-2" />
                        </div>
                        <div>
                          <div className="flex justify-between mb-1 text-sm">
                            <span>Stablecoins</span>
                            <span>20%</span>
                          </div>
                          <Progress value={20} className="h-2" />
                        </div>
                        <div>
                          <div className="flex justify-between mb-1 text-sm">
                            <span>Top 10 Altcoins</span>
                            <span>15%</span>
                          </div>
                          <Progress value={15} className="h-2" />
                        </div>
                        <div>
                          <div className="flex justify-between mb-1 text-sm">
                            <span>Mid-cap Altcoins</span>
                            <span>10%</span>
                          </div>
                          <Progress value={10} className="h-2" />
                        </div>
                      </div>
                      <p className="text-xs text-muted-foreground mt-4">
                        Balances established cryptocurrencies with a selection of promising altcoins while
                        maintaining adequate stablecoin reserves.
                      </p>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">Aggressive</CardTitle>
                      <CardDescription>Higher risk, potentially higher returns</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div>
                          <div className="flex justify-between mb-1 text-sm">
                            <span>Bitcoin (BTC)</span>
                            <span>20%</span>
                          </div>
                          <Progress value={20} className="h-2" />
                        </div>
                        <div>
                          <div className="flex justify-between mb-1 text-sm">
                            <span>Ethereum (ETH)</span>
                            <span>20%</span>
                          </div>
                          <Progress value={20} className="h-2" />
                        </div>
                        <div>
                          <div className="flex justify-between mb-1 text-sm">
                            <span>Stablecoins</span>
                            <span>10%</span>
                          </div>
                          <Progress value={10} className="h-2" />
                        </div>
                        <div>
                          <div className="flex justify-between mb-1 text-sm">
                            <span>Top 10 Altcoins</span>
                            <span>20%</span>
                          </div>
                          <Progress value={20} className="h-2" />
                        </div>
                        <div>
                          <div className="flex justify-between mb-1 text-sm">
                            <span>Mid-cap Altcoins</span>
                            <span>20%</span>
                          </div>
                          <Progress value={20} className="h-2" />
                        </div>
                        <div>
                          <div className="flex justify-between mb-1 text-sm">
                            <span>Small-cap/New Projects</span>
                            <span>10%</span>
                          </div>
                          <Progress value={10} className="h-2" />
                        </div>
                      </div>
                      <p className="text-xs text-muted-foreground mt-4">
                        Allocates significant portions to higher-risk altcoins with potential for substantial
                        returns. Maintains smaller positions in established cryptocurrencies for stability.
                      </p>
                    </CardContent>
                  </Card>
                </div>

                <div className="mt-6 flex justify-center">
                  <Button onClick={handleQuizStart}>
                    Take Investment Strategies Quiz <ArrowRight className="ml-1 h-4 w-4" />
                  </Button>
                </div>
              </section>
            </TabsContent>

            <TabsContent value="advanced" className="space-y-6">
              <section>
                <h3 className="text-lg font-medium mb-2">Analysis Methodologies</h3>
                <p className="text-muted-foreground mb-4">
                  Advanced cryptocurrency investors often combine multiple analysis methodologies to inform their
                  investment decisions.
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">Fundamental Analysis</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground mb-3">
                        Evaluates a cryptocurrency's intrinsic value based on its utility, adoption, team, and tokenomics.
                      </p>
                      <div className="space-y-2 text-sm">
                        <div className="p-3 bg-muted/50 rounded-md">
                          <h5 className="font-medium mb-1">Key Metrics</h5>
                          <ul className="list-disc pl-5 space-y-1">
                            <li>Network activity (active addresses, transaction volume)</li>
                            <li>Developer activity (GitHub commits, updates)</li>
                            <li>Adoption metrics (partnerships, integrations)</li>
                            <li>Tokenomics (supply model, distribution, utility)</li>
                            <li>Team background and execution history</li>
                          </ul>
                        </div>
                        <p className="mt-2">
                          <span className="font-medium">Best For:</span> Long-term investing decisions, project evaluation
                        </p>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">Technical Analysis</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground mb-3">
                        Studies price charts and market statistics to identify patterns and predict future price movements.
                      </p>
                      <div className="space-y-2 text-sm">
                        <div className="p-3 bg-muted/50 rounded-md">
                          <h5 className="font-medium mb-1">Key Indicators</h5>
                          <ul className="list-disc pl-5 space-y-1">
                            <li>Moving Averages (50-day, 200-day)</li>
                            <li>Relative Strength Index (RSI)</li>
                            <li>Moving Average Convergence Divergence (MACD)</li>
                            <li>Support and resistance levels</li>
                            <li>Chart patterns (head and shoulders, flags)</li>
                          </ul>
                        </div>
                        <p className="mt-2">
                          <span className="font-medium">Best For:</span> Entry/exit timing, short-term trading decisions
                        </p>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">On-Chain Analysis</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground mb-3">
                        Examines blockchain data to understand network usage, investor behavior, and market trends.
                      </p>
                      <div className="space-y-2 text-sm">
                        <div className="p-3 bg-muted/50 rounded-md">
                          <h5 className="font-medium mb-1">Key Metrics</h5>
                          <ul className="list-disc pl-5 space-y-1">
                            <li>UTXO Age Distribution</li>
                            <li>Exchange Inflows/Outflows</li>
                            <li>Whale Wallet Movements</li>
                            <li>Mining Difficulty and Hash Rate</li>
                            <li>Network Value to Transactions Ratio (NVT)</li>
                          </ul>
                        </div>
                        <p className="mt-2">
                          <span className="font-medium">Best For:</span> Understanding market sentiment, identifying accumulation/distribution
                        </p>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">Sentiment Analysis</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground mb-3">
                        Analyzes market psychology and social indicators to gauge investor emotions and potential market moves.
                      </p>
                      <div className="space-y-2 text-sm">
                        <div className="p-3 bg-muted/50 rounded-md">
                          <h5 className="font-medium mb-1">Key Indicators</h5>
                          <ul className="list-disc pl-5 space-y-1">
                            <li>Fear & Greed Index</li>
                            <li>Social Media Sentiment Analysis</li>
                            <li>Google Trends Data</li>
                            <li>Funding Rates in Futures Markets</li>
                            <li>Put/Call Ratios in Options Markets</li>
                          </ul>
                        </div>
                        <p className="mt-2">
                          <span className="font-medium">Best For:</span> Contrarian investing, identifying market extremes
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </section>

              <Separator />

              <section>
                <h3 className="text-lg font-medium mb-2">Advanced Investing Strategies</h3>
                <p className="text-muted-foreground mb-4">
                  Experienced cryptocurrency investors may employ these more complex strategies to enhance returns
                  or manage risk. These approaches typically require deeper market knowledge and more active management.
                </p>

                <div className="space-y-4">
                  <div className="p-4 border rounded-md">
                    <div className="flex items-start gap-3">
                      <span className="p-2 bg-primary/10 rounded-full">
                        <Percent size={20} className="text-primary" />
                      </span>
                      <div>
                        <h4 className="font-medium">Yield Farming / Staking</h4>
                        <p className="text-sm text-muted-foreground mb-2">
                          Deploying cryptocurrency assets in DeFi protocols to earn additional returns through interest,
                          fees, or rewards. Includes staking (securing proof-of-stake networks), liquidity provision, and
                          lending on platforms like Aave, Compound, or Uniswap.
                        </p>
                        <div className="space-y-1 text-sm">
                          <p><span className="font-medium">Risk Level:</span> Moderate to High</p>
                          <p><span className="font-medium">Potential Returns:</span> 1-100%+ APY depending on platform and risk</p>
                          <p><span className="font-medium">Main Risks:</span> Smart contract vulnerabilities, impermanent loss, protocol insolvency</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="p-4 border rounded-md">
                    <div className="flex items-start gap-3">
                      <span className="p-2 bg-primary/10 rounded-full">
                        <TrendingUp size={20} className="text-primary" />
                      </span>
                      <div>
                        <h4 className="font-medium">Sector Rotation</h4>
                        <p className="text-sm text-muted-foreground mb-2">
                          Strategically shifting investments between different cryptocurrency sectors (DeFi, NFTs, Layer 1s, etc.)
                          based on market cycles and momentum. This approach capitalizes on the tendency of different crypto sectors
                          to outperform at different times.
                        </p>
                        <div className="space-y-1 text-sm">
                          <p><span className="font-medium">Risk Level:</span> Moderate to High</p>
                          <p><span className="font-medium">Time Commitment:</span> High</p>
                          <p><span className="font-medium">Key Challenge:</span> Identifying sector trends before mainstream recognition</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="p-4 border rounded-md">
                    <div className="flex items-start gap-3">
                      <span className="p-2 bg-primary/10 rounded-full">
                        <LineChart size={20} className="text-primary" />
                      </span>
                      <div>
                        <h4 className="font-medium">Index Investing</h4>
                        <p className="text-sm text-muted-foreground mb-2">
                          Creating or investing in a cryptocurrency index that tracks a basket of assets, similar to stock market
                          index funds. This can be done manually by maintaining a portfolio of multiple cryptocurrencies weighted
                          by market cap, or through crypto index products.
                        </p>
                        <div className="space-y-1 text-sm">
                          <p><span className="font-medium">Risk Level:</span> Low to Moderate</p>
                          <p><span className="font-medium">Time Commitment:</span> Low (with index products) to Moderate (self-managed)</p>
                          <p><span className="font-medium">Examples:</span> DeFi Pulse Index, Bitwise 10 Crypto Index</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="p-4 border rounded-md">
                    <div className="flex items-start gap-3">
                      <span className="p-2 bg-primary/10 rounded-full">
                        <Shuffle size={20} className="text-primary" />
                      </span>
                      <div>
                        <h4 className="font-medium">Hedging Strategies</h4>
                        <p className="text-sm text-muted-foreground mb-2">
                          Using derivatives, short positions, or uncorrelated assets to protect against downside risk in a
                          cryptocurrency portfolio. These strategies aim to reduce volatility and protect capital during market downturns.
                        </p>
                        <div className="space-y-1 text-sm">
                          <p><span className="font-medium">Risk Level:</span> Varies based on implementation</p>
                          <p><span className="font-medium">Time Commitment:</span> High</p>
                          <p><span className="font-medium">Techniques:</span> Options strategies, futures contracts, short positions, stablecoin allocation</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </section>

              <Separator />

              <section>
                <h3 className="text-lg font-medium mb-2">Common Investment Mistakes</h3>
                <p className="text-muted-foreground mb-4">
                  Understanding these common pitfalls can help you avoid costly errors in your cryptocurrency investment journey.
                </p>

                <div className="space-y-3">
                  <div>
                    <h4 className="font-medium">FOMO (Fear of Missing Out)</h4>
                    <p className="text-sm text-muted-foreground">
                      Buying cryptocurrencies that have already experienced significant price increases due to fear of missing
                      further gains. This often leads to buying at market tops and experiencing subsequent losses.
                    </p>
                  </div>

                  <div>
                    <h4 className="font-medium">Overtrading</h4>
                    <p className="text-sm text-muted-foreground">
                      Excessive buying and selling in response to market movements or news. This typically reduces returns
                      through trading fees and taxes while increasing the likelihood of emotional decision-making.
                    </p>
                  </div>

                  <div>
                    <h4 className="font-medium">Lack of Research</h4>
                    <p className="text-sm text-muted-foreground">
                      Investing based solely on social media recommendations or price movements without understanding the
                      fundamental value proposition, technology, or team behind a cryptocurrency.
                    </p>
                  </div>

                  <div>
                    <h4 className="font-medium">Ignoring Security</h4>
                    <p className="text-sm text-muted-foreground">
                      Failing to implement proper security measures for cryptocurrency storage, such as using hardware wallets,
                      enabling two-factor authentication, and maintaining secure backups of private keys and seed phrases.
                    </p>
                  </div>

                  <div>
                    <h4 className="font-medium">Improper Position Sizing</h4>
                    <p className="text-sm text-muted-foreground">
                      Allocating too much capital to a single cryptocurrency or to the crypto asset class as a whole,
                      creating excessive concentration risk in a portfolio.
                    </p>
                  </div>
                </div>
              </section>
            </TabsContent>

            <TabsContent value="simulator" className="space-y-6">
              <section>
                <h3 className="text-lg font-medium mb-2">Portfolio Strategy Simulator</h3>
                <p className="text-muted-foreground mb-4">
                  This simulator will help you understand how different portfolio allocations might perform
                  under various market conditions. Choose your risk level and investment amount to see potential outcomes.
                </p>

                <div className="bg-primary/5 p-6 rounded-lg border">
                  {simulationStep === 1 && (
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className="space-y-6"
                    >
                      <h4 className="font-medium">Step 1: Configure Your Portfolio</h4>

                      <div className="space-y-4">
                        <div>
                          <label className="text-sm font-medium mb-1 block">Risk Level</label>
                          <div className="flex flex-wrap gap-2">
                            <Button
                              variant={riskLevel === "conservative" ? "default" : "outline"}
                              size="sm"
                              onClick={() => handleRiskLevelChange("conservative")}
                            >
                              Conservative
                            </Button>
                            <Button
                              variant={riskLevel === "moderate" ? "default" : "outline"}
                              size="sm"
                              onClick={() => handleRiskLevelChange("moderate")}
                            >
                              Moderate
                            </Button>
                            <Button
                              variant={riskLevel === "aggressive" ? "default" : "outline"}
                              size="sm"
                              onClick={() => handleRiskLevelChange("aggressive")}
                            >
                              Aggressive
                            </Button>
                          </div>
                        </div>

                        <div>
                          <label htmlFor="investmentAmount" className="text-sm font-medium mb-1 block">
                            Investment Amount (USD): ${investmentAmount.toLocaleString()}
                          </label>
                          <input
                            type="range"
                            id="investmentAmount"
                            min="1000"
                            max="100000"
                            step="1000"
                            value={investmentAmount}
                            onChange={handleInvestmentChange}
                            className="w-full"
                          />
                          <div className="flex justify-between text-xs text-muted-foreground">
                            <span>$1,000</span>
                            <span>$100,000</span>
                          </div>
                        </div>

                        <div>
                          <label className="text-sm font-medium mb-2 block">Portfolio Allocation</label>
                          <div className="space-y-3">
                            <div>
                              <div className="flex justify-between mb-1 text-sm">
                                <span>Bitcoin (BTC)</span>
                                <span>{portfolioAllocation.bitcoin}%</span>
                              </div>
                              <Progress value={portfolioAllocation.bitcoin} className="h-2" />
                            </div>
                            <div>
                              <div className="flex justify-between mb-1 text-sm">
                                <span>Ethereum (ETH)</span>
                                <span>{portfolioAllocation.ethereum}%</span>
                              </div>
                              <Progress value={portfolioAllocation.ethereum} className="h-2" />
                            </div>
                            <div>
                              <div className="flex justify-between mb-1 text-sm">
                                <span>Stablecoins</span>
                                <span>{portfolioAllocation.stablecoins}%</span>
                              </div>
                              <Progress value={portfolioAllocation.stablecoins} className="h-2" />
                            </div>
                            <div>
                              <div className="flex justify-between mb-1 text-sm">
                                <span>Altcoins</span>
                                <span>{portfolioAllocation.altcoins}%</span>
                              </div>
                              <Progress value={portfolioAllocation.altcoins} className="h-2" />
                            </div>
                          </div>
                        </div>
                      </div>

                      <Button onClick={handleNextSimulationStep}>
                        Run Simulation <ArrowRight className="ml-1 h-4 w-4" />
                      </Button>
                    </motion.div>
                  )}

                  {simulationStep === 2 && (
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className="space-y-6"
                    >
                      <h4 className="font-medium">Step 2: Simulation Parameters</h4>
                      <p className="text-sm text-muted-foreground">
                        We'll simulate how your portfolio would perform in three different market scenarios over a one-year period.
                      </p>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="p-4 border rounded-md bg-background">
                          <h5 className="font-medium text-center mb-2">Bullish Market</h5>
                          <ul className="text-sm space-y-2">
                            <li>Bitcoin: +150%</li>
                            <li>Ethereum: +200%</li>
                            <li>Stablecoins: 0%</li>
                            <li>Altcoins: +300%</li>
                          </ul>
                        </div>

                        <div className="p-4 border rounded-md bg-background">
                          <h5 className="font-medium text-center mb-2">Neutral Market</h5>
                          <ul className="text-sm space-y-2">
                            <li>Bitcoin: +20%</li>
                            <li>Ethereum: +10%</li>
                            <li>Stablecoins: +5%</li>
                            <li>Altcoins: -10%</li>
                          </ul>
                        </div>

                        <div className="p-4 border rounded-md bg-background">
                          <h5 className="font-medium text-center mb-2">Bearish Market</h5>
                          <ul className="text-sm space-y-2">
                            <li>Bitcoin: -40%</li>
                            <li>Ethereum: -50%</li>
                            <li>Stablecoins: 0%</li>
                            <li>Altcoins: -70%</li>
                          </ul>
                        </div>
                      </div>

                      <p className="text-sm text-muted-foreground">
                        Your portfolio allocation: {riskLevel === "conservative" ? "Conservative" : riskLevel === "moderate" ? "Moderate" : "Aggressive"} strategy
                        with ${investmentAmount.toLocaleString()} initial investment.
                      </p>

                      <Button onClick={handleNextSimulationStep}>
                        View Results <ArrowRight className="ml-1 h-4 w-4" />
                      </Button>
                    </motion.div>
                  )}

                  {simulationStep === 3 && (
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className="space-y-6"
                    >
                      <h4 className="font-medium">Step 3: Simulation Results</h4>
                      <p className="text-sm text-muted-foreground">
                        Here's how your {riskLevel} portfolio with ${investmentAmount.toLocaleString()} would perform in different market conditions.
                      </p>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <Card>
                          <CardHeader className="pb-2">
                            <CardTitle className="text-base text-center">Bullish Market</CardTitle>
                          </CardHeader>
                          <CardContent className="text-center">
                            <p className="text-3xl font-bold text-green-500">
                              ${Math.round(simulationResults.bullish).toLocaleString()}
                            </p>
                            <p className="text-sm text-muted-foreground">
                              {Math.round((simulationResults.bullish - investmentAmount) / investmentAmount * 100)}% return
                            </p>
                            <p className="text-sm mt-2">
                              Profit: ${Math.round(simulationResults.bullish - investmentAmount).toLocaleString()}
                            </p>
                          </CardContent>
                        </Card>

                        <Card>
                          <CardHeader className="pb-2">
                            <CardTitle className="text-base text-center">Neutral Market</CardTitle>
                          </CardHeader>
                          <CardContent className="text-center">
                            <p className={`text-3xl font-bold ${simulationResults.neutral > investmentAmount ? "text-green-500" : simulationResults.neutral < investmentAmount ? "text-red-500" : "text-yellow-500"}`}>
                              ${Math.round(simulationResults.neutral).toLocaleString()}
                            </p>
                            <p className="text-sm text-muted-foreground">
                              {Math.round((simulationResults.neutral - investmentAmount) / investmentAmount * 100)}% return
                            </p>
                            <p className="text-sm mt-2">
                              {simulationResults.neutral > investmentAmount ? "Profit" : "Loss"}: ${Math.abs(Math.round(simulationResults.neutral - investmentAmount)).toLocaleString()}
                            </p>
                          </CardContent>
                        </Card>

                        <Card>
                          <CardHeader className="pb-2">
                            <CardTitle className="text-base text-center">Bearish Market</CardTitle>
                          </CardHeader>
                          <CardContent className="text-center">
                            <p className="text-3xl font-bold text-red-500">
                              ${Math.round(simulationResults.bearish).toLocaleString()}
                            </p>
                            <p className="text-sm text-muted-foreground">
                              {Math.round((simulationResults.bearish - investmentAmount) / investmentAmount * 100)}% return
                            </p>
                            <p className="text-sm mt-2">
                              Loss: ${Math.abs(Math.round(simulationResults.bearish - investmentAmount)).toLocaleString()}
                            </p>
                          </CardContent>
                        </Card>
                      </div>

                      <div className="p-4 border rounded-md bg-background">
                        <h5 className="font-medium mb-2">Analysis</h5>
                        <p className="text-sm text-muted-foreground">
                          {riskLevel === "conservative" ? (
                            "Your conservative portfolio shows more stability in bearish conditions due to higher stablecoin allocation, but offers lower returns in bullish markets. This approach is suitable for investors with lower risk tolerance or shorter time horizons."
                          ) : riskLevel === "moderate" ? (
                            "Your moderate portfolio balances risk and potential returns, showing decent performance in bullish markets while limiting downside in bearish conditions. This approach works well for most investors with medium-term time horizons."
                          ) : (
                            "Your aggressive portfolio demonstrates significant upside potential in bullish markets but also substantial losses in bearish conditions. This approach is most suitable for investors with high risk tolerance and long time horizons who can withstand significant volatility."
                          )}
                        </p>
                        <div className="mt-3 text-sm">
                          <p><span className="font-medium">Key Observation:</span> Note how the stablecoin allocation serves as a buffer during market downturns. Higher stablecoin allocations reduce potential returns in bull markets but provide stability in bear markets.</p>
                        </div>
                      </div>

                      <Button onClick={handleNextSimulationStep}>
                        Try Different Allocation <ArrowRight className="ml-1 h-4 w-4" />
                      </Button>
                    </motion.div>
                  )}
                </div>
              </section>

              <Separator />

              <section>
                <h3 className="text-lg font-medium mb-2">Key Investment Principles</h3>
                <p className="text-muted-foreground mb-4">
                  Regardless of your chosen strategy, these timeless investment principles can improve your results
                  in cryptocurrency markets.
                </p>

                <div className="space-y-3">
                  <div>
                    <h4 className="font-medium">Only Invest What You Can Afford to Lose</h4>
                    <p className="text-sm text-muted-foreground">
                      Cryptocurrency remains a high-risk asset class. Never invest emergency funds or money needed for
                      essential expenses.
                    </p>
                  </div>

                  <div>
                    <h4 className="font-medium">Focus on Long-Term Value</h4>
                    <p className="text-sm text-muted-foreground">
                      Prioritize projects with strong fundamentals, real-world utility, and active development rather
                      than short-term price movements.
                    </p>
                  </div>

                  <div>
                    <h4 className="font-medium">Maintain a Balanced Portfolio</h4>
                    <p className="text-sm text-muted-foreground">
                      Diversify not just within crypto but across multiple asset classes, including traditional investments
                      like stocks, bonds, and real estate.
                    </p>
                  </div>

                  <div>
                    <h4 className="font-medium">Automate Emotional Decisions</h4>
                    <p className="text-sm text-muted-foreground">
                      Use tools like dollar-cost averaging, predetermined buy/sell levels, and portfolio rebalancing to
                      reduce emotional decision-making.
                    </p>
                  </div>

                  <div>
                    <h4 className="font-medium">Stay Educated</h4>
                    <p className="text-sm text-muted-foreground">
                      The cryptocurrency market evolves rapidly. Continuously learn about new developments, technologies,
                      and regulatory changes.
                    </p>
                  </div>
                </div>

                <div className="mt-6 flex justify-center">
                  <Button onClick={handleQuizStart}>
                    Take Investment Strategies Quiz <ArrowRight className="ml-1 h-4 w-4" />
                  </Button>
                </div>
              </section>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
