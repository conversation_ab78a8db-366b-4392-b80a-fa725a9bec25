
import { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import type { AssetScore } from "@/types/rating";
import { RatingTableSkeleton } from "./rating/RatingTableSkeleton";
import { SortableColumnHeader } from "./rating/SortableColumnHeader";
import { AssetTableRow } from "./rating/AssetTableRow";

interface RatingTableProps {
  assets: AssetScore[];
  isLoading?: boolean;
}

export function RatingTable({ assets, isLoading = false }: RatingTableProps) {
  const [sortBy, setSortBy] = useState<keyof AssetScore | keyof AssetScore['scores'] | null>("overallScore");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");

  const handleSort = (column: typeof sortBy) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      setSortBy(column);
      setSortOrder("desc");
    }
  };

  const sortedAssets = [...assets].sort((a, b) => {
    if (!sortBy) return 0;

    let aValue, bValue;

    if (sortBy === 'overallScore' || typeof a[sortBy as keyof AssetScore] !== 'undefined') {
      aValue = a[sortBy as keyof AssetScore];
      bValue = b[sortBy as keyof AssetScore];
    } else {
      // Handle nested score properties
      aValue = a.scores[sortBy as keyof AssetScore['scores']];
      bValue = b.scores[sortBy as keyof AssetScore['scores']];
    }

    if (typeof aValue === 'number' && typeof bValue === 'number') {
      return sortOrder === "asc" ? aValue - bValue : bValue - aValue;
    }

    return 0;
  });

  // Render loading skeleton
  if (isLoading) {
    return <RatingTableSkeleton />;
  }

  return (
    <div className="rounded-lg border border-border bg-card p-5 gradient-border animate-scale-in">
      <div className="mb-4">
        <h3 className="text-lg font-semibold">Asset Ratings</h3>
        <p className="text-sm text-muted-foreground">
          Comprehensive growth potential scores across multiple factors
        </p>
      </div>

      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Asset</TableHead>
              <TableHead>
                <SortableColumnHeader label="Price" onClick={() => handleSort("price")} />
              </TableHead>
              <TableHead>
                <SortableColumnHeader label="24h Change" onClick={() => handleSort("change24h")} />
              </TableHead>
              <TableHead>
                <SortableColumnHeader label="Technical" onClick={() => handleSort("technical")} />
              </TableHead>
              <TableHead>
                <SortableColumnHeader label="On-Chain" onClick={() => handleSort("onChain")} />
              </TableHead>
              <TableHead>
                <SortableColumnHeader label="Social" onClick={() => handleSort("social")} />
              </TableHead>
              <TableHead>
                <SortableColumnHeader label="Fundamental" onClick={() => handleSort("fundamental")} />
              </TableHead>
              <TableHead>
                <SortableColumnHeader
                  label="Overall Score"
                  onClick={() => handleSort("overallScore")}
                  isBold={true}
                />
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {sortedAssets.map((asset) => (
              <AssetTableRow key={asset.id} asset={asset} />
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
