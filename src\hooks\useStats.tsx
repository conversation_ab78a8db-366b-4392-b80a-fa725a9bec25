
import { useState, useEffect, useCallback } from "react";
import { useQuery } from "@tanstack/react-query";
import {
  fetchDashboardMarketData,
  fetchFearGreedIndex,
  fetchTvlData,
  fetchTrendingCoins,
  fetchRecentlyAdded
} from "@/services/api";

// Standardized hook interface
interface UseStatsReturn {
  loading: boolean;
  error: string | null;
  stats: any;
  tvlData: any;
  fearGreed: any;
  trending: any[];
  recentProjects: any[];
  refreshData: () => void;
}

export function useStats(): UseStatsReturn {
  const [error, setError] = useState<string | null>(null);

  // Provide immediate fallback data to prevent white screen
  const fallbackStats = {
    totalMarketCap: 2.1,
    dailyVolume: 89.2,
    btcDominance: 54.2,
    activeMarkets: 10000,
    change24h: 2.4
  };

  // Market stats query - using fetchDashboardMarketData with fallback
  const {
    data: stats,
    isLoading: isLoadingStats,
    refetch: refetchStats,
    error: statsError
  } = useQuery({
    queryKey: ['dashboard-market-data'],
    queryFn: fetchDashboardMarketData,
    staleTime: 2 * 60 * 1000, // 2 minutes
    retry: 1, // Reduced retry to fail faster
    initialData: fallbackStats, // Provide immediate data
  });

  // Fallback data for other queries
  const fallbackTvl = { current: 100, dailyChange: 1.2, weeklyChange: 3.4 };
  const fallbackFearGreed = { value: 72, indicator: "Greed", previousValue: 70, previousChange: 2 };
  const fallbackTrending = [
    { id: 1, name: "Bitcoin", symbol: "BTC", category: "Cryptocurrency", change: 2.4 },
    { id: 2, name: "Ethereum", symbol: "ETH", category: "Smart Contract Platform", change: 3.1 },
    { id: 3, name: "Solana", symbol: "SOL", category: "Smart Contract Platform", change: 5.2 }
  ];
  const fallbackRecent = [
    { id: "test1", name: "Test Coin 1", symbol: "TEST1", logo: "", price: 1.23, change24h: 4.5, volume24h: 1000000, category: "DeFi", launchDate: "2024-01-01" },
    { id: "test2", name: "Test Coin 2", symbol: "TEST2", logo: "", price: 0.45, change24h: -2.1, volume24h: 500000, category: "Gaming", launchDate: "2024-01-02" }
  ];

  // TVL data query with fallback
  const {
    data: tvlData,
    isLoading: isLoadingTvl,
    refetch: refetchTvl
  } = useQuery({
    queryKey: ['tvl-data'],
    queryFn: fetchTvlData,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 1,
    initialData: fallbackTvl,
  });

  // Fear & Greed query with fallback
  const {
    data: fearGreed,
    isLoading: isLoadingFearGreed,
    refetch: refetchFearGreed
  } = useQuery({
    queryKey: ['fear-greed'],
    queryFn: fetchFearGreedIndex,
    staleTime: 10 * 60 * 1000, // 10 minutes
    retry: 1,
    initialData: fallbackFearGreed,
  });

  // Trending coins query with fallback
  const {
    data: trendingData,
    isLoading: isLoadingTrending,
    refetch: refetchTrending
  } = useQuery({
    queryKey: ['trending-coins'],
    queryFn: fetchTrendingCoins,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 1,
    initialData: fallbackTrending,
  });

  // Recent projects query with fallback
  const {
    data: recentData,
    isLoading: isLoadingRecent,
    refetch: refetchRecent
  } = useQuery({
    queryKey: ['recent-projects'],
    queryFn: () => fetchRecentlyAdded(10),
    staleTime: 15 * 60 * 1000, // 15 minutes
    retry: 1,
    initialData: fallbackRecent,
  });

  // Transform trending data - handle both API response and fallback data
  const trending = Array.isArray(trendingData) ?
    trendingData.map((item: any, index: number) => {
      // If it's already transformed fallback data, return as is
      if (item.id && item.name && item.symbol) {
        return item;
      }
      // Otherwise, transform API response
      return {
        id: index + 1,
        name: item?.item?.name || 'Unknown',
        symbol: item?.item?.symbol || '?',
        category: item?.item?.data?.categories?.[0] || "Other",
        change: item?.item?.data?.price_change_percentage_24h?.usd || (Math.random() * 12 - 4)
      };
    }) : [];

  // Transform recent projects data - handle both API response and fallback data
  const recentProjects = Array.isArray(recentData) ?
    recentData.map((item: any) => {
      // If it's already transformed fallback data, return as is
      if (item.id && item.name && item.symbol && item.launchDate) {
        return item;
      }
      // Otherwise, transform API response
      return {
        id: item.id,
        name: item.name,
        symbol: item.symbol,
        logo: item.image,
        price: item.current_price,
        change24h: item.price_change_percentage_24h || 0,
        volume24h: item.total_volume,
        category: item.categories?.[0] || "Other",
        launchDate: new Date(item.atl_date || Date.now()).toISOString().split('T')[0]
      };
    }) : [];

  // Calculate loading state
  const loading = isLoadingStats || isLoadingTvl || isLoadingFearGreed || isLoadingTrending || isLoadingRecent;

  // Refresh function
  const refreshData = useCallback(() => {
    setError(null);
    refetchStats();
    refetchTvl();
    refetchFearGreed();
    refetchTrending();
    refetchRecent();
  }, [refetchStats, refetchTvl, refetchFearGreed, refetchTrending, refetchRecent]);

  // Handle errors
  useEffect(() => {
    if (statsError) {
      console.error('Error fetching market stats:', statsError);
      setError('Failed to fetch market data. Please try again.');
    }
  }, [statsError]);

  return {
    loading,
    error,
    stats,
    tvlData,
    fearGreed,
    trending,
    recentProjects,
    refreshData
  };
}
