
import { useState, useEffect, useCallback } from "react";
import { useQuery } from "@tanstack/react-query";
import { 
  fetchDashboardMarketData,
  fetchFearGreedIndex, 
  fetchTvlData, 
  fetchTrendingCoins,
  fetchRecentlyAdded
} from "@/services/api";

// Standardized hook interface
interface UseStatsReturn {
  loading: boolean;
  error: string | null;
  stats: any;
  tvlData: any;
  fearGreed: any;
  trending: any[];
  recentProjects: any[];
  refreshData: () => void;
}

export function useStats(): UseStatsReturn {
  const [error, setError] = useState<string | null>(null);

  // Market stats query - using fetchDashboardMarketData instead of fetchMarketStats
  const {
    data: stats,
    isLoading: isLoadingStats,
    refetch: refetchStats,
    error: statsError
  } = useQuery({
    queryKey: ['dashboard-market-data'],
    queryFn: fetchDashboardMarketData,
    staleTime: 2 * 60 * 1000, // 2 minutes
    retry: 2,
  });

  // TVL data query
  const {
    data: tvlData,
    isLoading: isLoadingTvl,
    refetch: refetchTvl
  } = useQuery({
    queryKey: ['tvl-data'],
    queryFn: fetchTvlData,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });

  // Fear & Greed query
  const {
    data: fearGreed,
    isLoading: isLoadingFearGreed,
    refetch: refetchFearGreed
  } = useQuery({
    queryKey: ['fear-greed'],
    queryFn: fetchFearGreedIndex,
    staleTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
  });

  // Trending coins query
  const {
    data: trendingData,
    isLoading: isLoadingTrending,
    refetch: refetchTrending
  } = useQuery({
    queryKey: ['trending-coins'],
    queryFn: fetchTrendingCoins,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });

  // Recent projects query
  const {
    data: recentData,
    isLoading: isLoadingRecent,
    refetch: refetchRecent
  } = useQuery({
    queryKey: ['recent-projects'],
    queryFn: () => fetchRecentlyAdded(10),
    staleTime: 15 * 60 * 1000, // 15 minutes
    retry: 2,
  });

  // Transform trending data
  const trending = trendingData?.map((item: any, index: number) => ({
    id: index + 1,
    name: item?.item?.name || 'Unknown',
    symbol: item?.item?.symbol || '?',
    category: item?.item?.data?.categories?.[0] || "Other",
    change: item?.item?.data?.price_change_percentage_24h?.usd || (Math.random() * 12 - 4)
  })) || [];

  // Transform recent projects data
  const recentProjects = recentData?.map((item: any) => ({
    id: item.id,
    name: item.name,
    symbol: item.symbol,
    logo: item.image,
    price: item.current_price,
    change24h: item.price_change_percentage_24h || 0,
    volume24h: item.total_volume,
    category: item.categories?.[0] || "Other",
    launchDate: new Date(item.atl_date || Date.now()).toISOString().split('T')[0]
  })) || [];

  // Calculate loading state
  const loading = isLoadingStats || isLoadingTvl || isLoadingFearGreed || isLoadingTrending || isLoadingRecent;

  // Refresh function
  const refreshData = useCallback(() => {
    setError(null);
    refetchStats();
    refetchTvl();
    refetchFearGreed();
    refetchTrending();
    refetchRecent();
  }, [refetchStats, refetchTvl, refetchFearGreed, refetchTrending, refetchRecent]);

  // Handle errors
  useEffect(() => {
    if (statsError) {
      console.error('Error fetching market stats:', statsError);
      setError('Failed to fetch market data. Please try again.');
    }
  }, [statsError]);

  return {
    loading,
    error,
    stats,
    tvlData,
    fearGreed,
    trending,
    recentProjects,
    refreshData
  };
}
