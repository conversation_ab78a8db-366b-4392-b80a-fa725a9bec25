
import { ReactNode } from 'react';
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';

interface AuthTabsProps {
  value: 'sign_in' | 'sign_up';
  onValueChange: (value: string) => void;
  signInContent: ReactNode;
  signUpContent: ReactNode;
}

export function AuthTabs({ value, onValueChange, signInContent, signUpContent }: AuthTabsProps) {
  return (
    <Tabs value={value} onValueChange={(v) => onValueChange(v)} className="w-full">
      <TabsList className="grid w-full grid-cols-2 mb-6">
        <TabsTrigger value="sign_in">Sign In</TabsTrigger>
        <TabsTrigger value="sign_up">Sign Up</TabsTrigger>
      </TabsList>

      <TabsContent value="sign_in">{signInContent}</TabsContent>
      <TabsContent value="sign_up">{signUpContent}</TabsContent>
    </Tabs>
  );
}
