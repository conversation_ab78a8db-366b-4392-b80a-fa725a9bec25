
import { useState, useEffect } from "react";
import { useLocation, Link } from "react-router-dom";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import { useIsMobile } from "@/hooks/use-mobile";
import { NavigationSection } from "./NavigationSection";
import { marketLinks, advancedAnalysisLinks, otherLinks } from "./navigationData";
import { Home } from "lucide-react";

export function Navigation() {
  const { pathname } = useLocation();
  const [mounted, setMounted] = useState(false);
  const [collapsed, setCollapsed] = useState(false);
  const isMobile = useIsMobile();

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (isMobile && !collapsed) {
      setCollapsed(true);
    }
  }, [isMobile, pathname, collapsed]);

  if (!mounted) {
    return null;
  }

  return (
    <div className="h-screen border-r bg-card">
      <ScrollArea className="h-full py-2">
        {collapsed && (
          <div className="flex justify-center mb-4">
            <Link to="/">
              <Button variant="ghost" size="sm" className="w-10 h-10 p-0">
                <Home className="h-5 w-5" />
              </Button>
            </Link>
          </div>
        )}
        <div className="flex flex-col gap-2 p-2">
          <NavigationSection
            title="Market Analysis"
            links={marketLinks}
            pathname={pathname}
            collapsed={collapsed}
          />

          <Separator />

          <NavigationSection
            title="Advanced Analysis"
            links={advancedAnalysisLinks}
            pathname={pathname}
            collapsed={collapsed}
          />

          <Separator />

          <NavigationSection
            title="Other"
            links={otherLinks}
            pathname={pathname}
            collapsed={collapsed}
          />
        </div>
      </ScrollArea>
    </div>
  );
}
