
import { coinGeckoAxios, handleApiError, cacheResponse } from "../coinGeckoClient";
import { HistoricalDataPoint } from "@/hooks/useFundamentalAnalysis";

// Fetch historical price and volume data for a token
export const fetchTokenHistory = async (coinId: string, days = 90): Promise<HistoricalDataPoint[]> => {
  try {
    const response = await coinGeckoAxios.get(`/coins/${coinId}/market_chart`, {
      params: {
        vs_currency: 'usd',
        days: days,
        interval: days > 90 ? 'daily' : undefined
      }
    });
    
    // Transform data to our format
    const { prices, total_volumes } = response.data;
    
    const historyData = prices.map((pricePoint: [number, number], index: number) => {
      // Find matching volume data point
      const volumeData = total_volumes.find((volumePoint: [number, number]) => 
        Math.abs(volumePoint[0] - pricePoint[0]) < 3600000  // Within an hour
      );
      
      return {
        timestamp: pricePoint[0],
        price: pricePoint[1],
        volume: volumeData ? volumeData[1] : 0
      };
    });
    
    // Sample data to reduce points if too many
    let sampledData = historyData;
    if (historyData.length > 90) {
      // Sample to ~90 points
      const sampleRate = Math.ceil(historyData.length / 90);
      sampledData = historyData.filter((_, index) => index % sampleRate === 0);
    }
    
    cacheResponse(`history_${coinId}_${days}`, sampledData);
    return sampledData;
  } catch (error) {
    console.error("Error fetching token history:", error);
    return handleApiError(error, {
      key: `history_${coinId}_${days}`,
      data: []
    });
  }
};
