
import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { NetworkActivityData } from "@/hooks/useOnChainAnalytics";
import { TrendingUp, TrendingDown, BarChart3, Network, Database } from "lucide-react";
import {
  ResponsiveContainer,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  AreaChart,
  Area
} from "recharts";

interface NetworkActivitySectionProps {
  data: NetworkActivityData;
  isLoading: boolean;
}

export function NetworkActivitySection({ data, isLoading }: NetworkActivitySectionProps) {
  const formatNumber = (value: number) => {
    if (value >= 1000000) {
      return `${(value / 1000000).toFixed(2)}M`;
    } else if (value >= 1000) {
      return `${(value / 1000).toFixed(1)}K`;
    }
    return value.toString();
  };

  const formatChange = (value: number) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(1)}%`;
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div className="space-y-1">
              <CardTitle className="text-sm font-medium">Daily Transactions</CardTitle>
              <CardDescription>Last 24 hours</CardDescription>
            </div>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(data.transactions.daily)}</div>
            <div className={`text-xs ${data.transactions.change >= 0 ? 'text-crypto-positive' : 'text-crypto-negative'} flex items-center mt-1`}>
              {data.transactions.change >= 0 ? <TrendingUp className="mr-1 h-3 w-3" /> : <TrendingDown className="mr-1 h-3 w-3" />}
              {formatChange(data.transactions.change)} from last week
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div className="space-y-1">
              <CardTitle className="text-sm font-medium">Active Addresses</CardTitle>
              <CardDescription>Unique addresses</CardDescription>
            </div>
            <Network className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(data.activeAddresses.daily)}</div>
            <div className={`text-xs ${data.activeAddresses.change >= 0 ? 'text-crypto-positive' : 'text-crypto-negative'} flex items-center mt-1`}>
              {data.activeAddresses.change >= 0 ? <TrendingUp className="mr-1 h-3 w-3" /> : <TrendingDown className="mr-1 h-3 w-3" />}
              {formatChange(data.activeAddresses.change)} from last week
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div className="space-y-1">
              <CardTitle className="text-sm font-medium">Gas Used</CardTitle>
              <CardDescription>Network fees</CardDescription>
            </div>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(data.gasUsed.daily)}</div>
            <div className={`text-xs ${data.gasUsed.change >= 0 ? 'text-crypto-positive' : 'text-crypto-negative'} flex items-center mt-1`}>
              {data.gasUsed.change >= 0 ? <TrendingUp className="mr-1 h-3 w-3" /> : <TrendingDown className="mr-1 h-3 w-3" />}
              {formatChange(data.gasUsed.change)} from last week
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div className="space-y-1">
              <CardTitle className="text-sm font-medium">Block Height</CardTitle>
              <CardDescription>Current block</CardDescription>
            </div>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.blockHeight.toLocaleString()}</div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="col-span-1">
          <CardHeader>
            <CardTitle>Transaction Activity</CardTitle>
            <CardDescription>Daily transactions over the past 14 days</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              {isLoading ? (
                <div className="h-full flex items-center justify-center">
                  <p className="text-muted-foreground">Loading data...</p>
                </div>
              ) : (
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart data={data.transactions.history}>
                    <defs>
                      <linearGradient id="transactionGradient" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="rgb(var(--chart-1))" stopOpacity={0.8}/>
                        <stop offset="95%" stopColor="rgb(var(--chart-1))" stopOpacity={0.1}/>
                      </linearGradient>
                    </defs>
                    <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                    <XAxis dataKey="date" />
                    <YAxis tickFormatter={formatNumber} />
                    <Tooltip formatter={(value) => [formatNumber(value as number), "Transactions"]} />
                    <Area
                      type="monotone"
                      dataKey="count"
                      stroke="rgb(var(--chart-1))"
                      fillOpacity={1}
                      fill="url(#transactionGradient)"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              )}
            </div>
          </CardContent>
        </Card>

        <Card className="col-span-1">
          <CardHeader>
            <CardTitle>Active Addresses</CardTitle>
            <CardDescription>Daily active addresses over the past 14 days</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              {isLoading ? (
                <div className="h-full flex items-center justify-center">
                  <p className="text-muted-foreground">Loading data...</p>
                </div>
              ) : (
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={data.activeAddresses.history}>
                    <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                    <XAxis dataKey="date" />
                    <YAxis tickFormatter={formatNumber} />
                    <Tooltip formatter={(value) => [formatNumber(value as number), "Active Addresses"]} />
                    <Legend />
                    <Line
                      type="monotone"
                      dataKey="count"
                      stroke="rgb(var(--crypto-positive))"
                      activeDot={{ r: 8 }}
                      strokeWidth={2}
                    />
                  </LineChart>
                </ResponsiveContainer>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
