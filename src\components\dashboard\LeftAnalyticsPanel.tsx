
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { RatingTable } from "@/components/RatingTable";
import { GaugeChart } from "@/components/GaugeChart";
import { FearGreedIndex } from "@/components/FearGreedIndex";

interface LeftAnalyticsPanelProps {
  ratingAssets: any;
  ratingLoading: boolean;
  tvlData: any;
  fearGreed: any;
}

export function LeftAnalyticsPanel({
  ratingAssets,
  ratingLoading,
  tvlData,
  fearGreed
}: LeftAnalyticsPanelProps) {
  return (
    <>
      {/* Professional data grid */}
      <Card className="bg-card border-border">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-foreground text-lg font-semibold">AI-Powered Asset Analysis</CardTitle>
            <Badge variant="outline" className="border-orange-500 text-orange-500">
              REAL-TIME
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <RatingTable assets={ratingAssets} isLoading={ratingLoading} />
        </CardContent>
      </Card>

      {/* Advanced charts section */}
      <div className="grid grid-cols-2 gap-6">
        <Card className="bg-card border-border">
          <CardHeader className="pb-3">
            <CardTitle className="text-foreground text-sm font-semibold uppercase tracking-wide">
              Total Value Locked
            </CardTitle>
          </CardHeader>
          <CardContent>
            {tvlData && (
              <GaugeChart
                value={tvlData.current * 1e9}
                dailyChange={tvlData.dailyChange || 2.1}
                weeklyChange={tvlData.weeklyChange || 5.8}
              />
            )}
          </CardContent>
        </Card>

        <Card className="bg-card border-border">
          <CardHeader className="pb-3">
            <CardTitle className="text-foreground text-sm font-semibold uppercase tracking-wide">
              Market Sentiment
            </CardTitle>
          </CardHeader>
          <CardContent>
            {fearGreed && (
              <FearGreedIndex
                value={fearGreed.value}
                indicator={fearGreed.indicator}
                previousValue={fearGreed.previousValue}
                previousChange={fearGreed.previousChange}
              />
            )}
          </CardContent>
        </Card>
      </div>
    </>
  );
}
