
import { coinGeckoAxios, cacheResponse, handleApiError } from "../coinGeckoClient";
import { LiquidityPool } from "./types";

// Fetch liquidity pool data for analysis
export const fetchLiquidityPoolData = async (): Promise<LiquidityPool[]> => {
  try {
    // In a real app, this would call an API like DefiLlama or Covalent
    // For now, we'll simulate with realistic data
    
    // Get top tokens from CoinGecko for pair generation
    const topTokensResponse = await coinGeckoAxios.get("/coins/markets", {
      params: {
        vs_currency: 'usd',
        order: 'market_cap_desc',
        per_page: 20,
        page: 1,
        sparkline: false
      }
    });
    
    const topTokens = topTokensResponse.data.slice(0, 10);
    
    // Generate realistic liquidity pool data
    const liquidityPools: LiquidityPool[] = [];
    const protocols = ['Uniswap', 'Curve', 'Balancer', 'SushiSwap', 'PancakeSwap'];
    
    // Helper for random historic data
    const generateHistoricalData = (baseApy: number, baseTvl: number) => {
      const days = 30;
      const data = [];
      
      for (let i = 0; i < days; i++) {
        const date = new Date();
        date.setDate(date.getDate() - (days - i));
        
        // Add some randomness to make realistic trends
        const tvlVariation = (Math.random() * 0.1 - 0.05) * baseTvl;
        const apyVariation = (Math.random() * 0.3 - 0.15) * baseApy;
        const volumeBase = baseTvl * (0.05 + Math.random() * 0.15);
        
        data.push({
          date: date.toISOString().split('T')[0],
          tvl: baseTvl + tvlVariation * (i / days), // Trending up slightly
          volume: volumeBase * (1 + Math.sin(i / 5) * 0.3), // Oscillating volume
          apy: Math.max(0, baseApy + apyVariation * (1 - i / days)) // Trending down slightly
        });
      }
      
      return data;
    };
    
    // Generate pools combining various tokens
    for (let i = 0; i < 12; i++) {
      const token0Index = i % topTokens.length;
      const token1Index = (i + Math.floor(i / 2) + 1) % topTokens.length;
      
      // Skip if same token
      if (token0Index === token1Index) continue;
      
      const token0 = topTokens[token0Index];
      const token1 = topTokens[token1Index];
      const protocol = protocols[i % protocols.length];
      
      // Calculate realistic TVL and APY based on token market caps
      const tvl = (token0.market_cap * 0.001 * (1 + Math.random())) / 1000000;
      const baseApy = 5 + Math.random() * 20; // Between 5% and 25%
      const volume24h = tvl * (0.1 + Math.random() * 0.3); // 10-40% of TVL
      
      // Calculate IL risk (higher for more volatile pairs)
      const volatilityFactor = (token0.price_change_percentage_24h + token1.price_change_percentage_24h) / 2;
      const ilRisk = Math.abs(volatilityFactor) / 5; // Scale to 1-10
      
      // Simulate impermanent loss based on price movements
      const impermanentLoss7d = Math.abs(volatilityFactor) * 0.02;
      const impermanentLoss30d = Math.abs(volatilityFactor) * 0.05;
      
      liquidityPools.push({
        id: `${protocol.toLowerCase()}-${token0.symbol}-${token1.symbol}`,
        name: `${token0.symbol}/${token1.symbol}`,
        protocol,
        token0: token0.symbol,
        token1: token1.symbol,
        tvl: tvl * 1000000, // Convert back to dollars
        volume24h: volume24h * 1000000,
        apy: baseApy,
        ilRisk: Math.min(10, Math.max(1, Math.round(ilRisk * 10) / 10 * 10)),
        impermanentLoss7d,
        impermanentLoss30d,
        historicalData: generateHistoricalData(baseApy, tvl * 1000000)
      });
    }
    
    cacheResponse("liquidity_pools", liquidityPools);
    return liquidityPools;
    
  } catch (error) {
    return handleApiError(error, {
      key: "liquidity_pools",
      data: []
    });
  }
};
