
import { coinGeckoAxios, handleApiError, cacheResponse } from "../coinGeckoClient";

// Get on-chain metrics for a coin (simulated as CoinGecko doesn't provide this directly)
export const fetchOnChainMetrics = async (coinId: string) => {
  try {
    // For demonstration, we'll use CoinGecko's community_data as a proxy for on-chain metrics
    const response = await coinGeckoAxios.get(`/coins/${coinId}`, {
      params: {
        localization: false,
        tickers: false,
        market_data: false,
        community_data: true,
        developer_data: false,
      }
    });
    
    const { community_data } = response.data;
    
    // Simulate some on-chain metrics based on available data
    const metrics = {
      active_addresses: Math.floor(community_data?.twitter_followers || 0 / 10),
      transaction_volume_24h: Math.floor(Math.random() * 1000000),
      average_transaction_fee: (Math.random() * 2).toFixed(4),
      network_hash_rate: coinId === 'bitcoin' ? `${(Math.random() * 300 + 100).toFixed(2)} EH/s` : 'N/A',
      active_validators: ['ethereum', 'solana', 'cardano', 'polkadot'].includes(coinId) ? 
        Math.floor(Math.random() * 10000 + 1000) : 'N/A',
      social_dominance: community_data?.twitter_followers || 0,
    };
    
    cacheResponse(`onchain_${coinId}`, metrics);
    return metrics;
  } catch (error) {
    return handleApiError(error, {
      key: `onchain_${coinId}`,
      data: null
    });
  }
};
