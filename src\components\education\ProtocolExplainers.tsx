
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDes<PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent } from "@/components/ui/tabs";
import { ExternalLink, Layers, ArrowRight } from "lucide-react";
import { ProtocolExplainer } from "@/hooks/useEducation";
import { ScrollArea } from "@/components/ui/scroll-area";

interface ProtocolExplainersProps {
  protocols: ProtocolExplainer[];
  loading: boolean;
}

export function ProtocolExplainers({ protocols, loading }: ProtocolExplainersProps) {
  const [activeProtocol, setActiveProtocol] = useState<string | null>(protocols?.[0]?.id || null);

  // Get selected protocol
  const selectedProtocol = protocols.find(p => p.id === activeProtocol);

  if (loading) {
    return (
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="animate-pulse bg-muted h-80 rounded-md"></div>
        <div className="lg:col-span-2 animate-pulse bg-muted h-80 rounded-md"></div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Protocol List */}
      <Card>
        <CardHeader>
          <CardTitle>Protocol Directory</CardTitle>
          <CardDescription>Select a protocol to explore</CardDescription>
        </CardHeader>
        <CardContent className="p-0">
          <ScrollArea className="h-[calc(100vh-370px)]">
            <div className="space-y-1 p-4">
              {protocols.map((protocol) => (
                <Button
                  key={protocol.id}
                  variant={protocol.id === activeProtocol ? "default" : "ghost"}
                  className="w-full justify-start gap-3 h-auto py-3"
                  onClick={() => setActiveProtocol(protocol.id)}
                >
                  <div className="w-8 h-8 rounded-full bg-muted flex items-center justify-center shrink-0">
                    <Layers size={16} className="text-primary" />
                  </div>
                  <div className="text-left">
                    <div className="font-medium">{protocol.name}</div>
                    <div className="text-xs text-muted-foreground">{protocol.category}</div>
                  </div>
                </Button>
              ))}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>

      {/* Protocol Details */}
      {selectedProtocol ? (
        <Card className="lg:col-span-2">
          <CardHeader>
            <div className="flex justify-between items-start">
              <div>
                <CardTitle className="text-xl">{selectedProtocol.name}</CardTitle>
                <CardDescription>{selectedProtocol.category}</CardDescription>
              </div>
              <Badge variant="outline">{selectedProtocol.category}</Badge>
            </div>
          </CardHeader>

          <CardContent className="space-y-6">
            <Tabs defaultValue="overview">
              <TabsList className="grid grid-cols-3 mb-4">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="components">Components</TabsTrigger>
                <TabsTrigger value="risks">Risks</TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="space-y-4">
                <div className="space-y-4">
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Description</h3>
                    <p>{selectedProtocol.description}</p>
                  </div>

                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">How It Works</h3>
                    <p>{selectedProtocol.howItWorks}</p>
                  </div>

                  {/* Protocol Diagram */}
                  <div className="mt-4">
                    <h3 className="text-sm font-medium text-muted-foreground mb-2">Architecture</h3>
                    <div className="rounded-lg overflow-hidden border">
                      <img
                        src={selectedProtocol.diagram}
                        alt={`${selectedProtocol.name} diagram`}
                        className="w-full object-contain max-h-96"
                        onError={(e) => {
                          (e.target as HTMLImageElement).src = '/placeholder.svg';
                        }}
                      />
                    </div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="components">
                <div className="space-y-4">
                  <h3 className="text-sm font-medium text-muted-foreground">Key Components</h3>

                  <div className="space-y-3">
                    {selectedProtocol.keyComponents.map((component, index) => (
                      <Card key={index}>
                        <CardContent className="p-4">
                          <h4 className="font-medium">{component.name}</h4>
                          <p className="text-sm text-muted-foreground mt-1">{component.description}</p>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="risks">
                <div className="space-y-4">
                  <h3 className="text-sm font-medium text-muted-foreground">Use Cases</h3>
                  <ul className="space-y-2">
                    {selectedProtocol.useCases.map((useCase, index) => (
                      <li key={index} className="flex gap-2 items-start">
                        <span className="inline-block h-1.5 w-1.5 rounded-full bg-primary mt-2"></span>
                        <span>{useCase}</span>
                      </li>
                    ))}
                  </ul>

                  <h3 className="text-sm font-medium text-muted-foreground pt-4">Potential Risks</h3>
                  <ul className="space-y-2">
                    {selectedProtocol.risks.map((risk, index) => (
                      <li key={index} className="flex gap-2 items-start">
                        <span className="inline-block h-1.5 w-1.5 rounded-full bg-destructive mt-2"></span>
                        <span>{risk}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>

          <CardFooter>
            <div className="flex flex-wrap gap-2 w-full">
              {selectedProtocol.examples.map((example, index) => (
                <a
                  key={index}
                  href={example.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex"
                >
                  <Button variant="outline" size="sm" className="gap-1">
                    {example.name}
                    <ExternalLink size={14} />
                  </Button>
                </a>
              ))}
            </div>
          </CardFooter>
        </Card>
      ) : (
        <Card className="lg:col-span-2">
          <CardContent className="p-10 text-center flex flex-col items-center justify-center h-full">
            <Layers size={48} className="text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium">Select a Protocol</h3>
            <p className="text-muted-foreground mt-1">Choose a protocol from the list to view details</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
