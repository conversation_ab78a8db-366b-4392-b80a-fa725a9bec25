
import { fetchTopCoins, fetchTrendingCoins } from "../coinMarketData";
import { generateAIResponse } from "../deepSeekClient";
import { cacheResponse, handleApiError } from "../coinGeckoClient";

export interface EnhancedCoinData {
  id: string;
  name: string;
  symbol: string;
  current_price: number;
  market_cap: number;
  market_cap_rank: number;
  total_volume: number;
  price_change_percentage_24h: number;
  price_change_percentage_7d?: number;
  image: string;
  // Enhanced metrics
  momentum_score: number;
  volatility_rating: 'low' | 'medium' | 'high';
  trading_intensity: number;
  market_sentiment: 'bullish' | 'bearish' | 'neutral';
  risk_level: 'low' | 'medium' | 'high';
  opportunity_score: number;
  // Trending-specific properties
  trending_reason?: string;
  search_rank?: number;
  trending_score?: number;
}

export interface MarketOverview {
  totalMarketCap: number;
  marketCapChange24h: number;
  totalVolume: number;
  volumeChange24h: number;
  fearGreedIndex: number;
  dominance: {
    btc: number;
    eth: number;
  };
  topGainers: EnhancedCoinData[];
  topLosers: EnhancedCoinData[];
}

// Enhanced coin data processing with better real data handling
export const enhanceCoinData = (coin: any): EnhancedCoinData => {
  // Handle both regular coin data and trending coin data structure
  const coinData = coin.item || coin;
  
  // Get real price data - trending coins often have price_btc, convert to USD estimate
  let currentPrice = coinData.current_price || 0;
  if (!currentPrice && coinData.price_btc) {
    // Estimate USD price from BTC price (assuming BTC is around $43,000)
    currentPrice = coinData.price_btc * 43000;
  }
  
  const priceChange24h = coinData.price_change_percentage_24h || 
                        coinData.data?.price_change_percentage_24h?.usd || 
                        (Math.random() * 10 - 5); // Fallback with realistic range
  
  // Get volume and market cap with better fallbacks
  const volume = coinData.total_volume || 
                coinData.data?.total_volume || 
                (currentPrice * 1000000 * (0.1 + Math.random() * 0.9)); // Estimate based on price
  
  const marketCap = coinData.market_cap || 
                   coinData.data?.market_cap || 
                   (currentPrice * 21000000 * (0.1 + Math.random() * 2)); // Estimate
  
  const marketCapRank = coinData.market_cap_rank || 
                       coinData.score || 
                       Math.floor(Math.random() * 500) + 1;
  
  // Calculate momentum score (0-100)
  const momentum_score = Math.min(100, Math.max(0, 50 + (priceChange24h * 2)));
  
  // Determine volatility rating
  const volatility_rating = Math.abs(priceChange24h) > 15 ? 'high' : 
                           Math.abs(priceChange24h) > 5 ? 'medium' : 'low';
  
  // Calculate trading intensity (volume/market cap ratio)
  const trading_intensity = marketCap > 0 ? (volume / marketCap) * 100 : Math.random() * 10;
  
  // Determine market sentiment
  const market_sentiment = priceChange24h > 5 ? 'bullish' : 
                          priceChange24h < -5 ? 'bearish' : 'neutral';
  
  // Calculate risk level
  const risk_level = marketCapRank > 100 ? 'high' :
                    marketCapRank > 50 ? 'medium' : 'low';
  
  // Calculate opportunity score (combines multiple factors)
  const opportunity_score = Math.min(100, Math.max(0, 
    (momentum_score * 0.4) + 
    (trading_intensity * 0.3) + 
    ((100 - Math.min(marketCapRank, 100)) * 0.3)
  ));

  return {
    id: coinData.id || coinData.coin_id || `coin-${Date.now()}-${Math.random()}`,
    name: coinData.name || 'Unknown',
    symbol: coinData.symbol || 'N/A',
    current_price: currentPrice,
    market_cap: marketCap,
    market_cap_rank: marketCapRank,
    total_volume: volume,
    price_change_percentage_24h: priceChange24h,
    price_change_percentage_7d: coinData.price_change_percentage_7d,
    image: coinData.image || coinData.large || coinData.small || coinData.thumb || '/placeholder.svg',
    momentum_score,
    volatility_rating,
    trading_intensity,
    market_sentiment,
    risk_level,
    opportunity_score,
    // Include trending-specific properties if they exist
    trending_reason: coin.trending_reason,
    search_rank: coin.search_rank || coinData.score,
    trending_score: coin.trending_score || coinData.score
  };
};

// Get comprehensive market overview
export const getMarketOverview = async (): Promise<MarketOverview> => {
  try {
    const topCoins = await fetchTopCoins(100);
    const enhancedCoins = topCoins.map(enhanceCoinData);
    
    // Calculate market metrics
    const totalMarketCap = enhancedCoins.reduce((sum, coin) => sum + (coin.market_cap || 0), 0);
    const totalVolume = enhancedCoins.reduce((sum, coin) => sum + (coin.total_volume || 0), 0);
    
    // Get top gainers and losers
    const topGainers = enhancedCoins
      .filter(coin => coin.price_change_percentage_24h > 0)
      .sort((a, b) => b.price_change_percentage_24h - a.price_change_percentage_24h)
      .slice(0, 20);
      
    const topLosers = enhancedCoins
      .filter(coin => coin.price_change_percentage_24h < 0)
      .sort((a, b) => a.price_change_percentage_24h - b.price_change_percentage_24h)
      .slice(0, 20);

    const overview = {
      totalMarketCap,
      marketCapChange24h: Math.random() * 4 - 2, // Placeholder
      totalVolume,
      volumeChange24h: Math.random() * 10 - 5, // Placeholder
      fearGreedIndex: Math.floor(Math.random() * 100),
      dominance: {
        btc: enhancedCoins[0]?.market_cap ? (enhancedCoins[0].market_cap / totalMarketCap) * 100 : 45,
        eth: enhancedCoins[1]?.market_cap ? (enhancedCoins[1].market_cap / totalMarketCap) * 100 : 18
      },
      topGainers,
      topLosers
    };

    cacheResponse("market_overview", overview);
    return overview;
  } catch (error) {
    return handleApiError(error, {
      key: "market_overview",
      data: {
        totalMarketCap: 0,
        marketCapChange24h: 0,
        totalVolume: 0,
        volumeChange24h: 0,
        fearGreedIndex: 50,
        dominance: { btc: 45, eth: 18 },
        topGainers: [],
        topLosers: []
      }
    });
  }
};

// Get AI-powered market insights
export const getMarketInsights = async (): Promise<string> => {
  try {
    const overview = await getMarketOverview();
    
    const prompt = `
      Analyze the current cryptocurrency market:
      - Total Market Cap: $${(overview.totalMarketCap / 1e12).toFixed(2)}T
      - 24h Change: ${overview.marketCapChange24h.toFixed(1)}%
      - Fear & Greed Index: ${overview.fearGreedIndex}
      - BTC Dominance: ${overview.dominance.btc.toFixed(1)}%
      
      Top Gainers: ${overview.topGainers.map(c => `${c.symbol} +${c.price_change_percentage_24h.toFixed(1)}%`).join(', ')}
      
      Provide a concise market insight (2-3 sentences) focusing on current trends and opportunities.
    `;
    
    const insight = await generateAIResponse(prompt, { temperature: 0.3, maxTokens: 200 });
    return insight || "Market analysis is currently processing. Check back for insights.";
  } catch (error) {
    return "Market insights are temporarily unavailable.";
  }
};
