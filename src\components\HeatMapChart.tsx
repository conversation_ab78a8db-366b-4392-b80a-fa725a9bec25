
import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, Card<PERSON>itle, CardDescription } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

interface HeatMapItem {
  name: string;
  value: number;
  percentChange: number;
  category: string;
}

interface HeatMapChartProps {
  title: string;
  description?: string;
  data: HeatMapItem[];
  isLoading?: boolean;
  colorScale?: "performance" | "correlation";
  showLegend?: boolean;
}

export function HeatMapChart({
  title,
  description,
  data,
  isLoading = false,
  colorScale = "performance",
  showLegend = true
}: HeatMapChartProps) {
  const [ready, setReady] = useState(false);

  useEffect(() => {
    // Add slight delay to ensure proper rendering
    const timer = setTimeout(() => setReady(true), 100);
    return () => clearTimeout(timer);
  }, [data]);

  // Group data by categories
  const groupedData = data.reduce((acc, item) => {
    if (!acc[item.category]) {
      acc[item.category] = [];
    }
    acc[item.category].push(item);
    return acc;
  }, {} as Record<string, HeatMapItem[]>);

  // Get value range for color scale
  const values = data.map(item => item.value);
  const minValue = Math.min(...values);
  const maxValue = Math.max(...values);

  // Function to determine cell color based on value
  const getCellColor = (value: number, percentChange: number) => {
    if (colorScale === "correlation") {
      // Correlation color scale from -1 to 1
      if (value === 0) return "rgb(240, 240, 240)";
      if (value > 0) {
        const intensity = Math.min(Math.round((value / maxValue) * 255), 255);
        return `rgba(21, 128, 61, ${value.toFixed(1)})`;
      } else {
        const intensity = Math.min(Math.round((Math.abs(value) / Math.abs(minValue)) * 255), 255);
        return `rgba(185, 28, 28, ${Math.abs(value).toFixed(1)})`;
      }
    } else {
      // Performance color scale (green to red)
      if (percentChange > 0) {
        const intensity = Math.min(percentChange * 5, 100);
        return `rgba(21, 128, 61, ${intensity / 100})`;
      } else {
        const intensity = Math.min(Math.abs(percentChange) * 5, 100);
        return `rgba(185, 28, 28, ${intensity / 100})`;
      }
    }
  };

  const getTextColor = (bgColor: string) => {
    // Extract opacity from rgba string
    const opacity = parseFloat(bgColor.split(',')[3] || "1");
    return opacity > 0.6 ? "white" : "black";
  };

  if (isLoading || !ready) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
          {description && <CardDescription>{description}</CardDescription>}
        </CardHeader>
        <CardContent>
          <Skeleton className="h-[400px] w-full" />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent>
        {showLegend && (
          <div className="flex justify-center mb-4">
            <div className="flex items-center gap-2">
              <div className="flex h-2 w-40 rounded">
                <div className="flex-1 bg-gradient-to-r from-red-700 via-gray-200 to-green-700"></div>
              </div>
              <div className="flex justify-between w-40 text-xs text-muted-foreground">
                <span>{colorScale === "correlation" ? "-1" : "-20%"}</span>
                <span>0</span>
                <span>{colorScale === "correlation" ? "1" : "+20%"}</span>
              </div>
            </div>
          </div>
        )}

        <div className="space-y-6">
          {Object.entries(groupedData).map(([category, items]) => (
            <div key={category} className="mb-4">
              <h3 className="text-sm font-medium mb-2">{category}</h3>
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-2">
                {items.map((item, i) => {
                  const bgColor = getCellColor(item.value, item.percentChange);
                  const textColor = getTextColor(bgColor);
                  return (
                    <div
                      key={`${item.name}-${i}`}
                      className="flex flex-col items-center justify-center p-3 rounded transition-transform hover:scale-105"
                      style={{
                        backgroundColor: bgColor,
                        color: textColor
                      }}
                    >
                      <span className="font-bold text-sm">{item.name}</span>
                      <span className="text-xs opacity-90">{item.value.toFixed(2)}</span>
                      <span className={`text-xs ${item.percentChange >= 0 ? "text-green-100" : "text-red-100"}`}>
                        {item.percentChange > 0 ? "+" : ""}{item.percentChange.toFixed(2)}%
                      </span>
                    </div>
                  );
                })}
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
