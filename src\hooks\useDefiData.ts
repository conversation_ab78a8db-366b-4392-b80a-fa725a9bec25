
import { useState, useEffect } from "react";
import { toast } from "sonner";
import {
  fetchYieldOpportunities,
  fetchProtocolRisks,
  generateInvestmentStrategies,
  fetchLiquidityPoolData,
  fetchProtocolHealthData,
  fetchGasData,
  fetchCrossChainBridges,
  YieldOpportunity,
  ProtocolRisk,
  Strategy,
  LiquidityPool,
  ProtocolHealth,
  GasInfo,
  Bridge
} from "@/services/api/defi";

export function useDefiData() {
  const [opportunities, setOpportunities] = useState<YieldOpportunity[]>([]);
  const [protocolRisks, setProtocolRisks] = useState<ProtocolRisk[]>([]);
  const [strategies, setStrategies] = useState<Strategy[]>([]);
  const [liquidityPools, setLiquidityPools] = useState<LiquidityPool[]>([]);
  const [protocolHealth, setProtocolHealth] = useState<ProtocolHealth[]>([]);
  const [gasData, setGasData] = useState<GasInfo[]>([]);
  const [bridges, setBridges] = useState<Bridge[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const [
        yieldData, 
        riskData, 
        strategyData, 
        liquidityData, 
        healthData, 
        gasInfo, 
        bridgeData
      ] = await Promise.all([
        fetchYieldOpportunities(),
        fetchProtocolRisks(),
        generateInvestmentStrategies(),
        fetchLiquidityPoolData(),
        fetchProtocolHealthData(),
        fetchGasData(),
        fetchCrossChainBridges()
      ]);
      
      setOpportunities(yieldData);
      setProtocolRisks(riskData);
      setStrategies(strategyData);
      setLiquidityPools(liquidityData);
      setProtocolHealth(healthData);
      setGasData(gasInfo);
      setBridges(bridgeData);
    } catch (err) {
      console.error("Error fetching DeFi data:", err);
      setError("Failed to fetch DeFi data. Please try again.");
      toast.error("Failed to fetch DeFi data");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const refreshData = () => {
    fetchData();
  };

  return {
    opportunities,
    protocolRisks,
    strategies,
    liquidityPools,
    protocolHealth,
    gasData,
    bridges,
    loading,
    error,
    refreshData
  };
}
