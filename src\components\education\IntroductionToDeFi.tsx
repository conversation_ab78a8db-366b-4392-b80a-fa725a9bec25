
import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from '@/components/ui/separator';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { ArrowRight, CheckCircle, XCircle, DollarSign, Percent, ArrowUpDown, Shield } from 'lucide-react';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

export default function IntroductionToDeFi({ loading = false }: { loading?: boolean }) {
  const [selectedTab, setSelectedTab] = useState("overview");
  const [quizStarted, setQuizStarted] = useState(false);
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [score, setScore] = useState(0);
  const [quizCompleted, setQuizCompleted] = useState(false);
  const [showExplanation, setShowExplanation] = useState(false);
  const [simulatorStep, setSimulatorStep] = useState(1);
  const [simulatorBalance, setSimulatorBalance] = useState(100);
  const [depositAmount, setDepositAmount] = useState(50);
  const [interestEarned, setInterestEarned] = useState(0);

  // Quiz questions
  const quizQuestions = [
    {
      question: "What does DeFi stand for?",
      options: [
        "Digital Finance",
        "Decentralized Finance",
        "Distributable Financials",
        "Direct Financial Instruments"
      ],
      correctAnswer: 1,
      explanation: "DeFi stands for Decentralized Finance, which refers to financial applications built on blockchain technology that don't rely on centralized financial intermediaries."
    },
    {
      question: "Which of the following is NOT a common DeFi application?",
      options: [
        "Decentralized exchanges (DEXs)",
        "Lending platforms",
        "Yield farming",
        "Central bank digital currencies (CBDCs)"
      ],
      correctAnswer: 3,
      explanation: "Central bank digital currencies (CBDCs) are typically issued and controlled by central banks, making them centralized by nature, which is contrary to the decentralized ethos of DeFi."
    },
    {
      question: "What is a liquidity pool in DeFi?",
      options: [
        "A centralized reserve of funds controlled by a bank",
        "A collection of cryptocurrency locked in a smart contract",
        "A type of cryptocurrency wallet",
        "A regulatory compliance mechanism"
      ],
      correctAnswer: 1,
      explanation: "Liquidity pools are collections of tokens or cryptocurrencies locked in a smart contract, which provide liquidity for decentralized trading, lending, and other financial activities."
    },
    {
      question: "What is 'yield farming' in DeFi?",
      options: [
        "Mining new cryptocurrencies",
        "Growing agricultural products using blockchain",
        "Staking or lending crypto assets to generate returns",
        "Creating new blockchain networks"
      ],
      correctAnswer: 2,
      explanation: "Yield farming refers to the practice of staking or lending crypto assets in order to generate high returns or rewards in the form of additional cryptocurrency."
    },
    {
      question: "What is a smart contract in the context of DeFi?",
      options: [
        "A legally binding agreement between two financial institutions",
        "A self-executing contract with the terms directly written into code",
        "A traditional financial contract that uses digital signatures",
        "A contract that requires approval from financial regulators"
      ],
      correctAnswer: 1,
      explanation: "A smart contract is a self-executing contract with the terms of the agreement directly written into lines of code that automatically execute when predetermined conditions are met, without requiring intermediaries."
    }
  ];

  const handleQuizStart = () => {
    setQuizStarted(true);
    setCurrentQuestion(0);
    setScore(0);
    setQuizCompleted(false);
    toast.info("DeFi Quiz started! Test your knowledge.");
  };

  const handleAnswerSubmit = (selectedAnswer: number) => {
    const isCorrect = selectedAnswer === quizQuestions[currentQuestion].correctAnswer;
    setShowExplanation(true);

    if (isCorrect) {
      setScore(score + 1);
      toast.success("Correct answer!");
    } else {
      toast.error("Not quite right!");
    }

    // Move to next question or end quiz after delay to show explanation
    setTimeout(() => {
      if (currentQuestion < quizQuestions.length - 1) {
        setCurrentQuestion(currentQuestion + 1);
        setShowExplanation(false);
      } else {
        setQuizCompleted(true);
      }
    }, 3000);
  };

  const handleNextSimulatorStep = () => {
    if (simulatorStep < 3) {
      setSimulatorStep(simulatorStep + 1);

      // Calculate interest in step 2
      if (simulatorStep === 2) {
        const interest = parseFloat((depositAmount * 0.05).toFixed(2));
        setInterestEarned(interest);
        setSimulatorBalance(simulatorBalance - depositAmount + depositAmount + interest);
      }
    } else {
      // Reset simulator
      setSimulatorStep(1);
      setSimulatorBalance(100);
      setDepositAmount(50);
      setInterestEarned(0);
    }
  };

  const handleDepositChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(e.target.value);
    if (!isNaN(value) && value <= simulatorBalance && value >= 0) {
      setDepositAmount(value);
    }
  };

  if (loading) {
    return (
      <Card className="animate-pulse">
        <CardHeader className="pb-2">
          <div className="h-5 w-3/4 bg-muted rounded mb-1"></div>
          <div className="h-4 w-full bg-muted rounded"></div>
        </CardHeader>
        <CardContent>
          <div className="h-[400px] bg-muted rounded mb-2"></div>
        </CardContent>
      </Card>
    );
  }

  // Quiz component
  if (quizStarted) {
    return (
      <Card className="max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle>Introduction to DeFi Quiz</CardTitle>
          <CardDescription>
            {!quizCompleted
              ? `Question ${currentQuestion + 1} of ${quizQuestions.length}`
              : "Quiz Completed"}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {!quizCompleted ? (
            <div className="space-y-4">
              <div className="text-xl font-medium">{quizQuestions[currentQuestion].question}</div>
              <div className="space-y-2">
                {quizQuestions[currentQuestion].options.map((option, index) => (
                  <Button
                    key={index}
                    variant={showExplanation ? (index === quizQuestions[currentQuestion].correctAnswer ? "default" : "outline") : "outline"}
                    className={`w-full justify-start text-left h-auto py-3 px-4 ${
                      showExplanation && index === quizQuestions[currentQuestion].correctAnswer ? "bg-green-500/20 hover:bg-green-500/20 border-green-500" : ""
                    }`}
                    onClick={() => !showExplanation && handleAnswerSubmit(index)}
                    disabled={showExplanation}
                  >
                    {option}
                    {showExplanation && index === quizQuestions[currentQuestion].correctAnswer && (
                      <CheckCircle className="ml-auto h-4 w-4 text-green-500" />
                    )}
                  </Button>
                ))}
              </div>

              {showExplanation && (
                <div className="p-4 bg-background border rounded-md mt-4">
                  <div className="flex gap-2">
                    <CheckCircle className="text-primary mt-0.5" size={18} />
                    <div>
                      <h4 className="font-medium mb-1">Explanation:</h4>
                      <p className="text-sm">{quizQuestions[currentQuestion].explanation}</p>
                    </div>
                  </div>
                </div>
              )}

              <div className="pt-4">
                <p className="text-muted-foreground text-sm">
                  {!showExplanation ? "Select the best answer. Your score will be calculated at the end." : "Moving to the next question shortly..."}
                </p>
              </div>
            </div>
          ) : (
            <div className="space-y-6 py-4">
              <div className="text-center">
                <div className="text-3xl font-bold mb-2">
                  Your Score: {score}/{quizQuestions.length}
                </div>
                <div className="text-muted-foreground">
                  {score === quizQuestions.length ?
                    "Perfect! You're a DeFi expert!" :
                    score >= quizQuestions.length / 2 ?
                    "Good job! You have a solid understanding of DeFi fundamentals." :
                    "Keep learning! DeFi concepts take time to master."}
                </div>
              </div>

              <div className="space-y-4 mt-6">
                <h3 className="font-medium text-lg">Review:</h3>
                {quizQuestions.map((q, idx) => (
                  <div key={idx} className="p-4 border rounded-md">
                    <div className="flex gap-2">
                      <div className="mt-1">
                        {score > idx ? <CheckCircle className="text-green-500" size={18} /> : <XCircle className="text-red-500" size={18} />}
                      </div>
                      <div>
                        <p className="font-medium">{q.question}</p>
                        <p className="text-sm text-muted-foreground mt-1">Correct answer: {q.options[q.correctAnswer]}</p>
                        <p className="text-sm mt-1">{q.explanation}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              <div className="flex justify-center pt-4">
                <Button onClick={() => setQuizStarted(false)} className="mr-2">
                  Return to Lesson
                </Button>
                <Button onClick={handleQuizStart} variant="outline">
                  Retry Quiz
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Introduction to DeFi</CardTitle>
          <CardDescription>Discover the world of Decentralized Finance and its revolutionary applications</CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="overview" value={selectedTab} onValueChange={setSelectedTab}>
            <TabsList className="grid grid-cols-4 mb-6">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="protocols">Key Protocols</TabsTrigger>
              <TabsTrigger value="applications">Applications</TabsTrigger>
              <TabsTrigger value="simulator">DeFi Simulator</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              <section>
                <h3 className="text-lg font-medium mb-2">What is DeFi?</h3>
                <p className="text-muted-foreground mb-4">
                  Decentralized Finance, or DeFi, is a blockchain-based financial ecosystem that aims to recreate and improve
                  upon traditional financial systems without relying on centralized intermediaries like banks, brokerages,
                  or exchanges. Instead, DeFi uses smart contracts on blockchain networks (primarily Ethereum) to enable
                  financial services that are permissionless, transparent, and accessible to anyone with an internet connection.
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 my-4">
                  <div className="p-4 border rounded-md bg-primary/5">
                    <h4 className="font-medium mb-2">Traditional Finance</h4>
                    <ul className="list-disc pl-5 space-y-1 text-sm text-muted-foreground">
                      <li>Controlled by centralized institutions</li>
                      <li>Restricted access based on location/status</li>
                      <li>Limited operational hours</li>
                      <li>Lack of transparency</li>
                      <li>Higher fees for international transactions</li>
                      <li>Slow settlement times</li>
                    </ul>
                  </div>

                  <div className="p-4 border rounded-md bg-primary/5">
                    <h4 className="font-medium mb-2">Decentralized Finance</h4>
                    <ul className="list-disc pl-5 space-y-1 text-sm text-muted-foreground">
                      <li>Permissionless, open to anyone</li>
                      <li>Global access, no geographical restrictions</li>
                      <li>24/7 operation</li>
                      <li>Full transparency on blockchain</li>
                      <li>Lower fees, borderless transactions</li>
                      <li>Near-instant settlement</li>
                    </ul>
                  </div>
                </div>

                <p className="text-muted-foreground">
                  The DeFi ecosystem has grown exponentially since 2020, with billions of dollars locked in various
                  protocols. This growth reflects increasing interest in alternatives to traditional financial systems
                  and recognition of the potential for more inclusive, efficient financial services.
                </p>
              </section>

              <Separator />

              <section>
                <h3 className="text-lg font-medium mb-2">Core Components of DeFi</h3>

                <div className="space-y-4 mt-4">
                  <div className="p-4 border rounded-md">
                    <div className="flex items-start gap-3">
                      <span className="p-2 bg-primary/10 rounded-full">
                        <DollarSign size={20} className="text-primary" />
                      </span>
                      <div>
                        <h4 className="font-medium">Stablecoins</h4>
                        <p className="text-sm text-muted-foreground">
                          Cryptocurrencies designed to maintain a stable value, usually pegged to a fiat currency like the
                          US Dollar. Examples include USDC, DAI, and USDT. These serve as the foundation for many DeFi
                          applications by providing price stability in an otherwise volatile market.
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="p-4 border rounded-md">
                    <div className="flex items-start gap-3">
                      <span className="p-2 bg-primary/10 rounded-full">
                        <ArrowUpDown size={20} className="text-primary" />
                      </span>
                      <div>
                        <h4 className="font-medium">Decentralized Exchanges (DEXs)</h4>
                        <p className="text-sm text-muted-foreground">
                          Platforms that enable peer-to-peer trading of cryptocurrencies without a central authority.
                          Instead of using order books, many DEXs like Uniswap use automated market makers (AMMs) that
                          use liquidity pools and mathematical formulas to determine asset prices.
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="p-4 border rounded-md">
                    <div className="flex items-start gap-3">
                      <span className="p-2 bg-primary/10 rounded-full">
                        <Percent size={20} className="text-primary" />
                      </span>
                      <div>
                        <h4 className="font-medium">Lending and Borrowing Protocols</h4>
                        <p className="text-sm text-muted-foreground">
                          Platforms that allow users to lend their crypto assets to earn interest or borrow assets by
                          providing collateral. Examples include Aave, Compound, and MakerDAO. These protocols determine
                          interest rates algorithmically based on supply and demand.
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="p-4 border rounded-md">
                    <div className="flex items-start gap-3">
                      <span className="p-2 bg-primary/10 rounded-full">
                        <Shield size={20} className="text-primary" />
                      </span>
                      <div>
                        <h4 className="font-medium">Insurance Protocols</h4>
                        <p className="text-sm text-muted-foreground">
                          Services that provide coverage against smart contract failures, hacks, or other risks in the
                          DeFi ecosystem. Examples include Nexus Mutual and InsurAce. These protocols help mitigate the
                          risks associated with interacting with DeFi applications.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </section>

              <Separator />

              <section>
                <h3 className="text-lg font-medium mb-2">Key Concepts</h3>

                <div className="space-y-3">
                  <div>
                    <h4 className="font-medium">Smart Contracts</h4>
                    <p className="text-sm text-muted-foreground">
                      Self-executing contracts with the terms directly written into code. They automatically execute
                      transactions when predefined conditions are met, eliminating the need for intermediaries.
                    </p>
                  </div>

                  <div>
                    <h4 className="font-medium">Liquidity Pools</h4>
                    <p className="text-sm text-muted-foreground">
                      Collections of funds locked in smart contracts that provide liquidity for decentralized trading,
                      lending, and other financial activities. Liquidity providers earn fees or rewards in return.
                    </p>
                  </div>

                  <div>
                    <h4 className="font-medium">Yield Farming</h4>
                    <p className="text-sm text-muted-foreground">
                      The practice of moving crypto assets between different DeFi protocols to maximize returns.
                      Yield farmers seek the highest yields by continuously shifting their assets.
                    </p>
                  </div>

                  <div>
                    <h4 className="font-medium">Governance Tokens</h4>
                    <p className="text-sm text-muted-foreground">
                      Tokens that grant holders voting rights on protocol changes or upgrades. They represent a form
                      of decentralized governance where stakeholders collectively decide on the future of a protocol.
                    </p>
                  </div>

                  <div>
                    <h4 className="font-medium">Total Value Locked (TVL)</h4>
                    <p className="text-sm text-muted-foreground">
                      A metric that measures the total value of assets deposited in a DeFi protocol. It's often used
                      to compare the relative size and growth of different DeFi projects.
                    </p>
                  </div>
                </div>

                <div className="mt-6 flex justify-center">
                  <Button onClick={handleQuizStart}>
                    Take DeFi Quiz <ArrowRight className="ml-1 h-4 w-4" />
                  </Button>
                </div>
              </section>
            </TabsContent>

            <TabsContent value="protocols" className="space-y-6">
              <section>
                <h3 className="text-lg font-medium mb-4">Leading DeFi Protocols</h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">Uniswap</CardTitle>
                      <CardDescription>Decentralized Exchange</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground mb-3">
                        Uniswap is the largest decentralized exchange that uses an Automated Market Maker (AMM) model
                        instead of traditional order books. It allows users to swap any ERC-20 tokens directly from their
                        wallets without intermediaries.
                      </p>
                      <div className="space-y-1 text-sm">
                        <p><span className="font-medium">Key Features:</span> Permissionless token listings, automated liquidity, minimal fees</p>
                        <p><span className="font-medium">Governance:</span> UNI token</p>
                        <p><span className="font-medium">Founded:</span> 2018</p>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">Aave</CardTitle>
                      <CardDescription>Lending Protocol</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground mb-3">
                        Aave is an open-source, non-custodial liquidity protocol that enables users to earn interest on
                        deposits and borrow assets. It pioneered features like flash loans and variable interest rates.
                      </p>
                      <div className="space-y-1 text-sm">
                        <p><span className="font-medium">Key Features:</span> Variable & stable interest rates, flash loans, credit delegation</p>
                        <p><span className="font-medium">Governance:</span> AAVE token</p>
                        <p><span className="font-medium">Founded:</span> 2017 (as ETHLend)</p>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">MakerDAO</CardTitle>
                      <CardDescription>Stablecoin Protocol</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground mb-3">
                        MakerDAO is the protocol behind DAI, a decentralized stablecoin pegged to the US Dollar. Users can
                        generate DAI by depositing collateral assets into Maker Vaults.
                      </p>
                      <div className="space-y-1 text-sm">
                        <p><span className="font-medium">Key Features:</span> Collateralized stablecoin, decentralized governance, stability mechanisms</p>
                        <p><span className="font-medium">Governance:</span> MKR token</p>
                        <p><span className="font-medium">Founded:</span> 2014</p>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">Compound</CardTitle>
                      <CardDescription>Lending Protocol</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground mb-3">
                        Compound is an algorithmic money market protocol that allows users to lend and borrow crypto assets.
                        Interest rates adjust automatically based on supply and demand.
                      </p>
                      <div className="space-y-1 text-sm">
                        <p><span className="font-medium">Key Features:</span> Algorithmic interest rates, cTokens, open borrowing/lending</p>
                        <p><span className="font-medium">Governance:</span> COMP token</p>
                        <p><span className="font-medium">Founded:</span> 2018</p>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </section>

              <Separator />

              <section>
                <h3 className="text-lg font-medium mb-4">DeFi Protocol Categories</h3>

                <div className="space-y-4">
                  <div className="p-4 border rounded-md">
                    <h4 className="font-medium mb-2">Exchanges</h4>
                    <p className="text-sm text-muted-foreground mb-2">
                      Decentralized exchanges enable peer-to-peer trading without intermediaries.
                    </p>
                    <div className="flex flex-wrap gap-2">
                      <span className="px-3 py-1 bg-primary/10 rounded-full text-xs">Uniswap</span>
                      <span className="px-3 py-1 bg-primary/10 rounded-full text-xs">SushiSwap</span>
                      <span className="px-3 py-1 bg-primary/10 rounded-full text-xs">Curve Finance</span>
                      <span className="px-3 py-1 bg-primary/10 rounded-full text-xs">dYdX</span>
                      <span className="px-3 py-1 bg-primary/10 rounded-full text-xs">Balancer</span>
                    </div>
                  </div>

                  <div className="p-4 border rounded-md">
                    <h4 className="font-medium mb-2">Lending Platforms</h4>
                    <p className="text-sm text-muted-foreground mb-2">
                      These protocols allow users to lend assets to earn interest or borrow by providing collateral.
                    </p>
                    <div className="flex flex-wrap gap-2">
                      <span className="px-3 py-1 bg-primary/10 rounded-full text-xs">Aave</span>
                      <span className="px-3 py-1 bg-primary/10 rounded-full text-xs">Compound</span>
                      <span className="px-3 py-1 bg-primary/10 rounded-full text-xs">MakerDAO</span>
                      <span className="px-3 py-1 bg-primary/10 rounded-full text-xs">Alchemix</span>
                      <span className="px-3 py-1 bg-primary/10 rounded-full text-xs">Liquity</span>
                    </div>
                  </div>

                  <div className="p-4 border rounded-md">
                    <h4 className="font-medium mb-2">Derivatives</h4>
                    <p className="text-sm text-muted-foreground mb-2">
                      Platforms that enable trading of synthetic assets, options, futures, and other derivatives.
                    </p>
                    <div className="flex flex-wrap gap-2">
                      <span className="px-3 py-1 bg-primary/10 rounded-full text-xs">Synthetix</span>
                      <span className="px-3 py-1 bg-primary/10 rounded-full text-xs">dYdX</span>
                      <span className="px-3 py-1 bg-primary/10 rounded-full text-xs">Opyn</span>
                      <span className="px-3 py-1 bg-primary/10 rounded-full text-xs">Perpetual Protocol</span>
                      <span className="px-3 py-1 bg-primary/10 rounded-full text-xs">UMA</span>
                    </div>
                  </div>

                  <div className="p-4 border rounded-md">
                    <h4 className="font-medium mb-2">Insurance</h4>
                    <p className="text-sm text-muted-foreground mb-2">
                      Protocols that provide coverage against smart contract failures, hacks, or other DeFi risks.
                    </p>
                    <div className="flex flex-wrap gap-2">
                      <span className="px-3 py-1 bg-primary/10 rounded-full text-xs">Nexus Mutual</span>
                      <span className="px-3 py-1 bg-primary/10 rounded-full text-xs">InsurAce</span>
                      <span className="px-3 py-1 bg-primary/10 rounded-full text-xs">Cover Protocol</span>
                      <span className="px-3 py-1 bg-primary/10 rounded-full text-xs">Unslashed Finance</span>
                    </div>
                  </div>
                </div>
              </section>
            </TabsContent>

            <TabsContent value="applications" className="space-y-6">
              <section>
                <h3 className="text-lg font-medium mb-2">Real-World DeFi Applications</h3>
                <p className="text-muted-foreground mb-4">
                  DeFi is expanding beyond cryptocurrency speculation into practical applications that solve real-world problems
                  and offer alternatives to traditional financial services.
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="p-4 border rounded-md bg-primary/5">
                    <h4 className="font-medium mb-2">Remittances & Cross-Border Payments</h4>
                    <p className="text-sm text-muted-foreground">
                      DeFi protocols enable near-instant, low-cost international transfers without traditional banking intermediaries.
                      This can dramatically reduce costs for migrant workers sending money home and businesses making international payments.
                    </p>
                    <div className="mt-3 text-sm">
                      <p><span className="font-medium">Benefits:</span> Lower fees, faster settlement, accessibility</p>
                      <p><span className="font-medium">Examples:</span> Stellar, Ripple, stablecoin transfers on Ethereum</p>
                    </div>
                  </div>

                  <div className="p-4 border rounded-md bg-primary/5">
                    <h4 className="font-medium mb-2">Financial Inclusion</h4>
                    <p className="text-sm text-muted-foreground">
                      With only an internet connection needed, DeFi provides banking services to the 1.7 billion adults worldwide
                      who lack access to traditional banking. Users can save, borrow, and invest without needing to qualify for
                      a bank account.
                    </p>
                    <div className="mt-3 text-sm">
                      <p><span className="font-medium">Benefits:</span> Access without identity requirements, low barriers to entry</p>
                      <p><span className="font-medium">Examples:</span> DeFi lending platforms, mobile DeFi wallets</p>
                    </div>
                  </div>

                  <div className="p-4 border rounded-md bg-primary/5">
                    <h4 className="font-medium mb-2">Tokenized Real-World Assets</h4>
                    <p className="text-sm text-muted-foreground">
                      Physical assets like real estate, art, or commodities can be represented as tokens on blockchain, making them
                      divisible, easily transferable, and accessible to global investors.
                    </p>
                    <div className="mt-3 text-sm">
                      <p><span className="font-medium">Benefits:</span> Fractional ownership, increased liquidity, 24/7 markets</p>
                      <p><span className="font-medium">Examples:</span> RealT (real estate), Centrifuge (invoice financing)</p>
                    </div>
                  </div>

                  <div className="p-4 border rounded-md bg-primary/5">
                    <h4 className="font-medium mb-2">Decentralized Insurance</h4>
                    <p className="text-sm text-muted-foreground">
                      Insurance products that operate without traditional insurance companies, using smart contracts for claims
                      processing and community-based risk pooling.
                    </p>
                    <div className="mt-3 text-sm">
                      <p><span className="font-medium">Benefits:</span> Lower overhead costs, faster claims, transparency</p>
                      <p><span className="font-medium">Examples:</span> Nexus Mutual, Etherisc (flight delay insurance)</p>
                    </div>
                  </div>
                </div>
              </section>

              <Separator />

              <section>
                <h3 className="text-lg font-medium mb-2">DeFi Risks and Challenges</h3>
                <p className="text-muted-foreground mb-4">
                  While DeFi offers many benefits, it also comes with significant risks that users should understand before
                  participating in the ecosystem.
                </p>

                <div className="space-y-3">
                  <div>
                    <h4 className="font-medium">Smart Contract Risks</h4>
                    <p className="text-sm text-muted-foreground">
                      Vulnerabilities in smart contract code can lead to hacks and loss of funds. Even audited contracts may
                      contain undiscovered bugs.
                    </p>
                  </div>

                  <div>
                    <h4 className="font-medium">Impermanent Loss</h4>
                    <p className="text-sm text-muted-foreground">
                      Liquidity providers on DEXs can experience losses when the price ratio of pooled assets changes
                      significantly after deposit.
                    </p>
                  </div>

                  <div>
                    <h4 className="font-medium">Oracle Failures</h4>
                    <p className="text-sm text-muted-foreground">
                      Many DeFi protocols rely on oracles for external price data. If these oracles provide incorrect
                      information, it can lead to system failures or exploits.
                    </p>
                  </div>

                  <div>
                    <h4 className="font-medium">Regulatory Uncertainty</h4>
                    <p className="text-sm text-muted-foreground">
                      The regulatory landscape for DeFi is still developing, with potential for new regulations that could
                      impact how protocols operate.
                    </p>
                  </div>

                  <div>
                    <h4 className="font-medium">Scalability Issues</h4>
                    <p className="text-sm text-muted-foreground">
                      High transaction fees and network congestion on blockchains like Ethereum can make DeFi inaccessible
                      during peak usage periods.
                    </p>
                  </div>
                </div>
              </section>

              <Separator />

              <section>
                <h3 className="text-lg font-medium mb-2">The Future of DeFi</h3>
                <p className="text-muted-foreground mb-4">
                  DeFi continues to evolve rapidly, with several promising trends shaping its future development.
                </p>

                <div className="space-y-3">
                  <div>
                    <h4 className="font-medium">Layer 2 Scaling Solutions</h4>
                    <p className="text-sm text-muted-foreground">
                      Technologies like Optimistic Rollups, zkRollups, and sidechains aim to improve DeFi scalability
                      by processing transactions off the main blockchain.
                    </p>
                  </div>

                  <div>
                    <h4 className="font-medium">Cross-Chain DeFi</h4>
                    <p className="text-sm text-muted-foreground">
                      Protocols that bridge multiple blockchains are enabling interoperability between different DeFi
                      ecosystems, expanding possibilities for users.
                    </p>
                  </div>

                  <div>
                    <h4 className="font-medium">Institutional Adoption</h4>
                    <p className="text-sm text-muted-foreground">
                      Traditional financial institutions are increasingly exploring DeFi, potentially bringing greater
                      liquidity and mainstream adoption.
                    </p>
                  </div>

                  <div>
                    <h4 className="font-medium">Real-World Asset Integration</h4>
                    <p className="text-sm text-muted-foreground">
                      The tokenization of real-world assets like real estate, stocks, and commodities is expanding
                      DeFi's reach beyond crypto-native applications.
                    </p>
                  </div>
                </div>
              </section>
            </TabsContent>

            <TabsContent value="simulator" className="space-y-6">
              <section>
                <h3 className="text-lg font-medium mb-2">DeFi Lending Simulator</h3>
                <p className="text-muted-foreground mb-4">
                  This interactive simulator demonstrates how lending works in DeFi. You can deposit tokens into a
                  lending pool and see how interest accrues over time.
                </p>

                <div className="bg-primary/5 p-6 rounded-lg border">
                  <div className="mb-6">
                    <h4 className="font-medium mb-4">Wallet Balance: ${simulatorBalance.toFixed(2)}</h4>
                    <Progress value={(simulatorBalance/100) * 100} className="h-2 mb-1" />
                  </div>

                  {simulatorStep === 1 && (
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className="space-y-4"
                    >
                      <h4 className="font-medium">Step 1: Choose Deposit Amount</h4>
                      <p className="text-sm text-muted-foreground">
                        Select how much you want to deposit into the DeFi lending pool. This amount will earn 5% APY.
                      </p>

                      <div className="space-y-2">
                        <label htmlFor="depositAmount" className="text-sm font-medium">
                          Deposit Amount (max: ${simulatorBalance.toFixed(2)})
                        </label>
                        <input
                          type="range"
                          id="depositAmount"
                          min="0"
                          max={simulatorBalance}
                          value={depositAmount}
                          onChange={handleDepositChange}
                          className="w-full"
                        />
                        <div className="flex justify-between">
                          <span className="text-sm">$0</span>
                          <span className="text-sm font-medium">${depositAmount.toFixed(2)}</span>
                          <span className="text-sm">${simulatorBalance.toFixed(2)}</span>
                        </div>
                      </div>

                      <Button onClick={handleNextSimulatorStep} disabled={depositAmount <= 0}>
                        Deposit Funds <ArrowRight className="ml-1 h-4 w-4" />
                      </Button>
                    </motion.div>
                  )}

                  {simulatorStep === 2 && (
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className="space-y-4"
                    >
                      <h4 className="font-medium">Step 2: Funds Deposited</h4>
                      <p className="text-sm text-muted-foreground">
                        You've successfully deposited ${depositAmount.toFixed(2)} into the lending pool.
                        Your funds are now earning interest at 5% APY.
                      </p>

                      <div className="p-4 border rounded-md bg-background">
                        <div className="flex justify-between mb-2">
                          <span className="text-sm">Principal Amount:</span>
                          <span className="font-medium">${depositAmount.toFixed(2)}</span>
                        </div>
                        <div className="flex justify-between mb-2">
                          <span className="text-sm">Interest Rate:</span>
                          <span className="font-medium">5% APY</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm">Time Period:</span>
                          <span className="font-medium">1 Year</span>
                        </div>
                      </div>

                      <Button onClick={handleNextSimulatorStep}>
                        Fast Forward 1 Year <ArrowRight className="ml-1 h-4 w-4" />
                      </Button>
                    </motion.div>
                  )}

                  {simulatorStep === 3 && (
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className="space-y-4"
                    >
                      <h4 className="font-medium">Step 3: Results After 1 Year</h4>
                      <p className="text-sm text-muted-foreground">
                        After holding your deposit for 1 year, you've earned interest on your investment.
                      </p>

                      <div className="p-4 border rounded-md bg-background">
                        <div className="flex justify-between mb-2">
                          <span className="text-sm">Initial Deposit:</span>
                          <span className="font-medium">${depositAmount.toFixed(2)}</span>
                        </div>
                        <div className="flex justify-between mb-2">
                          <span className="text-sm">Interest Earned:</span>
                          <span className="font-medium text-green-500">+${interestEarned.toFixed(2)}</span>
                        </div>
                        <div className="flex justify-between mb-2 border-t pt-2">
                          <span className="text-sm font-medium">Total Value:</span>
                          <span className="font-medium">${(depositAmount + interestEarned).toFixed(2)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm">New Wallet Balance:</span>
                          <span className="font-medium">${simulatorBalance.toFixed(2)}</span>
                        </div>
                      </div>

                      <p className="text-sm text-muted-foreground">
                        In DeFi, interest rates adjust dynamically based on supply and demand. During periods
                        of high demand for borrowing, rates can be significantly higher than traditional finance.
                      </p>

                      <Button onClick={handleNextSimulatorStep}>
                        Start Again <ArrowRight className="ml-1 h-4 w-4" />
                      </Button>
                    </motion.div>
                  )}
                </div>
              </section>

              <section>
                <h3 className="text-lg font-medium mb-2">Key Takeaways</h3>
                <div className="space-y-3">
                  <div>
                    <h4 className="font-medium">Permissionless</h4>
                    <p className="text-sm text-muted-foreground">
                      Anyone with a crypto wallet can participate in DeFi lending without credit checks or bank approval.
                    </p>
                  </div>

                  <div>
                    <h4 className="font-medium">Collateralized</h4>
                    <p className="text-sm text-muted-foreground">
                      Most DeFi loans are over-collateralized, meaning borrowers must deposit more value than they borrow.
                    </p>
                  </div>

                  <div>
                    <h4 className="font-medium">Dynamic Rates</h4>
                    <p className="text-sm text-muted-foreground">
                      Interest rates in DeFi platforms adjust algorithmically based on utilization rates.
                    </p>
                  </div>

                  <div>
                    <h4 className="font-medium">Liquidity Risks</h4>
                    <p className="text-sm text-muted-foreground">
                      Lending protocols depend on adequate liquidity for users to withdraw funds when needed.
                    </p>
                  </div>
                </div>

                <div className="mt-6 flex justify-center">
                  <Button onClick={handleQuizStart}>
                    Take DeFi Quiz <ArrowRight className="ml-1 h-4 w-4" />
                  </Button>
                </div>
              </section>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
