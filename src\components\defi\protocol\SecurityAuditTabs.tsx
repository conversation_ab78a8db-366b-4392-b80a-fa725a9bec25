
// React import removed - using new JSX transform
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { CheckCircle2 } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveContainer, <PERSON><PERSON><PERSON>, Legend } from "recharts";
import SecurityIssuesBadge from "./SecurityIssuesBadge";

interface SecurityAuditTabsProps {
  protocol: any | null;
  isLoading: boolean;
  securityPieData: Array<{
    name: string;
    value: number;
    color: string;
  }>;
  formatCurrency: (value: number) => string;
}

const SecurityAuditTabs = ({
  protocol,
  isLoading,
  securityPieData,
  formatCurrency
}: SecurityAuditTabsProps) => {
  if (isLoading || !protocol) return null;

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <div className="lg:col-span-2">
        <Tabs defaultValue="audits" className="w-full">
          <TabsList>
            <TabsTrigger value="audits">Audit History</TabsTrigger>
            <TabsTrigger value="incidents">Security Incidents</TabsTrigger>
          </TabsList>

          <TabsContent value="audits" className="pt-4">
            {protocol.audits && protocol.audits.length > 0 ? (
              <div className="border rounded-md overflow-hidden">
                <table className="w-full">
                  <thead className="bg-secondary">
                    <tr>
                      <th className="px-4 py-2 text-left">Audit Firm</th>
                      <th className="px-4 py-2 text-left">Date</th>
                      <th className="px-4 py-2 text-left">Status</th>
                      <th className="px-4 py-2 text-left">Issues Found</th>
                    </tr>
                  </thead>
                  <tbody>
                    {protocol.audits.map((audit: any, index: number) => (
                      <tr key={index} className="border-t">
                        <td className="px-4 py-3">{audit.firm}</td>
                        <td className="px-4 py-3">{new Date(audit.date).toLocaleDateString()}</td>
                        <td className="px-4 py-3">
                          <Badge variant={audit.status === 'Completed' ? 'success' : 'default'}>
                            {audit.status}
                          </Badge>
                        </td>
                        <td className="px-4 py-3">
                          <SecurityIssuesBadge audit={audit} />
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-muted-foreground">No audit information available</p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="incidents" className="pt-4">
            {protocol.exploits && protocol.exploits.length > 0 ? (
              <div className="border rounded-md overflow-hidden">
                <table className="w-full">
                  <thead className="bg-secondary">
                    <tr>
                      <th className="px-4 py-2 text-left">Date</th>
                      <th className="px-4 py-2 text-left">Description</th>
                      <th className="px-4 py-2 text-left">Amount Lost</th>
                      <th className="px-4 py-2 text-left">Status</th>
                    </tr>
                  </thead>
                  <tbody>
                    {protocol.exploits.map((exploit: any, index: number) => (
                      <tr key={index} className="border-t">
                        <td className="px-4 py-3">{new Date(exploit.date).toLocaleDateString()}</td>
                        <td className="px-4 py-3">{exploit.description}</td>
                        <td className="px-4 py-3">{formatCurrency(exploit.amount)}</td>
                        <td className="px-4 py-3">
                          <Badge variant={exploit.fixed ? 'success' : 'destructive'}>
                            {exploit.fixed ? 'Fixed' : 'Unresolved'}
                          </Badge>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center py-8 text-green-500">
                <CheckCircle2 className="h-8 w-8 mx-auto mb-2" />
                <p>No security incidents reported</p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>

      <div className="lg:col-span-1">
        <div className="space-y-4">
          <h3 className="font-medium">Security Issues by Severity</h3>

          {securityPieData.length > 0 ? (
            <div className="h-[250px]">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={securityPieData}
                    dataKey="value"
                    nameKey="name"
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    label={(entry) => entry.name}
                  >
                    {securityPieData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value, name) => [`${value}`, name]} />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </div>
          ) : (
            <div className="text-center py-8">
              <p className="text-muted-foreground">No security data available</p>
            </div>
          )}

          <div className="space-y-1">
            <h4 className="text-sm font-medium">Security Notes</h4>
            <ul className="text-sm text-muted-foreground pl-5 list-disc">
              {protocol.bugBounty ? (
                <li>Has active bug bounty program</li>
              ) : (
                <li>No bug bounty program detected</li>
              )}
              <li>{protocol.audits?.length || 0} security audits conducted</li>
              <li>Security score: {protocol.securityScore}/10</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SecurityAuditTabs;
