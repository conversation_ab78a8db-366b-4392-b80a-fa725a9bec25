
import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { WalletActivityChart } from "@/components/WalletActivityChart";
import { TrendingSection } from "@/components/TrendingSection";
import { ProjectsTable } from "@/components/ProjectsTable";

interface RightAnalyticsPanelProps {
  trending: any;
  transformedProjects: any[];
}

export function RightAnalyticsPanel({
  trending,
  transformedProjects
}: RightAnalyticsPanelProps) {
  return (
    <>
      {/* Market depth */}
      <Card className="bg-card border-border">
        <CardHeader className="pb-3">
          <CardTitle className="text-foreground text-sm font-semibold uppercase tracking-wide">
            Portfolio Monitor
          </CardTitle>
        </CardHeader>
        <CardContent>
          <WalletActivityChart />
        </CardContent>
      </Card>

      {/* News terminal */}
      <Card className="bg-card border-border">
        <CardHeader className="pb-3">
          <CardTitle className="text-foreground text-sm font-semibold uppercase tracking-wide">
            Market Intelligence
          </CardTitle>
        </CardHeader>
        <CardContent>
          <TrendingSection tokens={trending} />
        </CardContent>
      </Card>

      {/* Recently added projects */}
      <Card className="bg-card border-border">
        <CardHeader className="pb-3">
          <CardTitle className="text-foreground text-sm font-semibold uppercase tracking-wide">
            New Listings
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <ProjectsTable projects={transformedProjects.slice(0, 5)} />
        </CardContent>
      </Card>
    </>
  );
}
