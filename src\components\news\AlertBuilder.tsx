
import { useState } from "react";
import { AlertRule } from "@/hooks/useNewsSentiment";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Skeleton } from "@/components/ui/skeleton";
import { useNewsSentiment } from "@/hooks/useNewsSentiment";
import { AlertDialog } from "./AlertDialog";
import { AlertCard } from "./AlertCard";
import { EmptyAlertState } from "./EmptyAlertState";

interface AlertBuilderProps {
  rules: AlertRule[];
  isLoading: boolean;
}

export default function AlertBuilder({ rules, isLoading }: AlertBuilderProps) {
  const { toast } = useToast();
  const [isOpen, setIsOpen] = useState(false);
  const { addAlertRule, toggleAlertRule, deleteAlertRule } = useNewsSentiment();

  const handleSubmit = (formData: Omit<AlertRule, "id" | "active">) => {
    // Now using the real mutation from the hook
    addAlertRule({...formData, active: true}, {
      onSuccess: () => {
        toast({
          title: "Alert rule created",
          description: `Alert "${formData.name}" has been created.`,
        });
        setIsOpen(false);
      },
      onError: (error) => {
        toast({
          title: "Error creating alert",
          description: error instanceof Error ? error.message : "An unknown error occurred",
          variant: "destructive",
        });
      }
    });
  };

  const handleToggleRule = (id: string) => {
    // Now using the real mutation from the hook
    toggleAlertRule(id, {
      onSuccess: () => {
        toast({
          title: "Alert status changed",
          description: "The alert has been toggled.",
        });
      },
      onError: (error) => {
        toast({
          title: "Error toggling alert",
          description: error instanceof Error ? error.message : "An unknown error occurred",
          variant: "destructive",
        });
      }
    });
  };

  const handleDeleteRule = (id: string) => {
    // Now using the real mutation from the hook
    deleteAlertRule(id, {
      onSuccess: () => {
        toast({
          title: "Alert deleted",
          description: "The alert has been removed.",
        });
      },
      onError: (error) => {
        toast({
          title: "Error deleting alert",
          description: error instanceof Error ? error.message : "An unknown error occurred",
          variant: "destructive",
        });
      }
    });
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold">Alert Rules</h2>
          <Skeleton className="h-9 w-[120px]" />
        </div>
        {[1, 2, 3].map(i => (
          <Card key={i}>
            <Skeleton className="h-16 w-full" />
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold">Alert Rules</h2>
        <Button onClick={() => setIsOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          New Alert
        </Button>
      </div>

      {rules.length === 0 ? (
        <EmptyAlertState onCreateClick={() => setIsOpen(true)} />
      ) : (
        rules.map(rule => (
          <AlertCard
            key={rule.id}
            rule={rule}
            onToggle={handleToggleRule}
            onDelete={handleDeleteRule}
          />
        ))
      )}

      <AlertDialog
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        onSubmit={handleSubmit}
      />
    </div>
  );
}
