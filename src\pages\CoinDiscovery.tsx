import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  TrendingUp, 
  Star, 
  Activity, 
  Code, 
  Users, 
  AlertCircle, 
  RefreshCw,
  Search,
  Filter,
  BarChart3
} from "lucide-react";
import { 
  fetchCoinDiscoveryData, 
  generatePersonalizedRecommendations 
} from "@/services/api/coinDiscoveryService";
import { 
  getMarketOverview, 
  getMarketInsights,
  enhanceCoinData,
  EnhancedCoinData
} from "@/services/api/discovery/enhancedDiscoveryService";
import EnhancedCoinCard from "@/components/discovery/EnhancedCoinCard";
import MarketOverviewDashboard from "@/components/discovery/MarketOverviewDashboard";
import TrendingChart from "@/components/discovery/TrendingChart";
import CoinAnalysisModal from "@/components/discovery/CoinAnalysisModal";
import HeaderBar from "@/components/HeaderBar";
import TokenExplorer from "@/components/education/TokenExplorer";

export default function CoinDiscovery() {
  const [activeTab, setActiveTab] = useState("overview");
  const [riskProfile, setRiskProfile] = useState("moderate");
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState("opportunity_score");
  const [filterRisk, setFilterRisk] = useState("all");
  const [selectedCoinForAnalysis, setSelectedCoinForAnalysis] = useState<EnhancedCoinData | null>(null);
  const [isAnalysisModalOpen, setIsAnalysisModalOpen] = useState(false);

  // Market overview data
  const { data: marketOverview, isLoading: overviewLoading } = useQuery({
    queryKey: ['marketOverview'],
    queryFn: getMarketOverview,
    refetchInterval: 30000,
  });

  const { data: marketInsights, isLoading: insightsLoading } = useQuery({
    queryKey: ['marketInsights'],
    queryFn: getMarketInsights,
    refetchInterval: 60000,
  });

  // Discovery data
  const { data: discoveryData, isLoading: discoveryLoading, error, refetch } = useQuery({
    queryKey: ['coinDiscovery'],
    queryFn: () => fetchCoinDiscoveryData(),
    refetchInterval: 30000,
  });

  // Personalized data
  const { data: personalizedData, isLoading: personalizedLoading } = useQuery({
    queryKey: ['personalizedRecommendations', riskProfile],
    queryFn: () => generatePersonalizedRecommendations(riskProfile),
    refetchInterval: 60000,
  });

  // Enhanced coin processing
  const processCoinsData = (coins: any[]) => {
    if (!coins) return [];
    
    let enhanced = coins.map(enhanceCoinData);
    
    // Apply search filter
    if (searchTerm) {
      enhanced = enhanced.filter(coin => 
        coin.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        coin.symbol.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    
    // Apply risk filter
    if (filterRisk !== "all") {
      enhanced = enhanced.filter(coin => coin.risk_level === filterRisk);
    }
    
    // Apply sorting
    enhanced.sort((a, b) => {
      switch (sortBy) {
        case "opportunity_score":
          return b.opportunity_score - a.opportunity_score;
        case "momentum_score":
          return b.momentum_score - a.momentum_score;
        case "market_cap":
          return b.market_cap - a.market_cap;
        case "price_change":
          return b.price_change_percentage_24h - a.price_change_percentage_24h;
        default:
          return 0;
      }
    });
    
    return enhanced;
  };

  const handleAnalyzeCoin = (coinId: string) => {
    const coin = [...(discoveryData?.trendingCoins || []), ...(discoveryData?.emergingCoins || []), ...(discoveryData?.developmentActiveCoins || []), ...(personalizedData?.coins || [])]
      .find(c => c.id === coinId);
    
    if (coin) {
      setSelectedCoinForAnalysis(coin);
      setIsAnalysisModalOpen(true);
    }
  };

  const handleAddToWatchlist = (coinId: string) => {
    console.log("Adding to watchlist:", coinId);
    // TODO: Implement watchlist functionality
  };

  if (error) {
    return (
      <div className="flex-1 flex flex-col min-w-0">
        <HeaderBar 
          title="Coin Discovery & Trend Explorer" 
          description="Comprehensive cryptocurrency analysis with AI-powered insights and real-time market data"
        />
        <main className="flex-1 overflow-y-auto p-4 md:p-6 lg:p-8">
          <div className="container mx-auto px-4 py-8">
            <Card>
              <CardContent className="p-8 text-center">
                <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
                <p className="text-muted-foreground mb-4">Failed to load discovery data. Please try again.</p>
                <Button onClick={() => refetch()} variant="outline">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Retry
                </Button>
              </CardContent>
            </Card>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col min-w-0">
      <HeaderBar 
        title="Coin Discovery & Trend Explorer" 
        description="Comprehensive cryptocurrency analysis with AI-powered insights and real-time market data"
      />
      
      <main className="flex-1 overflow-y-auto p-4 md:p-6 lg:p-8">
        <div className="container mx-auto px-4 py-8 space-y-6 max-w-7xl">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">Coin Discovery & Trend Explorer</h1>
              <p className="text-muted-foreground mt-2">
                Comprehensive cryptocurrency analysis with AI-powered insights and real-time market data
              </p>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="flex items-center gap-2">
                <Activity className="h-4 w-4" />
                Live Data
              </Badge>
              <Button
                onClick={() => refetch()}
                variant="outline"
                size="sm"
                disabled={discoveryLoading}
              >
                <RefreshCw className={`h-4 w-4 ${discoveryLoading ? 'animate-spin' : ''}`} />
              </Button>
            </div>
          </div>

          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="overview">
                <BarChart3 className="h-4 w-4 mr-2" />
                Market Overview
              </TabsTrigger>
              <TabsTrigger value="trending">
                <TrendingUp className="h-4 w-4 mr-2" />
                Trending
              </TabsTrigger>
              <TabsTrigger value="emerging">
                <Star className="h-4 w-4 mr-2" />
                Emerging
              </TabsTrigger>
              <TabsTrigger value="development">
                <Code className="h-4 w-4 mr-2" />
                Development Active
              </TabsTrigger>
              <TabsTrigger value="personalized">
                <Users className="h-4 w-4 mr-2" />
                Personalized
              </TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              {overviewLoading || insightsLoading ? (
                <div className="flex items-center gap-2 text-muted-foreground">
                  <RefreshCw className="h-4 w-4 animate-spin" />
                  <span>Loading latest data...</span>
                </div>
              ) : null}
              
              <MarketOverviewDashboard 
                data={marketOverview || {
                  totalMarketCap: 0,
                  marketCapChange24h: 0,
                  totalVolume: 0,
                  volumeChange24h: 0,
                  fearGreedIndex: 50,
                  dominance: { btc: 45, eth: 18 },
                  topGainers: [],
                  topLosers: []
                }}
                insights={marketInsights || "Loading market insights..."}
                isLoading={overviewLoading || insightsLoading}
              />
              
              {discoveryData?.trendingData && (
                <Card>
                  <CardHeader>
                    <CardTitle>Market Momentum Visualization</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <TrendingChart data={discoveryData.trendingData} isLoading={discoveryLoading} />
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            {/* Trending Tab */}
            <TabsContent value="trending" className="space-y-6">
              {/* AI Insights for Trending */}
              {discoveryData?.trendingInsights && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <TrendingUp className="h-5 w-5 text-primary" />
                      AI Market Insights
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm leading-relaxed">{discoveryData.trendingInsights}</p>
                  </CardContent>
                </Card>
              )}

              <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
                <div className="flex flex-col sm:flex-row gap-2 flex-1">
                  <div className="relative flex-1 max-w-sm">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search trending coins..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-9"
                    />
                  </div>
                  <Select value={sortBy} onValueChange={setSortBy}>
                    <SelectTrigger className="w-48">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="opportunity_score">Opportunity Score</SelectItem>
                      <SelectItem value="momentum_score">Momentum</SelectItem>
                      <SelectItem value="market_cap">Market Cap</SelectItem>
                      <SelectItem value="price_change">Price Change</SelectItem>
                    </SelectContent>
                  </Select>
                  <Select value={filterRisk} onValueChange={setFilterRisk}>
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Risk</SelectItem>
                      <SelectItem value="low">Low Risk</SelectItem>
                      <SelectItem value="medium">Medium Risk</SelectItem>
                      <SelectItem value="high">High Risk</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {discoveryLoading ? (
                  Array(9).fill(0).map((_, i) => (
                    <div key={i} className="animate-pulse h-80 bg-gray-200 rounded-lg"></div>
                  ))
                ) : (
                  processCoinsData(discoveryData?.trendingCoins || []).map((coin) => (
                    <EnhancedCoinCard 
                      key={coin.id} 
                      coin={coin}
                      onAnalyze={handleAnalyzeCoin}
                      onWatchlist={handleAddToWatchlist}
                    />
                  ))
                )}
              </div>

              {!discoveryLoading && (!discoveryData?.trendingCoins || discoveryData.trendingCoins.length === 0) && (
                <Card>
                  <CardContent className="p-8 text-center">
                    <AlertCircle className="h-12 w-12 text-amber-500 mx-auto mb-4" />
                    <p className="text-muted-foreground mb-4">No trending coins available at the moment.</p>
                    <Button onClick={() => refetch()} variant="outline">
                      <RefreshCw className="h-4 w-4 mr-2" />
                      Refresh Data
                    </Button>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            {/* Emerging Tab */}
            <TabsContent value="emerging" className="space-y-6">
              <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
                <div>
                  <h3 className="text-xl font-semibold">Emerging Opportunities</h3>
                  <p className="text-muted-foreground">
                    High-potential cryptocurrencies with strong growth indicators and momentum
                  </p>
                </div>
                <div className="flex gap-2">
                  <Select value={sortBy} onValueChange={setSortBy}>
                    <SelectTrigger className="w-48">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="opportunity_score">Opportunity Score</SelectItem>
                      <SelectItem value="momentum_score">Momentum</SelectItem>
                      <SelectItem value="market_cap">Market Cap</SelectItem>
                      <SelectItem value="price_change">Price Change</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {discoveryLoading ? (
                  Array(6).fill(0).map((_, i) => (
                    <div key={i} className="animate-pulse h-80 bg-gray-200 rounded-lg"></div>
                  ))
                ) : (
                  processCoinsData(discoveryData?.emergingCoins || []).map((coin) => (
                    <EnhancedCoinCard 
                      key={coin.id} 
                      coin={coin}
                      onAnalyze={handleAnalyzeCoin}
                      onWatchlist={handleAddToWatchlist}
                    />
                  ))
                )}
              </div>
            </TabsContent>

            {/* Development Active Tab */}
            <TabsContent value="development" className="space-y-6">
              <div className="mb-4">
                <h3 className="text-xl font-semibold mb-2">Development Active Projects</h3>
                <p className="text-muted-foreground">
                  Cryptocurrencies with high development activity and community engagement
                </p>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {discoveryLoading ? (
                  Array(6).fill(0).map((_, i) => (
                    <div key={i} className="animate-pulse h-80 bg-gray-200 rounded-lg"></div>
                  ))
                ) : (
                  processCoinsData(discoveryData?.developmentActiveCoins || []).map((coin) => (
                    <EnhancedCoinCard 
                      key={coin.id} 
                      coin={coin}
                      onAnalyze={handleAnalyzeCoin}
                      onWatchlist={handleAddToWatchlist}
                    />
                  ))
                )}
              </div>
            </TabsContent>

            {/* Personalized Tab */}
            <TabsContent value="personalized" className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-xl font-semibold">Personalized Recommendations</h3>
                  <p className="text-muted-foreground">AI-curated picks based on your risk profile</p>
                </div>
                <Select value={riskProfile} onValueChange={setRiskProfile}>
                  <SelectTrigger className="w-40">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="conservative">Conservative</SelectItem>
                    <SelectItem value="moderate">Moderate</SelectItem>
                    <SelectItem value="aggressive">Aggressive</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* AI Insights for personalized */}
              {personalizedData && (
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 mb-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm">AI Insights</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm">{personalizedData.aiInsights}</p>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm">Risk Assessment</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm">{personalizedData.riskAssessment}</p>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm">Strategy</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm">{personalizedData.rationale}</p>
                    </CardContent>
                  </Card>
                </div>
              )}
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {personalizedLoading ? (
                  Array(6).fill(0).map((_, i) => (
                    <div key={i} className="animate-pulse h-80 bg-gray-200 rounded-lg"></div>
                  ))
                ) : (
                  processCoinsData(personalizedData?.coins || []).map((coin) => (
                    <EnhancedCoinCard 
                      key={coin.id} 
                      coin={coin}
                      onAnalyze={handleAnalyzeCoin}
                      onWatchlist={handleAddToWatchlist}
                    />
                  ))
                )}
              </div>
            </TabsContent>
          </Tabs>

          {/* Analysis Modal */}
          <CoinAnalysisModal
            coin={selectedCoinForAnalysis}
            isOpen={isAnalysisModalOpen}
            onClose={() => {
              setIsAnalysisModalOpen(false);
              setSelectedCoinForAnalysis(null);
            }}
          />
        </div>
      </main>
    </div>
  );
}
