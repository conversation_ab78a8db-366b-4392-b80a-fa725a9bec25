import { useEffect } from 'react';
import { useAuth } from '@/contexts/auth/useAuth';

export function DebugComponent() {
  const { user, isLoading, session } = useAuth();

  useEffect(() => {
    console.log('DebugComponent mounted');
    console.log('Auth state:', { user: !!user, isLoading, session: !!session });
  }, [user, isLoading, session]);

  return (
    <div className="min-h-screen bg-background text-foreground p-8">
      <h1 className="text-2xl font-bold mb-4">Debug Information</h1>
      <div className="space-y-2">
        <p>User: {user ? 'Authenticated' : 'Not authenticated'}</p>
        <p>Loading: {isLoading ? 'Yes' : 'No'}</p>
        <p>Session: {session ? 'Active' : 'None'}</p>
        <p>User ID: {user?.id || 'N/A'}</p>
        <p>User Email: {user?.email || 'N/A'}</p>
      </div>
    </div>
  );
}
