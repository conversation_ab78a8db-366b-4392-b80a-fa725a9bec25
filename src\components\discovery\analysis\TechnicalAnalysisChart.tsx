
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area, BarChart, Bar } from "recharts";
import { TrendingUp, TrendingDown, Activity } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { CoinData, PriceHistoryPoint, AnalysisProps } from "./types";
import { useQuery } from "@tanstack/react-query";
import { coinGeckoAxios } from "@/services/api/coinGeckoClient";

interface TechnicalAnalysisChartProps extends AnalysisProps {
  priceHistory: PriceHistoryPoint[];
}

export function TechnicalAnalysisChart({ coinData, priceHistory, isLoading }: TechnicalAnalysisChartProps) {
  // Fetch real historical data for technical indicators
  const { data: historicalData, isLoading: historicalLoading } = useQuery({
    queryKey: ['historical-data', coinData.id],
    queryFn: async () => {
      try {
        const response = await coinGeckoAxios.get(`/coins/${coinData.id}/market_chart`, {
          params: {
            vs_currency: 'usd',
            days: '30',
            interval: 'daily'
          }
        });
        
        return response.data;
      } catch (error) {
        console.error("Error fetching historical data:", error);
        return null;
      }
    },
    enabled: !!coinData.id,
    staleTime: 10 * 60 * 1000,
  });

  // Calculate technical indicators from real data
  const calculateTechnicalIndicators = (prices: number[]) => {
    const rsi = calculateRSI(prices);
    const sma20 = calculateSMA(prices, 20);
    const sma50 = calculateSMA(prices, 50);
    const macd = calculateMACD(prices);
    
    return { rsi, sma20, sma50, macd };
  };

  const calculateRSI = (prices: number[], period: number = 14) => {
    if (prices.length < period + 1) return 50;
    
    let gains = 0;
    let losses = 0;
    
    for (let i = 1; i <= period; i++) {
      const change = prices[prices.length - i] - prices[prices.length - i - 1];
      if (change > 0) gains += change;
      else losses -= change;
    }
    
    const avgGain = gains / period;
    const avgLoss = losses / period;
    const rs = avgGain / avgLoss;
    
    return 100 - (100 / (1 + rs));
  };

  const calculateSMA = (prices: number[], period: number) => {
    if (prices.length < period) return prices[prices.length - 1];
    
    const slice = prices.slice(-period);
    return slice.reduce((sum, price) => sum + price, 0) / period;
  };

  const calculateMACD = (prices: number[]) => {
    if (prices.length < 26) return 0;
    
    const ema12 = calculateEMA(prices, 12);
    const ema26 = calculateEMA(prices, 26);
    
    return ema12 - ema26;
  };

  const calculateEMA = (prices: number[], period: number) => {
    if (prices.length < period) return prices[prices.length - 1];
    
    const multiplier = 2 / (period + 1);
    let ema = prices[0];
    
    for (let i = 1; i < prices.length; i++) {
      ema = (prices[i] * multiplier) + (ema * (1 - multiplier));
    }
    
    return ema;
  };

  if (isLoading || historicalLoading) {
    return (
      <div className="space-y-4">
        <div className="animate-pulse h-80 bg-gray-200 rounded-lg"></div>
        <div className="animate-pulse h-60 bg-gray-200 rounded-lg"></div>
      </div>
    );
  }

  // Process real historical data
  let processedData = priceHistory;
  let currentRSI = 50;
  let currentMACD = 0;
  let supportLevel = coinData.current_price * 0.95;
  let resistanceLevel = coinData.current_price * 1.08;

  if (historicalData && historicalData.prices) {
    const prices = historicalData.prices.map((item: [number, number]) => item[1]);
    const volumes = historicalData.total_volumes.map((item: [number, number]) => item[1]);
    
    const indicators = calculateTechnicalIndicators(prices);
    currentRSI = indicators.rsi;
    currentMACD = indicators.macd;
    
    // Calculate support and resistance from actual price data
    const minPrice = Math.min(...prices);
    const maxPrice = Math.max(...prices);
    supportLevel = minPrice + (coinData.current_price - minPrice) * 0.2;
    resistanceLevel = maxPrice - (maxPrice - coinData.current_price) * 0.2;
    
    // Create processed data with real indicators
    processedData = historicalData.prices.map((pricePoint: [number, number], index: number) => {
      const date = new Date(pricePoint[0]).toISOString().split('T')[0];
      const price = pricePoint[1];
      const volume = volumes[index] ? volumes[index][1] : 0;
      
      // Calculate indicators for this point
      const localPrices = prices.slice(0, index + 1);
      const localIndicators = calculateTechnicalIndicators(localPrices);
      
      return {
        date,
        price,
        volume,
        rsi: localIndicators.rsi,
        macd: localIndicators.macd,
        sma20: localIndicators.sma20,
        sma50: localIndicators.sma50
      };
    });
  }

  const rsiSignal = currentRSI > 70 ? 'Overbought' : currentRSI < 30 ? 'Oversold' : 'Neutral';
  const macdSignal = currentMACD > 0 ? 'Bullish' : 'Bearish';

  return (
    <div className="space-y-6">
      {/* Technical Indicators Summary */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-sm text-muted-foreground">RSI (14)</div>
            <div className="text-2xl font-bold">{currentRSI.toFixed(1)}</div>
            <Badge variant={currentRSI > 70 ? 'destructive' : currentRSI < 30 ? 'secondary' : 'default'}>
              {rsiSignal}
            </Badge>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="text-sm text-muted-foreground">MACD</div>
            <div className="text-2xl font-bold">{currentMACD.toFixed(4)}</div>
            <Badge variant={currentMACD > 0 ? 'default' : 'destructive'}>
              {macdSignal}
            </Badge>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="text-sm text-muted-foreground">Support</div>
            <div className="text-2xl font-bold">${supportLevel.toFixed(4)}</div>
            <div className="text-xs text-green-600">Strong</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="text-sm text-muted-foreground">Resistance</div>
            <div className="text-2xl font-bold">${resistanceLevel.toFixed(4)}</div>
            <div className="text-xs text-red-600">Moderate</div>
          </CardContent>
        </Card>
      </div>

      {/* Price Chart with Moving Averages */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Price Action & Moving Averages
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={processedData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip 
                formatter={(value, name) => [`$${Number(value).toFixed(4)}`, name]}
                labelFormatter={(label) => `Date: ${label}`}
              />
              <Line type="monotone" dataKey="price" stroke="#2563eb" strokeWidth={2} name="Price" />
              <Line type="monotone" dataKey="sma20" stroke="#f59e0b" strokeWidth={1} name="SMA 20" />
              <Line type="monotone" dataKey="sma50" stroke="#ef4444" strokeWidth={1} name="SMA 50" />
            </LineChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* RSI Chart */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            RSI Oscillator
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={200}>
            <AreaChart data={processedData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis domain={[0, 100]} />
              <Tooltip formatter={(value) => [Number(value).toFixed(1), 'RSI']} />
              <Area 
                type="monotone" 
                dataKey="rsi" 
                stroke="#8884d8" 
                fill="#8884d8" 
                fillOpacity={0.3}
              />
            </AreaChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Volume Analysis */}
      <Card>
        <CardHeader>
          <CardTitle>Volume Profile</CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={200}>
            <BarChart data={processedData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip formatter={(value) => [Number(value).toLocaleString(), 'Volume']} />
              <Bar dataKey="volume" fill="#10b981" />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
    </div>
  );
}
