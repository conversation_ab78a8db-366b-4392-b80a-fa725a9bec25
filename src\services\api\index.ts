
// Core API clients
export * from "./coinGeckoClient";
export {
  fetchTopCoins,
  fetchCoinData,
  fetchGlobalData,
  fetchTrendingCoins,
  getCoinMarketData
} from "./coinMarketData";

// Analysis services
export * from "./sentimentAnalysis";
export * from "./priceHistory";
export * from "./deepSeekClient";
export * from "./searchService";
export * from "./whaleAlertApi";
export * from "./newsUtils";

// Feature-specific services
export * from "./defiApi";
export * from "./marketInsightsApi";
export * from "./anomalyDetectionService";
export * from "./portfolioService";
export * from "./fundamentalAnalysisService";
export * from "./aiInsightsService";
export * from "./coinDiscoveryService";
export * from "./newsItems";

// Discovery modules
export * from "./discovery/emergingCoinsService";
export * from "./discovery/personalizedService";
export * from "./discovery/coinAnalysisService";
export * from "./discovery/trendingDataService";
export * from "./discovery/enhancedDiscoveryService";

// Analysis modules
export * from "./analysis/coinAnalysisService";

// Fundamental analysis modules
export * from "./fundamental";
