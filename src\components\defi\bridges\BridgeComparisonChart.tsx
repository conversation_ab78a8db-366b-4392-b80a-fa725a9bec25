
import { useMemo } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent, CardDescription } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar, Legend, Tooltip as Rechart<PERSON><PERSON>tip, ResponsiveContainer } from "recharts";

interface BridgeComparisonChartProps {
  isLoading: boolean;
  sourceChain: string;
  bridges: any[];
}

export function BridgeComparisonChart({ isLoading, sourceChain, bridges }: BridgeComparisonChartProps) {
  // Filter bridges that support source chain
  const filteredBridges = useMemo(() => {
    if (!bridges || bridges.length === 0) return [];
    return bridges.filter(bridge => bridge.supportedNetworks.includes(sourceChain));
  }, [bridges, sourceChain]);

  // Sort bridges by security score to get the top ones
  const sortedBridges = useMemo(() => {
    if (!filteredBridges || filteredBridges.length === 0) return [];
    return [...filteredBridges].sort((a, b) => b.securityScore - a.securityScore);
  }, [filteredBridges]);

  // Generate comparison data for radar chart
  const comparisonData = useMemo(() => {
    if (!sortedBridges || sortedBridges.length === 0) return [];

    // Use at most top 3 bridges for comparison
    const topBridges = sortedBridges.slice(0, 3);

    // Create radar chart data structure
    const metrics = [
      { name: "Security", key: "securityScore", max: 10 },
      { name: "Speed", key: "speed", max: 10, inverse: true },
      { name: "Cost", key: "fees.percentage", max: 0.01, inverse: true },
      { name: "Volume", key: "volume24h", max: 1000000000 },
      { name: "User Rating", key: "userRating", max: 5 }
    ];

    return metrics.map(metric => {
      const dataPoint: any = {
        metric: metric.name
      };

      topBridges.forEach(bridge => {
        let value;

        // Handle special cases
        if (metric.key === "speed") {
          // Lower is better for speed, so we need to invert
          const rawSpeed = bridge.speed[sourceChain.toLowerCase()] || 10;
          // Transform to 0-10 scale (lower is better, so invert)
          value = 10 - Math.min(10, rawSpeed / 3);
        } else if (metric.key === "fees.percentage") {
          // Transform percentage to 0-10 scale (lower is better, so invert)
          value = 10 - (bridge.fees.percentage * 1000);
        } else if (metric.key === "volume24h") {
          // Transform volume to 0-10 scale
          value = Math.min(10, bridge.volume24h / 100000000);
        } else {
          // Direct values (already on 0-10 or 0-5 scale)
          value = bridge[metric.key];
        }

        dataPoint[bridge.name] = value;
      });

      return dataPoint;
    });
  }, [sortedBridges, sourceChain]);

  if (sortedBridges.length === 0) {
    return null;
  }

  return (
    <Card className="mt-6">
      <CardHeader>
        <CardTitle>Bridge Performance Comparison</CardTitle>
        <CardDescription>
          Comparing top bridges across key metrics
        </CardDescription>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <Skeleton className="h-80 w-full" />
        ) : (
          <div className="h-[350px]">
            <ResponsiveContainer width="100%" height="100%">
              <RadarChart cx="50%" cy="50%" outerRadius="80%" data={comparisonData}>
                <PolarGrid />
                <PolarAngleAxis dataKey="metric" />
                <PolarRadiusAxis angle={30} domain={[0, 10]} />

                {sortedBridges.slice(0, 3).map((bridge, index) => {
                  const colors = ["hsl(var(--chart-1))", "hsl(var(--chart-2))", "hsl(var(--chart-3))"];
                  return (
                    <Radar
                      key={bridge.id}
                      name={bridge.name}
                      dataKey={bridge.name}
                      stroke={colors[index]}
                      fill={colors[index]}
                      fillOpacity={0.2}
                    />
                  );
                })}

                <Legend />
                <RechartTooltip />
              </RadarChart>
            </ResponsiveContainer>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
