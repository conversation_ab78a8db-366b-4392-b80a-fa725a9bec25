
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Plus, AlertTriangle } from "lucide-react";

interface EmptyAlertStateProps {
  onCreateClick: () => void;
}

export function EmptyAlertState({ onCreateClick }: EmptyAlertStateProps) {
  return (
    <Card>
      <CardContent className="p-6 flex flex-col items-center justify-center">
        <AlertTriangle className="h-12 w-12 text-muted-foreground mb-3" />
        <h3 className="text-lg font-medium">No alerts configured</h3>
        <p className="text-muted-foreground text-center mt-1 mb-4">
          Create alerts to get notified about significant market sentiment changes
        </p>
        <Button onClick={onCreateClick}>
          <Plus className="mr-2 h-4 w-4" />
          Create your first alert
        </Button>
      </CardContent>
    </Card>
  );
}
