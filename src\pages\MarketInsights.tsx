
import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Loader2, TrendingUp, ArrowDown, ArrowUp, Info } from "lucide-react";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableHeader, TableBody, TableRow, TableHead, TableCell } from "@/components/ui/table";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { HeaderBar } from "@/components/HeaderBar";
import StatCard from "@/components/StatCard";
import GlobalMarketStats from "@/components/market/GlobalMarketStats";
import MarketTrends from "@/components/market/MarketTrends";
import ExchangesList from "@/components/market/ExchangesList";
import PriceCorrelation from "@/components/market/PriceCorrelation";
import { fetchGlobalData, fetchTopCoins } from "@/services/api/coinMarketData";
import { toast } from "@/hooks/use-toast";

export default function MarketInsights() {
  const [activeTab, setActiveTab] = useState("overview");

  const { data: globalData, isLoading: isLoadingGlobal, error: globalError } = useQuery({
    queryKey: ['globalMarketData'],
    queryFn: fetchGlobalData,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const { data: topCoins, isLoading: isLoadingCoins, error: coinsError } = useQuery({
    queryKey: ['topCoins'],
    queryFn: () => fetchTopCoins(50),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  if (globalError || coinsError) {
    toast({
      title: "Error loading data",
      description: "Could not load market data. Please try again later.",
      variant: "destructive",
    });
  }

  return (
    <div className="flex-1 flex flex-col min-w-0">
      <HeaderBar
        title="Market Insights"
        description="Comprehensive overview of the cryptocurrency market"
      />

      <main className="flex-1 overflow-y-auto p-4 md:p-6 lg:p-8">
        <div className="max-w-screen-2xl mx-auto space-y-6">
          {(isLoadingGlobal || isLoadingCoins) && (
            <div className="flex items-center gap-2 text-muted-foreground">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span>Loading latest data...</span>
            </div>
          )}

          {globalData && !isLoadingGlobal && (
            <GlobalMarketStats globalData={globalData} />
          )}

          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid grid-cols-4 md:w-[500px]">
              <TabsTrigger value="overview">Market Overview</TabsTrigger>
              <TabsTrigger value="trending">Trending</TabsTrigger>
              <TabsTrigger value="exchanges">Exchanges</TabsTrigger>
              <TabsTrigger value="correlation">Correlations</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6 mt-6">
              {topCoins && !isLoadingCoins ? (
                <TopCoinsTable coins={topCoins} />
              ) : (
                <Card>
                  <CardContent className="flex items-center justify-center min-h-[400px]">
                    <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="trending" className="mt-6">
              <MarketTrends />
            </TabsContent>

            <TabsContent value="exchanges" className="mt-6">
              <ExchangesList />
            </TabsContent>

            <TabsContent value="correlation" className="mt-6">
              <PriceCorrelation />
            </TabsContent>
          </Tabs>
        </div>
      </main>
    </div>
  );
}

const TopCoinsTable = ({ coins }: { coins: any[] }) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TrendingUp className="h-5 w-5" />
          Top Cryptocurrencies by Market Cap
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">#</TableHead>
              <TableHead>Name</TableHead>
              <TableHead className="text-right">Price</TableHead>
              <TableHead className="text-right">24h %</TableHead>
              <TableHead className="text-right">7d %</TableHead>
              <TableHead className="text-right">Market Cap</TableHead>
              <TableHead className="text-right">Volume (24h)</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {coins.map((coin, index) => (
              <TableRow key={coin.id}>
                <TableCell>{index + 1}</TableCell>
                <TableCell className="font-medium">
                  <div className="flex items-center gap-2">
                    <div className="w-6 h-6 rounded-full overflow-hidden">
                      <img src={coin.image} alt={coin.name} className="w-full h-full object-cover" />
                    </div>
                    <span>{coin.name}</span>
                    <span className="text-muted-foreground uppercase">{coin.symbol}</span>
                  </div>
                </TableCell>
                <TableCell className="text-right font-mono">
                  ${coin.current_price.toLocaleString(undefined, { maximumFractionDigits: 8 })}
                </TableCell>
                <TableCell className="text-right">
                  <PriceChange value={coin.price_change_percentage_24h} />
                </TableCell>
                <TableCell className="text-right">
                  <PriceChange value={coin.price_change_percentage_7d_in_currency} />
                </TableCell>
                <TableCell className="text-right font-mono">
                  ${formatLargeNumber(coin.market_cap)}
                </TableCell>
                <TableCell className="text-right font-mono">
                  ${formatLargeNumber(coin.total_volume)}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
};

const PriceChange = ({ value }: { value: number }) => {
  if (!value && value !== 0) return <span>--</span>;

  const isPositive = value >= 0;
  return (
    <div className={`flex items-center justify-end ${isPositive ? 'text-crypto-positive' : 'text-crypto-negative'}`}>
      {isPositive ? <ArrowUp className="h-3 w-3 mr-1" /> : <ArrowDown className="h-3 w-3 mr-1" />}
      {Math.abs(value).toFixed(2)}%
    </div>
  );
};

const formatLargeNumber = (num: number): string => {
  if (num >= 1000000000) {
    return (num / 1000000000).toFixed(2) + 'B';
  }
  if (num >= 1000000) {
    return (num / 1000000).toFixed(2) + 'M';
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(2) + 'K';
  }
  return num.toString();
};
