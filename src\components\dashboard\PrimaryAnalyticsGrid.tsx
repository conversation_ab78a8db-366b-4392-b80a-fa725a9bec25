
import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { KeyMetricsBar } from "./KeyMetricsBar";
import { LeftAnalyticsPanel } from "./LeftAnalyticsPanel";
import { RightAnalyticsPanel } from "./RightAnalyticsPanel";

interface PrimaryAnalyticsGridProps {
  stats: any;
  tvlData: any;
  fearGreed: any;
  trending: any;
  ratingAssets: any;
  ratingLoading: boolean;
  transformedProjects: any[];
}

export function PrimaryAnalyticsGrid({
  stats,
  tvlData,
  fearGreed,
  trending,
  ratingAssets,
  ratingLoading,
  transformedProjects
}: PrimaryAnalyticsGridProps) {
  return (
    <div className="grid grid-cols-12 gap-6">

      {/* Left panel - Market overview */}
      <div className="col-span-8 space-y-6">
        <KeyMetricsBar stats={stats} fearGreed={fearGreed} />
        <LeftAnalyticsPanel
          ratingAssets={ratingAssets}
          ratingLoading={ratingLoading}
          tvlData={tvlData}
          fearGreed={fearGreed}
        />
      </div>

      {/* Right panel - News and analytics */}
      <div className="col-span-4 space-y-6">
        <RightAnalyticsPanel
          trending={trending}
          transformedProjects={transformedProjects}
        />
      </div>
    </div>
  );
}
