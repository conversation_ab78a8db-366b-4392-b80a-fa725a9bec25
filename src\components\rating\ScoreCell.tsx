
import { cn } from "@/lib/utils";

interface ScoreCellProps {
  score: number;
}

export function ScoreCell({ score }: ScoreCellProps) {
  let colorClass = "bg-muted text-muted-foreground";

  if (score >= 8) colorClass = "bg-crypto-positive/20 text-crypto-positive";
  else if (score >= 6) colorClass = "bg-chart-1/20 text-chart-1";
  else if (score >= 4) colorClass = "bg-chart-3/20 text-chart-3";
  else colorClass = "bg-destructive/20 text-destructive";

  return (
    <span className={cn("px-2 py-0.5 rounded font-medium", colorClass)}>
      {score.toFixed(1)}
    </span>
  );
}
