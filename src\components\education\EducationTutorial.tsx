
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import {
  ChevronLeft,
  ChevronRight,
  BookOpen,
  CheckCircle,
  Clock,
  AlertCircle,
  HelpCircle
} from "lucide-react";
import { Tutorial, TutorialStep } from "@/hooks/useEducation";
import { useEducation } from "@/hooks/useEducation";
import { useToast } from "@/hooks/use-toast";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";

interface EducationTutorialProps {
  tutorial: Tutorial;
}

export function EducationTutorial({ tutorial }: EducationTutorialProps) {
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [quizAnswer, setQuizAnswer] = useState<number | null>(null);
  const [showExplanation, setShowExplanation] = useState(false);
  const { updateProgress, completeTutorial } = useEducation();
  const { toast } = useToast();
  const navigate = useNavigate();

  const currentStep = tutorial.steps[currentStepIndex];
  const isFirstStep = currentStepIndex === 0;
  const isLastStep = currentStepIndex === tutorial.steps.length - 1;

  // Calculate progress percentage
  const progressPercentage = Math.round(((currentStepIndex + 1) / tutorial.steps.length) * 100);

  const handleNext = () => {
    if (isLastStep) {
      completeTutorial(tutorial.id);
      navigate('/education');
      return;
    }

    // If this is a quiz step and no answer was selected, show a warning
    if (currentStep.quizQuestion && quizAnswer === null) {
      toast({
        title: "Please answer the question",
        description: "Select an answer before proceeding",
        variant: "destructive"
      });
      return;
    }

    setCurrentStepIndex(currentStepIndex + 1);
    setQuizAnswer(null);
    setShowExplanation(false);
    updateProgress(tutorial.id, currentStepIndex + 1);
  };

  const handlePrevious = () => {
    if (isFirstStep) {
      navigate('/education');
      return;
    }

    setCurrentStepIndex(currentStepIndex - 1);
    setQuizAnswer(null);
    setShowExplanation(false);
  };

  const handleQuizAnswer = (answerIndex: number) => {
    setQuizAnswer(answerIndex);
    setShowExplanation(true);

    if (currentStep.quizQuestion && answerIndex === currentStep.quizQuestion.correctAnswer) {
      toast({
        title: "Correct!",
        description: "Great job understanding this concept.",
        variant: "default"
      });
    } else {
      toast({
        title: "Not quite!",
        description: "Review the explanation and try again.",
        variant: "default"
      });
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <div className="space-y-1">
          <h1 className="text-2xl font-bold tracking-tight">{tutorial.title}</h1>
          <div className="flex flex-wrap items-center gap-2 text-sm text-muted-foreground">
            <Badge variant="outline">{tutorial.level}</Badge>
            <div className="flex items-center gap-1">
              <Clock size={14} />
              <span>{tutorial.estimatedTime}</span>
            </div>
            <div className="flex items-center gap-1">
              <BookOpen size={14} />
              <span>{tutorial.category}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Progress bar */}
      <div className="w-full space-y-1">
        <Progress value={progressPercentage} className="h-2" />
        <div className="flex justify-between text-xs text-muted-foreground">
          <span>Progress</span>
          <span>{progressPercentage}%</span>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
        {/* Steps sidebar */}
        <Card className="hidden lg:block lg:col-span-3">
          <CardContent className="p-4">
            <h3 className="font-medium mb-3">Tutorial Steps</h3>
            <ScrollArea className="h-[calc(100vh-300px)]">
              <div className="space-y-1 pr-4">
                {tutorial.steps.map((step, index) => (
                  <Button
                    key={step.id}
                    variant={index === currentStepIndex ? "default" : "ghost"}
                    className="w-full justify-start text-left h-auto py-2"
                    onClick={() => {
                      setCurrentStepIndex(index);
                      setQuizAnswer(null);
                      setShowExplanation(false);
                    }}
                  >
                    <div className="flex items-center gap-2">
                      {index < currentStepIndex ? (
                        <CheckCircle size={16} className="text-crypto-positive" />
                      ) : (
                        <span className="w-4 h-4 rounded-full bg-muted flex items-center justify-center text-xs">
                          {index + 1}
                        </span>
                      )}
                      <span className="line-clamp-2">{step.title}</span>
                    </div>
                  </Button>
                ))}
              </div>
            </ScrollArea>
          </CardContent>
        </Card>

        {/* Main content */}
        <Card className="lg:col-span-9">
          <CardContent className="p-6 space-y-6">
            <div>
              <h2 className="text-xl font-semibold mb-1">{currentStep.title}</h2>
              <p className="text-muted-foreground">{currentStep.description}</p>
            </div>

            <Separator />

            <div className="space-y-6">
              {/* Content section */}
              <div className="prose dark:prose-invert max-w-none">
                {currentStep.content.split('\n\n').map((paragraph, i) => (
                  <p key={i}>{paragraph}</p>
                ))}
              </div>

              {/* Image if available */}
              {currentStep.imageUrl && (
                <div className="my-6">
                  <img
                    src={currentStep.imageUrl}
                    alt={currentStep.title}
                    className="rounded-lg max-h-96 w-full object-cover"
                  />
                </div>
              )}

              {/* Video if available */}
              {currentStep.videoUrl && (
                <div className="my-6 aspect-video">
                  <iframe
                    src={currentStep.videoUrl}
                    className="w-full h-full rounded-lg"
                    title={currentStep.title}
                    allowFullScreen
                  ></iframe>
                </div>
              )}

              {/* Quiz question if available */}
              {currentStep.quizQuestion && (
                <div className="p-6 bg-muted/50 rounded-lg space-y-4">
                  <div className="flex items-start gap-2">
                    <HelpCircle className="text-primary mt-0.5" size={20} />
                    <h3 className="font-medium">{currentStep.quizQuestion.question}</h3>
                  </div>

                  <div className="space-y-2">
                    {currentStep.quizQuestion.options.map((option, index) => (
                      <div key={index} className="flex items-center">
                        <Button
                          type="button"
                          variant={quizAnswer === index ? (
                            index === currentStep.quizQuestion!.correctAnswer ? "default" : "destructive"
                          ) : "outline"}
                          className="w-full justify-start text-left"
                          onClick={() => handleQuizAnswer(index)}
                          disabled={quizAnswer !== null}
                        >
                          <span>{option}</span>
                          {quizAnswer === index && index === currentStep.quizQuestion!.correctAnswer && (
                            <CheckCircle className="ml-auto h-4 w-4 text-crypto-positive" />
                          )}
                        </Button>
                      </div>
                    ))}
                  </div>

                  {showExplanation && (
                    <div className="p-4 bg-background border rounded-md mt-4">
                      <div className="flex gap-2">
                        <AlertCircle className="text-primary mt-0.5" size={18} />
                        <div>
                          <h4 className="font-medium mb-1">Explanation:</h4>
                          <p className="text-sm">{currentStep.quizQuestion.explanation}</p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Navigation buttons */}
      <div className="flex justify-between">
        <Button
          variant="ghost"
          onClick={handlePrevious}
          className="flex items-center gap-2"
        >
          <ChevronLeft size={16} />
          {isFirstStep ? "Back to Education" : "Previous Step"}
        </Button>

        <Button
          onClick={handleNext}
          className="flex items-center gap-2"
        >
          {isLastStep ? "Complete Tutorial" : "Next Step"}
          <ChevronRight size={16} />
        </Button>
      </div>
    </div>
  );
}
