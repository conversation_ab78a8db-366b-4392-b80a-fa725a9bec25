import axios from "axios";

// Constants
const DEEPSEEK_API_URL = "https://api.deepseek.com";
const DEEPSEEK_API_KEY = "***********************************";

// Configure axios with API key
export const deepSeekAxios = axios.create({
  baseURL: DEEPSEEK_API_URL,
  headers: {
    'Authorization': `Bearer ${DEEPSEEK_API_KEY}`,
    'Content-Type': 'application/json'
  },
  timeout: 30000
});

// Helper function to log and handle API errors
export const handleDeepSeekApiError = (error: any, fallback: any = null) => {
  console.error("DeepSeek API error:", error);
  
  if (error.response) {
    console.warn(`DeepSeek API returned status code ${error.response.status}`);
  }
  
  return fallback;
};

// Process text through DeepSeek AI with proper error handling
export const generateAIResponse = async (prompt: string, options: any = {}) => {
  try {
    const response = await deepSeekAxios.post("/v1/chat/completions", {
      model: "deepseek-chat",
      messages: [
        { role: "user", content: prompt }
      ],
      max_tokens: options.maxTokens || 1024,
      temperature: options.temperature || 0.7,
      ...options
    });
    
    return response.data.choices[0].message.content;
  } catch (error) {
    console.error("Error calling DeepSeek API:", error);
    
    // Provide intelligent fallbacks based on prompt type
    if (prompt.includes("trending") || prompt.includes("market")) {
      return "Market analysis indicates mixed sentiment with active trading across multiple sectors. Consider monitoring volume patterns and momentum indicators for emerging opportunities.";
    } else if (prompt.includes("analysis") || prompt.includes("coin")) {
      return "Technical analysis suggests evaluating multiple factors including price action, volume trends, and market sentiment before making investment decisions.";
    } else if (prompt.includes("strategy") || prompt.includes("investment")) {
      return "Consider a diversified approach with proper risk management, focusing on established projects while maintaining exposure to emerging opportunities.";
    }
    
    return "AI analysis is temporarily unavailable. Please refer to fundamental metrics and technical indicators for decision making.";
  }
};

// Generate analysis for crypto asset with fallback
export const generateAssetAnalysis = async (assetData: any) => {
  const prompt = `
    Analyze the following cryptocurrency asset data and provide a concise analysis:
    Name: ${assetData.name} (${assetData.symbol})
    Price: $${assetData.current_price || assetData.price}
    Market Cap: $${assetData.market_cap || assetData.marketCap}
    24h Change: ${assetData.price_change_percentage_24h || assetData.change24h}%
    
    Please provide:
    1. Brief market sentiment analysis
    2. Key strengths and weaknesses
    3. Short-term outlook (1-2 sentences)
  `;
  
  const result = await generateAIResponse(prompt, { temperature: 0.3 });
  
  // If AI fails, provide basic analysis based on data
  if (!result || result.includes("temporarily unavailable")) {
    const change = assetData.price_change_percentage_24h || assetData.change24h || 0;
    const sentiment = change > 5 ? "bullish" : change < -5 ? "bearish" : "neutral";
    
    return `${assetData.name} shows ${sentiment} sentiment with ${Math.abs(change).toFixed(1)}% movement. Market cap of $${(assetData.market_cap || 0).toLocaleString()} indicates ${assetData.market_cap > 1e9 ? "established" : "emerging"} status. Consider current market conditions and fundamental metrics for investment decisions.`;
  }
  
  return result;
};

// Generate investment strategy recommendations
export const generateInvestmentStrategy = async (userProfile: any) => {
  const prompt = `
    Create a personalized crypto investment strategy based on the following user profile:
    Risk Tolerance: ${userProfile.riskTolerance}
    Investment Horizon: ${userProfile.timeHorizon}
    Investment Goals: ${userProfile.goals}
    
    Suggest a portfolio allocation strategy with 3-5 cryptocurrencies, explaining the rationale.
  `;
  
  return generateAIResponse(prompt, { temperature: 0.4, maxTokens: 1500 });
};

// Generate narrative for market trends
export const generateMarketNarrative = async (marketData: any) => {
  const prompt = `
    Based on the following market data, explain the current market trends and factors driving them:
    Total Market Cap: $${marketData.totalMarketCap} trillion
    BTC Dominance: ${marketData.btcDominance}%
    24h Change: ${marketData.change24h}%
    Top Gainers: ${marketData.topGainers.map((g: any) => `${g.symbol}: ${g.change}%`).join(', ')}
    Top Losers: ${marketData.topLosers.map((l: any) => `${l.symbol}: ${l.change}%`).join(', ')}
    
    Provide a concise analysis of market sentiment and key driving factors.
  `;
  
  return generateAIResponse(prompt, { temperature: 0.3 });
};
