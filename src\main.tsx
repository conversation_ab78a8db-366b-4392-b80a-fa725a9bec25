
import { StrictMode } from "react";
import ReactDOM from "react-dom/client";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import { QueryClientProvider, QueryClient } from "@tanstack/react-query";
import { ThemeProvider } from "./contexts/theme-provider";
import { AuthProvider } from "@/contexts/auth/AuthProvider";
import { Toaster } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import RequireAuth from "@/components/RequireAuth";
import { DashboardLayout } from "@/components/DashboardLayout";
import ErrorBoundary from "@/components/ErrorBoundary";

// Pages
import { Index } from "./pages/Index";
import { Dashboard } from "./pages/Dashboard";
import { NotFound } from "./pages/NotFound";
import { Auth } from "./pages/Auth";
import { ProfileSettings } from "./pages/ProfileSettings";
import { DeFiOpportunities } from "./pages/DeFiOpportunities";
import { OnChainAnalytics } from "./pages/OnChainAnalytics";
import MarketInsights from "./pages/MarketInsights";
import Portfolio from "./pages/Portfolio";
import NewsSentiment from "./pages/NewsSentiment";
import { Education } from "./pages/Education";
import { SmartMoney } from "./pages/SmartMoney";
import { FundamentalAnalysis } from "./pages/FundamentalAnalysis";
import Anomalies from "./pages/Anomalies";
import Ratings from "./pages/Ratings";
import Forecasting from "./pages/Forecasting";
import AdvancedVisualizations from "./pages/AdvancedVisualizations";
import AIInsights from "./pages/AIInsights";
import { TokenScamDetector } from "./pages/TokenScamDetector";
import CoinDiscovery from "./pages/CoinDiscovery";

// Styles
import "./index.css";

// Create a client with optimized configuration
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      retry: 2,
      refetchOnWindowFocus: false,
    },
    mutations: {
      retry: 1,
    },
  },
});

ReactDOM.createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <ThemeProvider defaultTheme="system" storageKey="cryptovision-theme">
          <AuthProvider>
            <TooltipProvider>
              <Router>
              <Routes>
                <Route path="/auth" element={<Auth />} />
                <Route path="/*" element={
                  <DashboardLayout>
                    <Routes>
                      <Route index element={<RequireAuth><Index /></RequireAuth>} />
                      <Route path="dashboard" element={<RequireAuth><Dashboard /></RequireAuth>} />
                      <Route path="profile" element={<RequireAuth><ProfileSettings /></RequireAuth>} />
                      <Route path="defi-opportunities" element={<RequireAuth><DeFiOpportunities /></RequireAuth>} />
                      <Route path="onchain-analytics" element={<RequireAuth><OnChainAnalytics /></RequireAuth>} />
                      <Route path="market-insights" element={<RequireAuth><MarketInsights /></RequireAuth>} />
                      <Route path="ai-insights" element={<RequireAuth><AIInsights /></RequireAuth>} />
                      <Route path="token-scam-detector" element={<RequireAuth><TokenScamDetector /></RequireAuth>} />
                      <Route path="coin-discovery" element={<RequireAuth><CoinDiscovery /></RequireAuth>} />
                      <Route path="portfolio" element={<RequireAuth><Portfolio /></RequireAuth>} />
                      <Route path="news-sentiment" element={<RequireAuth><NewsSentiment /></RequireAuth>} />
                      <Route path="education" element={<RequireAuth><Education /></RequireAuth>} />
                      <Route path="education/:section" element={<RequireAuth><Education /></RequireAuth>} />
                      <Route path="education/:section/:id" element={<RequireAuth><Education /></RequireAuth>} />
                      <Route path="smart-money" element={<RequireAuth><SmartMoney /></RequireAuth>} />
                      <Route path="fundamental-analysis" element={<RequireAuth><FundamentalAnalysis /></RequireAuth>} />
                      <Route path="anomalies" element={<RequireAuth><Anomalies /></RequireAuth>} />
                      <Route path="ratings" element={<RequireAuth><Ratings /></RequireAuth>} />
                      <Route path="forecasting" element={<RequireAuth><Forecasting /></RequireAuth>} />
                      <Route path="advanced-visualizations" element={<RequireAuth><AdvancedVisualizations /></RequireAuth>} />
                      <Route path="*" element={<NotFound />} />
                    </Routes>
                  </DashboardLayout>
                } />
                </Routes>
              </Router>
              <Toaster />
            </TooltipProvider>
          </AuthProvider>
        </ThemeProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  </StrictMode>
);
