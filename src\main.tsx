
import { StrictMode } from "react";
import ReactDOM from "react-dom/client";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import { QueryClientProvider, QueryClient } from "@tanstack/react-query";
import { ThemeProvider } from "./contexts/theme-provider";
import { AuthProvider } from "@/contexts/auth/AuthProvider";
import { Toaster } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { RequireAuth } from "@/components/RequireAuth";
import { DashboardLayout } from "@/components/DashboardLayout";
import { ErrorBoundary } from "@/components/ErrorBoundary";

// Pages
import { Index } from "./pages/Index";
import { Dashboard } from "./pages/Dashboard";
import { NotFound } from "./pages/NotFound";
import { Auth } from "./pages/Auth";
import { ProfileSettings } from "./pages/ProfileSettings";
import { DeFiOpportunities } from "./pages/DeFiOpportunities";
import { OnChainAnalytics } from "./pages/OnChainAnalytics";
import { MarketInsights } from "./pages/MarketInsights";
import { Portfolio } from "./pages/Portfolio";
import { NewsSentiment } from "./pages/NewsSentiment";
import { Education } from "./pages/Education";
import { SmartMoney } from "./pages/SmartMoney";
import { FundamentalAnalysis } from "./pages/FundamentalAnalysis";
import { Anomalies } from "./pages/Anomalies";
import { Ratings } from "./pages/Ratings";
import { Forecasting } from "./pages/Forecasting";
import { AdvancedVisualizations } from "./pages/AdvancedVisualizations";
import { AIInsights } from "./pages/AIInsights";
import { TokenScamDetector } from "./pages/TokenScamDetector";
import { CoinDiscovery } from "./pages/CoinDiscovery";

// Styles
import "./index.css";

// Create a client with optimized configuration
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      retry: 2,
      refetchOnWindowFocus: false,
    },
    mutations: {
      retry: 1,
    },
  },
});

// Debug function to log errors
function logError(error: any, context: string) {
  console.error(`[${context}] Error:`, error);
  // Also try to display error on page
  const root = document.getElementById("root");
  if (root) {
    root.innerHTML = `
      <div style="background: red; color: white; padding: 20px; font-family: monospace;">
        <h1>Error in ${context}</h1>
        <pre>${error.toString()}</pre>
        <pre>${error.stack || 'No stack trace'}</pre>
      </div>
    `;
  }
}

// Simple dashboard component
function SimpleDashboard() {
  const isDemoMode = window.location.search.includes('demo=true');

  return (
    <div className="min-h-screen bg-background text-foreground">
      {/* Demo Mode Banner */}
      {isDemoMode && (
        <div className="bg-warning text-warning-foreground px-4 py-2 text-center text-sm font-medium">
          🚀 Demo Mode - Exploring CryptoVision Pro
        </div>
      )}

      {/* Header */}
      <header className="border-b bg-card">
        <div className="container mx-auto px-4 py-4">
          <h1 className="text-2xl font-bold">CryptoVision Pro</h1>
          <p className="text-muted-foreground">Advanced Cryptocurrency Analytics Platform</p>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        <div className="grid gap-6">
          <div className="bg-card border rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4">✅ Application Status</h2>
            <div className="space-y-2 text-sm">
              <p><strong>Status:</strong> <span className="text-green-600">Fully Operational</span></p>
              <p><strong>Demo Mode:</strong> {isDemoMode ? 'Enabled' : 'Disabled'}</p>
              <p><strong>Theme:</strong> System Default</p>
              <p><strong>Build:</strong> Development</p>
            </div>
          </div>

          <div className="grid md:grid-cols-3 gap-4">
            <div className="bg-card border rounded-lg p-4">
              <h3 className="font-medium text-primary">Market Data</h3>
              <p className="text-2xl font-bold mt-2">Ready</p>
            </div>
            <div className="bg-card border rounded-lg p-4">
              <h3 className="font-medium text-primary">AI Analytics</h3>
              <p className="text-2xl font-bold mt-2">Ready</p>
            </div>
            <div className="bg-card border rounded-lg p-4">
              <h3 className="font-medium text-primary">Portfolio</h3>
              <p className="text-2xl font-bold mt-2">Ready</p>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}

try {
  const root = document.getElementById("root");
  if (!root) {
    throw new Error("Root element not found!");
  }

  console.log("Starting React app...");

  ReactDOM.createRoot(root).render(
    <StrictMode>
      <ErrorBoundary>
        <QueryClientProvider client={queryClient}>
          <ThemeProvider defaultTheme="system" storageKey="cryptovision-theme">
            <AuthProvider>
              <TooltipProvider>
                <Router>
                  <Routes>
                    <Route path="/auth" element={<Auth />} />
                    <Route path="/*" element={
                      <DashboardLayout>
                        <Routes>
                          <Route index element={<RequireAuth><Index /></RequireAuth>} />
                          <Route path="dashboard" element={<RequireAuth><Dashboard /></RequireAuth>} />
                          <Route path="profile" element={<RequireAuth><ProfileSettings /></RequireAuth>} />
                          <Route path="defi-opportunities" element={<RequireAuth><DeFiOpportunities /></RequireAuth>} />
                          <Route path="onchain-analytics" element={<RequireAuth><OnChainAnalytics /></RequireAuth>} />
                          <Route path="market-insights" element={<RequireAuth><MarketInsights /></RequireAuth>} />
                          <Route path="ai-insights" element={<RequireAuth><AIInsights /></RequireAuth>} />
                          <Route path="token-scam-detector" element={<RequireAuth><TokenScamDetector /></RequireAuth>} />
                          <Route path="coin-discovery" element={<RequireAuth><CoinDiscovery /></RequireAuth>} />
                          <Route path="portfolio" element={<RequireAuth><Portfolio /></RequireAuth>} />
                          <Route path="news-sentiment" element={<RequireAuth><NewsSentiment /></RequireAuth>} />
                          <Route path="education" element={<RequireAuth><Education /></RequireAuth>} />
                          <Route path="education/:section" element={<RequireAuth><Education /></RequireAuth>} />
                          <Route path="education/:section/:id" element={<RequireAuth><Education /></RequireAuth>} />
                          <Route path="smart-money" element={<RequireAuth><SmartMoney /></RequireAuth>} />
                          <Route path="fundamental-analysis" element={<RequireAuth><FundamentalAnalysis /></RequireAuth>} />
                          <Route path="anomalies" element={<RequireAuth><Anomalies /></RequireAuth>} />
                          <Route path="ratings" element={<RequireAuth><Ratings /></RequireAuth>} />
                          <Route path="forecasting" element={<RequireAuth><Forecasting /></RequireAuth>} />
                          <Route path="advanced-visualizations" element={<RequireAuth><AdvancedVisualizations /></RequireAuth>} />
                          <Route path="*" element={<NotFound />} />
                        </Routes>
                      </DashboardLayout>
                    } />
                  </Routes>
                  <Toaster />
                </Router>
              </TooltipProvider>
            </AuthProvider>
          </ThemeProvider>
        </QueryClientProvider>
      </ErrorBoundary>
    </StrictMode>
  );

  console.log("React app started successfully!");
} catch (error) {
  logError(error, "Main App Initialization");
}
