
import { StrictMode } from "react";
import ReactDOM from "react-dom/client";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import { QueryClientProvider, QueryClient } from "@tanstack/react-query";
import { ThemeProvider } from "./contexts/theme-provider";
import { AuthProvider } from "@/contexts/auth/AuthProvider";
import { Toaster } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { RequireAuth } from "@/components/RequireAuth";
import { DashboardLayout } from "@/components/DashboardLayout";
import { ErrorBoundary } from "@/components/ErrorBoundary";

// Pages
import { Index } from "./pages/Index";
import { Dashboard } from "./pages/Dashboard";
import { NotFound } from "./pages/NotFound";
import { Auth } from "./pages/Auth";
import { ProfileSettings } from "./pages/ProfileSettings";
import { DeFiOpportunities } from "./pages/DeFiOpportunities";
import { OnChainAnalytics } from "./pages/OnChainAnalytics";
import { MarketInsights } from "./pages/MarketInsights";
import { Portfolio } from "./pages/Portfolio";
import { NewsSentiment } from "./pages/NewsSentiment";
import { Education } from "./pages/Education";
import { SmartMoney } from "./pages/SmartMoney";
import { FundamentalAnalysis } from "./pages/FundamentalAnalysis";
import { Anomalies } from "./pages/Anomalies";
import { Ratings } from "./pages/Ratings";
import { Forecasting } from "./pages/Forecasting";
import { AdvancedVisualizations } from "./pages/AdvancedVisualizations";
import { AIInsights } from "./pages/AIInsights";
import { TokenScamDetector } from "./pages/TokenScamDetector";
import { CoinDiscovery } from "./pages/CoinDiscovery";

// Styles
import "./index.css";

// Create a client with optimized configuration
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      retry: 2,
      refetchOnWindowFocus: false,
    },
    mutations: {
      retry: 1,
    },
  },
});

// Debug function to log errors
function logError(error: any, context: string) {
  console.error(`[${context}] Error:`, error);
  // Also try to display error on page
  const root = document.getElementById("root");
  if (root) {
    root.innerHTML = `
      <div style="background: red; color: white; padding: 20px; font-family: monospace;">
        <h1>Error in ${context}</h1>
        <pre>${error.toString()}</pre>
        <pre>${error.stack || 'No stack trace'}</pre>
      </div>
    `;
  }
}

// Simple dashboard component
function SimpleDashboard() {
  const isDemoMode = window.location.search.includes('demo=true');

  return (
    <div className="min-h-screen bg-background text-foreground">
      {/* Demo Mode Banner */}
      {isDemoMode && (
        <div className="bg-warning text-warning-foreground px-4 py-2 text-center text-sm font-medium">
          🚀 Demo Mode - Exploring CryptoVision Pro
        </div>
      )}

      {/* Header */}
      <header className="border-b bg-card">
        <div className="container mx-auto px-4 py-4">
          <h1 className="text-2xl font-bold">CryptoVision Pro</h1>
          <p className="text-muted-foreground">Advanced Cryptocurrency Analytics Platform</p>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        <div className="grid gap-6">
          <div className="bg-card border rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4">✅ Application Status</h2>
            <div className="space-y-2 text-sm">
              <p><strong>Status:</strong> <span className="text-green-600">Fully Operational</span></p>
              <p><strong>Demo Mode:</strong> {isDemoMode ? 'Enabled' : 'Disabled'}</p>
              <p><strong>Theme:</strong> System Default</p>
              <p><strong>Build:</strong> Development</p>
            </div>
          </div>

          <div className="grid md:grid-cols-3 gap-4">
            <div className="bg-card border rounded-lg p-4">
              <h3 className="font-medium text-primary">Market Data</h3>
              <p className="text-2xl font-bold mt-2">Ready</p>
            </div>
            <div className="bg-card border rounded-lg p-4">
              <h3 className="font-medium text-primary">AI Analytics</h3>
              <p className="text-2xl font-bold mt-2">Ready</p>
            </div>
            <div className="bg-card border rounded-lg p-4">
              <h3 className="font-medium text-primary">Portfolio</h3>
              <p className="text-2xl font-bold mt-2">Ready</p>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}

// Professional Dashboard Component
function ProfessionalDashboard() {
  const isDemoMode = window.location.search.includes('demo=true');

  return (
    <div className="flex-1 flex flex-col min-w-0 bg-background text-foreground overflow-hidden">
      {/* Header Bar */}
      <div className="border-b bg-card px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">Dashboard</h1>
            <p className="text-muted-foreground">Professional cryptocurrency analytics platform</p>
          </div>
          <div className="flex items-center gap-4">
            {isDemoMode && (
              <div className="bg-chart-3 text-white px-3 py-1 rounded-full text-xs font-medium">
                🚀 Demo Mode
              </div>
            )}
            <button className="bg-primary text-primary-foreground px-4 py-2 rounded-md text-sm font-medium hover:bg-primary/90">
              Refresh Data
            </button>
          </div>
        </div>
      </div>

      {/* Market Ticker */}
      <div className="bg-muted/30 border-b px-6 py-3">
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center gap-6">
            <div className="flex items-center gap-2">
              <span className="text-muted-foreground">BTC:</span>
              <span className="font-medium">$43,250</span>
              <span className="text-green-600">+2.4%</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-muted-foreground">ETH:</span>
              <span className="font-medium">$2,650</span>
              <span className="text-green-600">+3.1%</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-muted-foreground">Market Cap:</span>
              <span className="font-medium">$2.1T</span>
            </div>
          </div>
          <div className="text-muted-foreground">
            Last updated: {new Date().toLocaleTimeString()}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <main className="flex-1 overflow-y-auto bg-background">
        <div className="max-w-screen-2xl mx-auto p-6 space-y-6">
          {/* Key Metrics */}
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            <div className="bg-card border rounded-lg p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Market Cap</p>
                  <p className="text-2xl font-bold">$2.1T</p>
                </div>
                <div className="h-8 w-8 bg-green-100 rounded-full flex items-center justify-center">
                  <span className="text-green-600 text-sm">📈</span>
                </div>
              </div>
              <p className="text-xs text-green-600 mt-2">+2.4% from yesterday</p>
            </div>

            <div className="bg-card border rounded-lg p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">24h Volume</p>
                  <p className="text-2xl font-bold">$89.2B</p>
                </div>
                <div className="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <span className="text-blue-600 text-sm">💰</span>
                </div>
              </div>
              <p className="text-xs text-green-600 mt-2">+5.1% from yesterday</p>
            </div>

            <div className="bg-card border rounded-lg p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">BTC Dominance</p>
                  <p className="text-2xl font-bold">54.2%</p>
                </div>
                <div className="h-8 w-8 bg-orange-100 rounded-full flex items-center justify-center">
                  <span className="text-orange-600 text-sm">₿</span>
                </div>
              </div>
              <p className="text-xs text-red-600 mt-2">-0.3% from yesterday</p>
            </div>

            <div className="bg-card border rounded-lg p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Fear & Greed</p>
                  <p className="text-2xl font-bold">72</p>
                </div>
                <div className="h-8 w-8 bg-green-100 rounded-full flex items-center justify-center">
                  <span className="text-green-600 text-sm">😊</span>
                </div>
              </div>
              <p className="text-xs text-muted-foreground mt-2">Greed</p>
            </div>
          </div>

          {/* Charts and Analytics */}
          <div className="grid gap-6 lg:grid-cols-2">
            <div className="bg-card border rounded-lg p-6">
              <h3 className="text-lg font-semibold mb-4">Market Overview</h3>
              <div className="h-64 bg-muted/20 rounded-lg flex items-center justify-center">
                <div className="text-center">
                  <div className="text-4xl mb-2">📊</div>
                  <p className="text-muted-foreground">Interactive chart coming soon</p>
                </div>
              </div>
            </div>

            <div className="bg-card border rounded-lg p-6">
              <h3 className="text-lg font-semibold mb-4">Top Gainers</h3>
              <div className="space-y-3">
                {[
                  { name: "Solana", symbol: "SOL", change: "+12.5%", price: "$98.45" },
                  { name: "Cardano", symbol: "ADA", change: "+8.2%", price: "$0.52" },
                  { name: "Polygon", symbol: "MATIC", change: "+6.7%", price: "$0.89" },
                  { name: "Chainlink", symbol: "LINK", change: "+5.3%", price: "$14.23" }
                ].map((coin, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-muted/20 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                        <span className="text-xs font-medium">{coin.symbol[0]}</span>
                      </div>
                      <div>
                        <p className="font-medium">{coin.name}</p>
                        <p className="text-xs text-muted-foreground">{coin.symbol}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">{coin.price}</p>
                      <p className="text-xs text-green-600">{coin.change}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Additional Features */}
          <div className="bg-card border rounded-lg p-6">
            <h3 className="text-lg font-semibold mb-4">🚀 Platform Features</h3>
            <div className="grid gap-4 md:grid-cols-3">
              <div className="p-4 bg-muted/20 rounded-lg">
                <h4 className="font-medium mb-2">📊 Advanced Analytics</h4>
                <p className="text-sm text-muted-foreground">Real-time market data, technical indicators, and comprehensive analysis tools.</p>
              </div>
              <div className="p-4 bg-muted/20 rounded-lg">
                <h4 className="font-medium mb-2">🤖 AI Insights</h4>
                <p className="text-sm text-muted-foreground">Machine learning powered predictions and market sentiment analysis.</p>
              </div>
              <div className="p-4 bg-muted/20 rounded-lg">
                <h4 className="font-medium mb-2">🔒 Security First</h4>
                <p className="text-sm text-muted-foreground">Enterprise-grade security with advanced scam detection capabilities.</p>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}

try {
  const root = document.getElementById("root");
  if (!root) {
    throw new Error("Root element not found!");
  }

  console.log("Starting React app...");

  ReactDOM.createRoot(root).render(
    <StrictMode>
      <ErrorBoundary>
        <QueryClientProvider client={queryClient}>
          <ThemeProvider defaultTheme="system" storageKey="cryptovision-theme">
            <AuthProvider>
              <TooltipProvider>
                <Router>
                  <Routes>
                    <Route path="/auth" element={<Auth />} />
                    <Route path="/*" element={
                      <DashboardLayout>
                        <Routes>
                          <Route index element={<RequireAuth><ProfessionalDashboard /></RequireAuth>} />
                          <Route path="dashboard" element={<RequireAuth><ProfessionalDashboard /></RequireAuth>} />
                          <Route path="*" element={<RequireAuth><ProfessionalDashboard /></RequireAuth>} />
                        </Routes>
                      </DashboardLayout>
                    } />
                  </Routes>
                  <Toaster />
                </Router>
              </TooltipProvider>
            </AuthProvider>
          </ThemeProvider>
        </QueryClientProvider>
      </ErrorBoundary>
    </StrictMode>
  );

  console.log("React app started successfully!");
} catch (error) {
  logError(error, "Main App Initialization");
}
