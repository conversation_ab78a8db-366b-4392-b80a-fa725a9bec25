
import { StrictMode } from "react";
import ReactDOM from "react-dom/client";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import { QueryClientProvider, QueryClient } from "@tanstack/react-query";
import { ThemeProvider } from "./contexts/theme-provider";
import { AuthProvider } from "@/contexts/auth/AuthProvider";
import { Toaster } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { RequireAuth } from "@/components/RequireAuth";
import { DashboardLayout } from "@/components/DashboardLayout";
import { ErrorBoundary } from "@/components/ErrorBoundary";

// Pages
import { Index } from "./pages/Index";
import { Dashboard } from "./pages/Dashboard";
import { NotFound } from "./pages/NotFound";
import { Auth } from "./pages/Auth";
import { ProfileSettings } from "./pages/ProfileSettings";
import { DeFiOpportunities } from "./pages/DeFiOpportunities";
import { OnChainAnalytics } from "./pages/OnChainAnalytics";
import { MarketInsights } from "./pages/MarketInsights";
import { Portfolio } from "./pages/Portfolio";
import { NewsSentiment } from "./pages/NewsSentiment";
import { Education } from "./pages/Education";
import { SmartMoney } from "./pages/SmartMoney";
import { FundamentalAnalysis } from "./pages/FundamentalAnalysis";
import { Anomalies } from "./pages/Anomalies";
import { Ratings } from "./pages/Ratings";
import { Forecasting } from "./pages/Forecasting";
import { AdvancedVisualizations } from "./pages/AdvancedVisualizations";
import { AIInsights } from "./pages/AIInsights";
import { TokenScamDetector } from "./pages/TokenScamDetector";
import { CoinDiscovery } from "./pages/CoinDiscovery";

// Styles
import "./index.css";

// Create a client with optimized configuration
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      retry: 2,
      refetchOnWindowFocus: false,
    },
    mutations: {
      retry: 1,
    },
  },
});

// Debug function to log errors
function logError(error: any, context: string) {
  console.error(`[${context}] Error:`, error);
  // Also try to display error on page
  const root = document.getElementById("root");
  if (root) {
    root.innerHTML = `
      <div style="background: red; color: white; padding: 20px; font-family: monospace;">
        <h1>Error in ${context}</h1>
        <pre>${error.toString()}</pre>
        <pre>${error.stack || 'No stack trace'}</pre>
      </div>
    `;
  }
}

// Simple test component
function SimpleApp() {
  return (
    <div style={{
      padding: '20px',
      backgroundColor: '#f0f0f0',
      minHeight: '100vh',
      fontFamily: 'Arial, sans-serif'
    }}>
      <h1 style={{ color: 'green' }}>✅ React App is Working!</h1>
      <p>Current URL: {window.location.href}</p>
      <p>Demo mode: {window.location.search.includes('demo=true') ? 'YES' : 'NO'}</p>
      <p>Timestamp: {new Date().toISOString()}</p>
    </div>
  );
}

try {
  const root = document.getElementById("root");
  if (!root) {
    throw new Error("Root element not found!");
  }

  console.log("Starting React app...");

  ReactDOM.createRoot(root).render(
    <StrictMode>
      <ErrorBoundary>
        <SimpleApp />
      </ErrorBoundary>
    </StrictMode>
  );

  console.log("React app started successfully!");
} catch (error) {
  logError(error, "Main App Initialization");
}
