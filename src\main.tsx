
import { StrictMode } from "react";
import ReactDOM from "react-dom/client";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import { QueryClientProvider, QueryClient } from "@tanstack/react-query";
import { ThemeProvider } from "./contexts/theme-provider";
import { AuthProvider } from "@/contexts/auth/AuthProvider";
import { Toaster } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { RequireAuth } from "@/components/RequireAuth";
import { DashboardLayout } from "@/components/DashboardLayout";
import { ErrorBoundary } from "@/components/ErrorBoundary";

// Pages
import { Index } from "./pages/Index";
import { Auth } from "./pages/Auth";
import { MarketInsights } from "./pages/MarketInsights";
import { Portfolio } from "./pages/Portfolio";
import { DeFiOpportunities } from "./pages/DeFiOpportunities";
import { AIInsights } from "./pages/AIInsights";
import { OnChainAnalytics } from "./pages/OnChainAnalytics";
import { NewsSentiment } from "./pages/NewsSentiment";
import { Education } from "./pages/Education";
import { SmartMoney } from "./pages/SmartMoney";

// Styles
import "./index.css";

// Create a client with optimized configuration
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      retry: 2,
      refetchOnWindowFocus: false,
    },
    mutations: {
      retry: 1,
    },
  },
});

// Get root element
const root = document.getElementById("root");
if (!root) {
  throw new Error("Root element not found!");
}

// Render application
ReactDOM.createRoot(root).render(
  <StrictMode>
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <ThemeProvider defaultTheme="system" storageKey="cryptovision-theme">
          <AuthProvider>
            <TooltipProvider>
              <Router>
                <Routes>
                  <Route path="/auth" element={<Auth />} />
                  <Route path="/*" element={
                    <DashboardLayout>
                      <Routes>
                        <Route index element={<RequireAuth><Index /></RequireAuth>} />
                        <Route path="market-insights" element={<RequireAuth><MarketInsights /></RequireAuth>} />
                        <Route path="portfolio" element={<RequireAuth><Portfolio /></RequireAuth>} />
                        <Route path="defi-opportunities" element={<RequireAuth><DeFiOpportunities /></RequireAuth>} />
                        <Route path="ai-insights" element={<RequireAuth><AIInsights /></RequireAuth>} />
                        <Route path="*" element={<RequireAuth><Index /></RequireAuth>} />
                      </Routes>
                    </DashboardLayout>
                  } />
                </Routes>
                <Toaster />
              </Router>
            </TooltipProvider>
          </AuthProvider>
        </ThemeProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  </StrictMode>
);
