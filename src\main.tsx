
import { StrictMode } from "react";
import ReactDOM from "react-dom/client";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import { QueryClientProvider, QueryClient } from "@tanstack/react-query";
import { ThemeProvider } from "./contexts/theme-provider";
import { AuthProvider } from "@/contexts/auth/AuthProvider";
import { Toaster } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { RequireAuth } from "@/components/RequireAuth";
import { DashboardLayout } from "@/components/DashboardLayout";
import { ErrorBoundary } from "@/components/ErrorBoundary";

// Pages
import { Index } from "./pages/Index";
import { Dashboard } from "./pages/Dashboard";
import { NotFound } from "./pages/NotFound";
import { Auth } from "./pages/Auth";
import { ProfileSettings } from "./pages/ProfileSettings";
import { DeFiOpportunities } from "./pages/DeFiOpportunities";
import { OnChainAnalytics } from "./pages/OnChainAnalytics";
import { MarketInsights } from "./pages/MarketInsights";
import { Portfolio } from "./pages/Portfolio";
import { NewsSentiment } from "./pages/NewsSentiment";
import { Education } from "./pages/Education";
import { SmartMoney } from "./pages/SmartMoney";
import { FundamentalAnalysis } from "./pages/FundamentalAnalysis";
import { Anomalies } from "./pages/Anomalies";
import { Ratings } from "./pages/Ratings";
import { Forecasting } from "./pages/Forecasting";
import { AdvancedVisualizations } from "./pages/AdvancedVisualizations";
import { AIInsights } from "./pages/AIInsights";
import { TokenScamDetector } from "./pages/TokenScamDetector";
import { CoinDiscovery } from "./pages/CoinDiscovery";

// Styles
import "./index.css";

// Create a client with optimized configuration
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      retry: 2,
      refetchOnWindowFocus: false,
    },
    mutations: {
      retry: 1,
    },
  },
});

// Simple working dashboard component with navigation
function WorkingDashboard() {
  const isDemoMode = window.location.search.includes('demo=true');

  return (
    <div className="min-h-screen bg-background text-foreground flex">
      {/* Simple Sidebar */}
      <div className="w-64 bg-card border-r flex-shrink-0">
        <div className="p-4">
          <h2 className="text-lg font-bold mb-4">CryptoVision Pro</h2>
          <nav className="space-y-2">
            <a href="/?demo=true" className="block px-3 py-2 rounded-md bg-primary text-primary-foreground">
              📊 Dashboard
            </a>
            <a href="/market-insights?demo=true" className="block px-3 py-2 rounded-md hover:bg-muted">
              📈 Market Insights
            </a>
            <a href="/portfolio?demo=true" className="block px-3 py-2 rounded-md hover:bg-muted">
              💼 Portfolio
            </a>
            <a href="/ai-insights?demo=true" className="block px-3 py-2 rounded-md hover:bg-muted">
              🤖 AI Insights
            </a>
            <a href="/defi-opportunities?demo=true" className="block px-3 py-2 rounded-md hover:bg-muted">
              💰 DeFi Opportunities
            </a>
          </nav>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col">
        {/* Demo Mode Banner */}
        {isDemoMode && (
          <div className="bg-chart-3 text-white px-4 py-2 text-center text-sm font-medium">
            🚀 Demo Mode Active - CryptoVision Pro
          </div>
        )}

        {/* Simple Header */}
        <header className="border-b bg-card px-6 py-4">
          <h1 className="text-2xl font-bold">Dashboard</h1>
          <p className="text-muted-foreground">Advanced Cryptocurrency Analytics Platform</p>
        </header>

        {/* Main Content */}
        <main className="flex-1 p-6 overflow-y-auto">
        <div className="max-w-screen-2xl mx-auto space-y-6">
          {/* Key Metrics */}
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            <div className="bg-card border rounded-lg p-6">
              <h3 className="text-sm font-medium text-muted-foreground">Total Market Cap</h3>
              <p className="text-2xl font-bold mt-2">$2.1T</p>
              <p className="text-xs text-green-600 mt-1">+2.4%</p>
            </div>
            <div className="bg-card border rounded-lg p-6">
              <h3 className="text-sm font-medium text-muted-foreground">24h Volume</h3>
              <p className="text-2xl font-bold mt-2">$89.2B</p>
              <p className="text-xs text-green-600 mt-1">+5.1%</p>
            </div>
            <div className="bg-card border rounded-lg p-6">
              <h3 className="text-sm font-medium text-muted-foreground">BTC Dominance</h3>
              <p className="text-2xl font-bold mt-2">54.2%</p>
              <p className="text-xs text-red-600 mt-1">-0.3%</p>
            </div>
            <div className="bg-card border rounded-lg p-6">
              <h3 className="text-sm font-medium text-muted-foreground">Fear & Greed</h3>
              <p className="text-2xl font-bold mt-2">72</p>
              <p className="text-xs text-muted-foreground mt-1">Greed</p>
            </div>
          </div>

          {/* Welcome Section */}
          <div className="bg-card border rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4">🚀 Welcome to CryptoVision Pro</h2>
            <p className="text-muted-foreground mb-4">
              Your advanced cryptocurrency analytics platform is fully operational!
            </p>
            <div className="grid gap-4 md:grid-cols-2">
              <div className="p-4 bg-muted/50 rounded-lg">
                <h3 className="font-medium mb-2">✅ System Status</h3>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• React application: Working</li>
                  <li>• Theme system: Active</li>
                  <li>• Demo mode: {isDemoMode ? 'Enabled' : 'Disabled'}</li>
                  <li>• API connections: Ready</li>
                </ul>
              </div>
              <div className="p-4 bg-muted/50 rounded-lg">
                <h3 className="font-medium mb-2">🔧 Next Steps</h3>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• Navigate using sidebar</li>
                  <li>• Explore market data</li>
                  <li>• Access AI insights</li>
                  <li>• Configure portfolio</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
        </main>
      </div>
    </div>
  );
}




try {
  const root = document.getElementById("root");
  if (!root) {
    throw new Error("Root element not found!");
  }

  console.log("Starting React app...");

  ReactDOM.createRoot(root).render(
    <StrictMode>
      <ThemeProvider defaultTheme="system" storageKey="cryptovision-theme">
        <Router>
          <Routes>
            <Route path="/*" element={<WorkingDashboard />} />
          </Routes>
        </Router>
      </ThemeProvider>
    </StrictMode>
  );

  console.log("React app started successfully!");
} catch (error) {
  console.error("Main App Initialization Error:", error);
  // Display error on page
  const root = document.getElementById("root");
  if (root) {
    root.innerHTML = `
      <div style="background: red; color: white; padding: 20px; font-family: monospace;">
        <h1>Error in Main App Initialization</h1>
        <pre>${error}</pre>
      </div>
    `;
  }
}
