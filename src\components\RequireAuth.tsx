
import { ReactNode, useEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/auth/useAuth';

interface RequireAuthProps {
  children: ReactNode;
}

export function RequireAuth({ children }: RequireAuthProps) {
  const { user, isLoading } = useAuth();
  const location = useLocation();
  const [isRedirecting, setIsRedirecting] = useState(false);

  // Add debounce to prevent rapid redirects
  useEffect(() => {
    let redirectTimeout: number;

    if (!isLoading && !user && !isRedirecting) {
      setIsRedirecting(true);
      console.log("No authenticated user, redirecting to /auth");

      // Small delay to prevent rapid redirects
      redirectTimeout = window.setTimeout(() => {}, 100);
    }

    return () => {
      if (redirectTimeout) clearTimeout(redirectTimeout);
    };
  }, [user, isLoading, isRedirecting, location.pathname]);

  // Show loading indicator while checking auth state
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  // Redirect to login page if not authenticated
  if (!user) {
    // Prevent redirect loop on auth page
    if (location.pathname === '/auth') {
      return <>{children}</>;
    }

    return <Navigate to="/auth" state={{ from: location }} replace />;
  }

  // User is authenticated, render children
  return <>{children}</>;
}
