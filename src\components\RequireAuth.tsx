
import { ReactNode, useEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/auth/useAuth';

interface RequireAuthProps {
  children: ReactNode;
}

export function RequireAuth({ children }: RequireAuthProps) {
  const { user, isLoading } = useAuth();
  const location = useLocation();
  const [isRedirecting, setIsRedirecting] = useState(false);

  // Check if we're in development mode and allow demo access
  const isDevelopment = import.meta.env.DEV;
  const allowDemoMode = isDevelopment && location.search.includes('demo=true');

  // Add debounce to prevent rapid redirects
  useEffect(() => {
    let redirectTimeout: number;

    if (!isLoading && !user && !isRedirecting && !allowDemoMode) {
      setIsRedirecting(true);
      console.log("No authenticated user, redirecting to /auth");

      // Small delay to prevent rapid redirects
      redirectTimeout = window.setTimeout(() => {}, 100);
    }

    return () => {
      if (redirectTimeout) clearTimeout(redirectTimeout);
    };
  }, [user, isLoading, isRedirecting, location.pathname, allowDemoMode]);

  // Show loading indicator while checking auth state
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  // Allow demo mode in development
  if (allowDemoMode) {
    console.log("Demo mode enabled - bypassing authentication");
    return (
      <>
        <div className="bg-chart-3 text-white px-4 py-2 text-center text-sm font-medium">
          🚀 Demo Mode Active - No authentication required
        </div>
        {children}
      </>
    );
  }

  // Redirect to login page if not authenticated
  if (!user) {
    // Prevent redirect loop on auth page
    if (location.pathname === '/auth') {
      return <>{children}</>;
    }

    return <Navigate to="/auth" state={{ from: location }} replace />;
  }

  // User is authenticated, render children
  return <>{children}</>;
}
