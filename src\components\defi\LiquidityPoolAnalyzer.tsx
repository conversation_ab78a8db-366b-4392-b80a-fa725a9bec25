
import { useState, useMemo } from "react";
import { Card, CardH<PERSON>er, CardTitle, CardContent, CardDescription } from "@/components/ui/card";
import { useDefiData } from "@/hooks/useDefiData";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { ArrowUpDown, Info } from "lucide-react";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip as RechartTooltip, Legend, ResponsiveContainer } from "recharts";

export default function LiquidityPoolAnalyzer({ isLoading }: { isLoading: boolean }) {
  const { liquidityPools } = useDefiData();
  const [sortField, setSortField] = useState<string>("apy");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc");
  const [selectedPool, setSelectedPool] = useState<string | null>(null);

  // Sort pools based on current criteria
  const sortedPools = useMemo(() => {
    if (!liquidityPools || liquidityPools.length === 0) return [];

    return [...liquidityPools].sort((a, b) => {
      let valueA = a[sortField as keyof typeof a];
      let valueB = b[sortField as keyof typeof b];

      // Handle string vs number comparison
      if (typeof valueA === "string" && typeof valueB === "string") {
        return sortDirection === "asc"
          ? valueA.localeCompare(valueB)
          : valueB.localeCompare(valueA);
      } else {
        // Numeric comparison
        valueA = valueA as number;
        valueB = valueB as number;
        return sortDirection === "asc" ? valueA - valueB : valueB - valueA;
      }
    });
  }, [liquidityPools, sortField, sortDirection]);

  // Currently selected pool data
  const currentPool = useMemo(() => {
    if (!selectedPool && sortedPools.length > 0) {
      return sortedPools[0];
    }
    return sortedPools.find(p => p.id === selectedPool) || sortedPools[0];
  }, [sortedPools, selectedPool]);

  // Handle sort click
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("desc");
    }
  };

  // Format currency values
  const formatCurrency = (value: number) => {
    if (value >= 1000000000) {
      return `$${(value / 1000000000).toFixed(2)}B`;
    } else if (value >= 1000000) {
      return `$${(value / 1000000).toFixed(2)}M`;
    } else if (value >= 1000) {
      return `$${(value / 1000).toFixed(2)}K`;
    } else {
      return `$${value.toFixed(2)}`;
    }
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <Card className="lg:col-span-2">
        <CardHeader>
          <CardTitle>Liquidity Pool Analysis</CardTitle>
          <CardDescription>Compare liquidity pools across protocols and assess impermanent loss risk</CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="space-y-4">
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-64 w-full" />
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead
                      className="cursor-pointer"
                      onClick={() => handleSort("name")}
                    >
                      Pool
                      {sortField === "name" && (
                        <ArrowUpDown className="ml-1 h-3 w-3 inline" />
                      )}
                    </TableHead>
                    <TableHead
                      className="cursor-pointer"
                      onClick={() => handleSort("protocol")}
                    >
                      Protocol
                      {sortField === "protocol" && (
                        <ArrowUpDown className="ml-1 h-3 w-3 inline" />
                      )}
                    </TableHead>
                    <TableHead
                      className="cursor-pointer text-right"
                      onClick={() => handleSort("tvl")}
                    >
                      TVL
                      {sortField === "tvl" && (
                        <ArrowUpDown className="ml-1 h-3 w-3 inline" />
                      )}
                    </TableHead>
                    <TableHead
                      className="cursor-pointer text-right"
                      onClick={() => handleSort("volume24h")}
                    >
                      24h Volume
                      {sortField === "volume24h" && (
                        <ArrowUpDown className="ml-1 h-3 w-3 inline" />
                      )}
                    </TableHead>
                    <TableHead
                      className="cursor-pointer text-right"
                      onClick={() => handleSort("apy")}
                    >
                      APY
                      {sortField === "apy" && (
                        <ArrowUpDown className="ml-1 h-3 w-3 inline" />
                      )}
                    </TableHead>
                    <TableHead
                      className="cursor-pointer text-right"
                      onClick={() => handleSort("ilRisk")}
                    >
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger className="flex items-center gap-1">
                            IL Risk <Info className="h-3.5 w-3.5" />
                            {sortField === "ilRisk" && (
                              <ArrowUpDown className="ml-1 h-3 w-3 inline" />
                            )}
                          </TooltipTrigger>
                          <TooltipContent>
                            <p className="w-60">Impermanent Loss Risk: 1 (lowest) to 10 (highest)</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </TableHead>
                    <TableHead className="text-right">7d IL</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sortedPools.map((pool) => (
                    <TableRow
                      key={pool.id}
                      className={`cursor-pointer ${currentPool?.id === pool.id ? 'bg-primary/10' : ''}`}
                      onClick={() => setSelectedPool(pool.id)}
                    >
                      <TableCell className="font-medium">{pool.name}</TableCell>
                      <TableCell>{pool.protocol}</TableCell>
                      <TableCell className="text-right">{formatCurrency(pool.tvl)}</TableCell>
                      <TableCell className="text-right">{formatCurrency(pool.volume24h)}</TableCell>
                      <TableCell className="font-medium text-right text-green-600 dark:text-green-400">
                        {pool.apy.toFixed(2)}%
                      </TableCell>
                      <TableCell className="text-right">
                        <ILRiskBadge score={pool.ilRisk} />
                      </TableCell>
                      <TableCell className="text-right">
                        {(pool.impermanentLoss7d * 100).toFixed(2)}%
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      <Card className="lg:col-span-1">
        <CardHeader>
          <CardTitle>Pool Performance</CardTitle>
          {!isLoading && currentPool && (
            <CardDescription>{currentPool.protocol} - {currentPool.name}</CardDescription>
          )}
        </CardHeader>
        <CardContent>
          {isLoading || !currentPool ? (
            <div className="space-y-4">
              <Skeleton className="h-5 w-full" />
              <Skeleton className="h-40 w-full" />
              <Skeleton className="h-5 w-full" />
              <Skeleton className="h-20 w-full" />
            </div>
          ) : (
            <Tabs defaultValue="tvl">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="tvl">TVL</TabsTrigger>
                <TabsTrigger value="volume">Volume</TabsTrigger>
                <TabsTrigger value="apy">APY</TabsTrigger>
              </TabsList>

              <div className="mt-4 space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-muted-foreground">Total Value Locked</p>
                    <p className="text-xl font-bold">{formatCurrency(currentPool.tvl)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">24h Trading Volume</p>
                    <p className="text-xl font-bold">{formatCurrency(currentPool.volume24h)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Current APY</p>
                    <p className="text-xl font-bold text-green-600 dark:text-green-400">{currentPool.apy.toFixed(2)}%</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">IL Risk Score</p>
                    <p className="text-xl font-bold">
                      <ILRiskBadge score={currentPool.ilRisk} />
                    </p>
                  </div>
                </div>

                <div>
                  <p className="text-sm font-medium mb-2">Impermanent Loss</p>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="p-3 rounded-md bg-secondary">
                      <p className="text-sm text-muted-foreground">7 Day IL</p>
                      <p className="text-lg font-medium">
                        {(currentPool.impermanentLoss7d * 100).toFixed(2)}%
                      </p>
                    </div>
                    <div className="p-3 rounded-md bg-secondary">
                      <p className="text-sm text-muted-foreground">30 Day IL</p>
                      <p className="text-lg font-medium">
                        {(currentPool.impermanentLoss30d * 100).toFixed(2)}%
                      </p>
                    </div>
                  </div>
                </div>

                <TabsContent value="tvl" className="h-[200px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={currentPool.historicalData}
                      margin={{ top: 10, right: 10, left: 0, bottom: 0 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" opacity={0.2} />
                      <XAxis
                        dataKey="date"
                        tickFormatter={(tick) => new Date(tick).toLocaleDateString(undefined, { month: 'short', day: 'numeric' })}
                        tick={{ fontSize: 12 }}
                        dy={10}
                      />
                      <YAxis
                        tickFormatter={(tick) => `$${(tick / 1000000).toFixed(1)}M`}
                        tick={{ fontSize: 12 }}
                      />
                      <RechartTooltip
                        formatter={(value: number) => [`$${(value / 1000000).toFixed(2)}M`, "TVL"]}
                        labelFormatter={(label) => new Date(label).toLocaleDateString()}
                      />
                      <Line
                        type="monotone"
                        dataKey="tvl"
                        stroke="#8884d8"
                        strokeWidth={2}
                        dot={false}
                        activeDot={{ r: 4 }}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </TabsContent>

                <TabsContent value="volume" className="h-[200px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={currentPool.historicalData}
                      margin={{ top: 10, right: 10, left: 0, bottom: 0 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" opacity={0.2} />
                      <XAxis
                        dataKey="date"
                        tickFormatter={(tick) => new Date(tick).toLocaleDateString(undefined, { month: 'short', day: 'numeric' })}
                        tick={{ fontSize: 12 }}
                        dy={10}
                      />
                      <YAxis
                        tickFormatter={(tick) => `$${(tick / 1000).toFixed(0)}K`}
                        tick={{ fontSize: 12 }}
                      />
                      <RechartTooltip
                        formatter={(value: number) => [`$${(value / 1000).toFixed(1)}K`, "Volume"]}
                        labelFormatter={(label) => new Date(label).toLocaleDateString()}
                      />
                      <Line
                        type="monotone"
                        dataKey="volume"
                        stroke="#82ca9d"
                        strokeWidth={2}
                        dot={false}
                        activeDot={{ r: 4 }}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </TabsContent>

                <TabsContent value="apy" className="h-[200px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={currentPool.historicalData}
                      margin={{ top: 10, right: 10, left: 0, bottom: 0 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" opacity={0.2} />
                      <XAxis
                        dataKey="date"
                        tickFormatter={(tick) => new Date(tick).toLocaleDateString(undefined, { month: 'short', day: 'numeric' })}
                        tick={{ fontSize: 12 }}
                        dy={10}
                      />
                      <YAxis
                        tickFormatter={(tick) => `${tick.toFixed(1)}%`}
                        tick={{ fontSize: 12 }}
                      />
                      <RechartTooltip
                        formatter={(value: number) => [`${value.toFixed(2)}%`, "APY"]}
                        labelFormatter={(label) => new Date(label).toLocaleDateString()}
                      />
                      <Line
                        type="monotone"
                        dataKey="apy"
                        stroke="#ff8042"
                        strokeWidth={2}
                        dot={false}
                        activeDot={{ r: 4 }}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </TabsContent>
              </div>
            </Tabs>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

function ILRiskBadge({ score }: { score: number }) {
  let bgColor = "bg-green-100 text-green-800";
  let label = "Low";

  if (score >= 7) {
    bgColor = "bg-red-100 text-red-800";
    label = "High";
  } else if (score >= 4) {
    bgColor = "bg-yellow-100 text-yellow-800";
    label = "Med";
  }

  return (
    <Badge variant="outline" className={`${bgColor} font-medium`}>
      {score} - {label}
    </Badge>
  );
}
