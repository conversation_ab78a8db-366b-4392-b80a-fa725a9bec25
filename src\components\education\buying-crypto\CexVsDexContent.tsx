
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON>H<PERSON>er, Card<PERSON><PERSON>le, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ArrowRight, Building, Activity, Shield, Check, Clock } from "lucide-react";

interface CexVsDexContentProps {
  onContinue: () => void;
}

export default function CexVsDexContent({ onContinue }: CexVsDexContentProps) {
  return (
    <div className="space-y-6">
      <div className="grid md:grid-cols-2 gap-6">
        <div>
          <h2 className="text-2xl font-semibold mb-4">Centralized vs. Decentralized Exchanges</h2>
          <p className="mb-4">Understanding the fundamental differences between Centralized Exchanges (CEXs) and Decentralized Exchanges (DEXs) is essential for making informed decisions about where to trade.</p>
          
          <div className="rounded-md bg-muted p-4 my-6">
            <h3 className="font-medium mb-2">Key Differences at a Glance</h3>
            <ul className="space-y-2">
              <li className="flex items-start">
                <span className="font-semibold w-24">Custody:</span> 
                <span>CEXs hold your funds, DEXs connect to your wallet</span>
              </li>
              <li className="flex items-start">
                <span className="font-semibold w-24">KYC:</span> 
                <span>Required by CEXs, rarely needed for DEXs</span>
              </li>
              <li className="flex items-start">
                <span className="font-semibold w-24">Fees:</span> 
                <span>Trading fees on CEXs, trading + gas fees on DEXs</span>
              </li>
              <li className="flex items-start">
                <span className="font-semibold w-24">User Experience:</span> 
                <span>CEXs are more user-friendly, DEXs require more technical knowledge</span>
              </li>
            </ul>
          </div>
          
          <Button variant="outline" className="mt-4" onClick={onContinue}>
            Continue to Fees <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
        
        <div className="space-y-6">
          <Card>
            <CardHeader className="bg-blue-50 dark:bg-blue-950/30">
              <CardTitle className="text-xl flex items-center">
                <Building className="mr-2 h-5 w-5 text-blue-500" />
                Centralized Exchanges (CEXs)
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-6">
              <h4 className="font-medium mb-2">Advantages</h4>
              <ul className="list-disc pl-5 space-y-1 mb-4">
                <li>Higher liquidity and trading volume</li>
                <li>User-friendly interfaces suitable for beginners</li>
                <li>Fiat on/off ramps (connect to your bank account)</li>
                <li>Customer support available</li>
                <li>Often lower fees for large trade volumes</li>
              </ul>
              
              <h4 className="font-medium mb-2">Disadvantages</h4>
              <ul className="list-disc pl-5 space-y-1">
                <li>Requires identity verification (KYC)</li>
                <li>You don't control your private keys</li>
                <li>Potential for hacks or freezing of accounts</li>
                <li>Limited cryptocurrency selection</li>
              </ul>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="bg-purple-50 dark:bg-purple-950/30">
              <CardTitle className="text-xl flex items-center">
                <Activity className="mr-2 h-5 w-5 text-purple-500" />
                Decentralized Exchanges (DEXs)
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-6">
              <h4 className="font-medium mb-2">Advantages</h4>
              <ul className="list-disc pl-5 space-y-1 mb-4">
                <li>Self-custody of your assets (you control your keys)</li>
                <li>No KYC requirements in most cases</li>
                <li>Access to a wider range of tokens</li>
                <li>Privacy preservation</li>
                <li>Censorship resistance</li>
              </ul>
              
              <h4 className="font-medium mb-2">Disadvantages</h4>
              <ul className="list-disc pl-5 space-y-1">
                <li>Gas fees can be high (especially on Ethereum)</li>
                <li>Technical complexity</li>
                <li>Potential for higher slippage on low-liquidity pairs</li>
                <li>No customer support for issues</li>
                <li>Limited or no fiat on-ramps</li>
              </ul>
            </CardContent>
          </Card>
        </div>
      </div>
      
      <div className="mt-8">
        <h3 className="text-xl font-medium mb-3">Which Should You Choose?</h3>
        <div className="grid md:grid-cols-3 gap-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center">
                <Check className="mr-2 h-4 w-4 text-green-500" />
                For Beginners
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p>If you're new to crypto, start with a reputable centralized exchange like Coinbase or Kraken. They offer better user experience, educational resources, and customer support.</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center">
                <Clock className="mr-2 h-4 w-4 text-blue-500" />
                For Traders
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p>Active traders might prefer CEXs like Binance or FTX for their advanced trading features, high liquidity, and lower fees for high-volume trading.</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center">
                <Shield className="mr-2 h-4 w-4 text-purple-500" />
                For Privacy-Focused Users
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p>If privacy and self-custody are priorities, DEXs like Uniswap or dYdX offer greater control over your assets without requiring personal information.</p>
            </CardContent>
          </Card>
        </div>
      </div>
      
      <div className="rounded-lg overflow-hidden mt-6 h-64">
        <img 
          src="https://images.unsplash.com/photo-1645770380348-c39b80ae60ad?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80" 
          alt="Comparison of centralized and decentralized cryptocurrency exchange interfaces" 
          className="w-full h-full object-cover"
        />
      </div>
    </div>
  );
}
