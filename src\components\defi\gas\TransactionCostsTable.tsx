
interface TransactionCostsTableProps {
  txCostEstimate: {
    simple: string;
    token: string;
    swap: string;
    complex: string;
  } | null;
  currentNetworkId: string;
  currentGwei: number;
  calculateTxCost: (
    gasPrice: number,
    networkId: string
  ) => {
    simple: string;
    token: string;
    swap: string;
    complex: string;
  };
}

export function TransactionCostsTable({
  txCostEstimate,
  currentNetworkId,
  currentGwei,
  calculateTxCost,
}: TransactionCostsTableProps) {
  if (!txCostEstimate) return null;

  const currentCosts = calculateTxCost(currentGwei, currentNetworkId);

  return (
    <div>
      <h3 className="font-medium mb-3">Estimated Transaction Costs</h3>
      <div className="overflow-x-auto">
        <table className="w-full text-sm">
          <thead className="bg-secondary">
            <tr>
              <th className="px-4 py-2 text-left">Transaction Type</th>
              <th className="px-4 py-2 text-right">Gas Units</th>
              <th className="px-4 py-2 text-right">Cost Now</th>
              <th className="px-4 py-2 text-right">Cost at Optimal Time</th>
              <th className="px-4 py-2 text-right">Savings</th>
            </tr>
          </thead>
          <tbody>
            <tr className="border-t">
              <td className="px-4 py-3">Simple Transfer</td>
              <td className="px-4 py-3 text-right">~21,000</td>
              <td className="px-4 py-3 text-right">${currentCosts.simple}</td>
              <td className="px-4 py-3 text-right">${txCostEstimate.simple}</td>
              <td className="px-4 py-3 text-right text-crypto-positive">
                ${(
                  parseFloat(currentCosts.simple) -
                  parseFloat(txCostEstimate.simple)
                ).toFixed(2)}
              </td>
            </tr>
            <tr className="border-t">
              <td className="px-4 py-3">Token Transfer</td>
              <td className="px-4 py-3 text-right">~65,000</td>
              <td className="px-4 py-3 text-right">${currentCosts.token}</td>
              <td className="px-4 py-3 text-right">${txCostEstimate.token}</td>
              <td className="px-4 py-3 text-right text-crypto-positive">
                ${(
                  parseFloat(currentCosts.token) -
                  parseFloat(txCostEstimate.token)
                ).toFixed(2)}
              </td>
            </tr>
            <tr className="border-t">
              <td className="px-4 py-3">Token Swap</td>
              <td className="px-4 py-3 text-right">~150,000</td>
              <td className="px-4 py-3 text-right">${currentCosts.swap}</td>
              <td className="px-4 py-3 text-right">${txCostEstimate.swap}</td>
              <td className="px-4 py-3 text-right text-crypto-positive">
                ${(
                  parseFloat(currentCosts.swap) -
                  parseFloat(txCostEstimate.swap)
                ).toFixed(2)}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  );
}
