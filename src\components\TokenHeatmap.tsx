
import { useState, useEffect } from "react";

type HeatmapItem = {
  id: string;
  name: string;
  symbol: string;
  sentiment: "very-high" | "high" | "medium" | "low" | "very-low";
  whaleCount: number;
  volumeChange: number;
};

// Mock data for token heatmap
const mockHeatmapData: HeatmapItem[] = [
  { id: "1", name: "Bitcoin", symbol: "BTC", sentiment: "very-high", whaleCount: 12, volumeChange: 23.4 },
  { id: "2", name: "Ethereum", symbol: "ETH", sentiment: "high", whaleCount: 10, volumeChange: 15.2 },
  { id: "3", name: "<PERSON><PERSON>", symbol: "SOL", sentiment: "very-high", whaleCount: 8, volumeChange: 32.1 },
  { id: "4", name: "Binance Coin", symbol: "BNB", sentiment: "medium", whaleCount: 6, volumeChange: 5.7 },
  { id: "5", name: "<PERSON><PERSON>", symbol: "ADA", sentiment: "low", whaleCount: 4, volumeChange: -2.3 },
  { id: "6", name: "X<PERSON>", symbol: "XRP", sentiment: "medium", whaleCount: 5, volumeChange: 8.9 },
  { id: "7", name: "Dogecoin", symbol: "DOGE", sentiment: "very-low", whaleCount: 3, volumeChange: -12.6 },
  { id: "8", name: "Polygon", symbol: "MATIC", sentiment: "high", whaleCount: 7, volumeChange: 18.3 },
  { id: "9", name: "Avalanche", symbol: "AVAX", sentiment: "high", whaleCount: 7, volumeChange: 14.2 },
  { id: "10", name: "Chainlink", symbol: "LINK", sentiment: "medium", whaleCount: 5, volumeChange: 7.8 },
  { id: "11", name: "Cosmos", symbol: "ATOM", sentiment: "low", whaleCount: 4, volumeChange: 3.1 },
  { id: "12", name: "Arbitrum", symbol: "ARB", sentiment: "high", whaleCount: 8, volumeChange: 21.5 },
  { id: "13", name: "Uniswap", symbol: "UNI", sentiment: "medium", whaleCount: 6, volumeChange: 9.7 },
  { id: "14", name: "Polkadot", symbol: "DOT", sentiment: "low", whaleCount: 3, volumeChange: -1.8 },
  { id: "15", name: "Shiba Inu", symbol: "SHIB", sentiment: "very-low", whaleCount: 2, volumeChange: -8.9 }
];

export function TokenHeatmap() {
  const [data, setData] = useState<HeatmapItem[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simulate API loading
    const timer = setTimeout(() => {
      setData(mockHeatmapData);
      setLoading(false);
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  const getSentimentColor = (sentiment: HeatmapItem["sentiment"]) => {
    switch(sentiment) {
      case "very-high": return "bg-crypto-positive";
      case "high": return "bg-chart-2";
      case "medium": return "bg-chart-3";
      case "low": return "bg-warning";
      case "very-low": return "bg-crypto-negative";
      default: return "bg-muted";
    }
  };

  if (loading) {
    return (
      <div className="h-[300px] flex items-center justify-center">
        <p className="text-muted-foreground">Loading heatmap data...</p>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="flex text-sm text-muted-foreground mb-4">
        <div className="flex items-center mr-4">
          <span className="w-3 h-3 inline-block rounded-sm bg-crypto-positive mr-1"></span> Very High
        </div>
        <div className="flex items-center mr-4">
          <span className="w-3 h-3 inline-block rounded-sm bg-chart-2 mr-1"></span> High
        </div>
        <div className="flex items-center mr-4">
          <span className="w-3 h-3 inline-block rounded-sm bg-chart-3 mr-1"></span> Medium
        </div>
        <div className="flex items-center mr-4">
          <span className="w-3 h-3 inline-block rounded-sm bg-warning mr-1"></span> Low
        </div>
        <div className="flex items-center">
          <span className="w-3 h-3 inline-block rounded-sm bg-crypto-negative mr-1"></span> Very Low
        </div>
      </div>

      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-4">
        {data.map((item) => (
          <div
            key={item.id}
            className={`${getSentimentColor(item.sentiment)} rounded-lg p-4 text-white hover:opacity-90 transition-opacity cursor-pointer`}
          >
            <div className="font-bold">{item.symbol}</div>
            <div className="text-sm">{item.name}</div>
            <div className="mt-2 text-xs flex justify-between">
              <span>Whales: {item.whaleCount}</span>
              <span className={item.volumeChange >= 0 ? "text-emerald-100" : "text-red-100"}>
                {item.volumeChange > 0 ? "+" : ""}{item.volumeChange}%
              </span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
