
import { useNavigate } from "react-router-dom";
import { HeaderBar } from "@/components/HeaderBar";
import { FundamentalAnalysisDashboard } from "@/components/fundamental/FundamentalAnalysisDashboard";
import { But<PERSON> } from "@/components/ui/button";
import { Home } from "lucide-react";

export default function FundamentalAnalysis() {
  const navigate = useNavigate();

  return (
    <div className="flex-1 flex flex-col min-w-0">
      <HeaderBar
        title="Fundamental Analysis"
        description="Evaluate cryptocurrencies based on underlying fundamentals"
        actions={
          <Button variant="outline" size="sm" onClick={() => navigate("/")} className="flex items-center gap-1">
            <Home className="h-4 w-4" />
            Market Overview
          </Button>
        }
      />

      <main className="flex-1 overflow-y-auto p-4 md:p-6 lg:p-8">
        <div className="max-w-screen-2xl mx-auto">
          <FundamentalAnalysisDashboard />
        </div>
      </main>
    </div>
  );
}
