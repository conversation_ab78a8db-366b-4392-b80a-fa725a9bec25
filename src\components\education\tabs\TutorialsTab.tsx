
import { <PERSON> } from "react-router-dom";
import { Tutorial } from "@/hooks/useEducation";
import { Progress } from "@/components/ui/progress";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ArrowRight, BookOpen } from "lucide-react";

interface TutorialsTabProps {
  tutorials: Tutorial[];
  progress: Record<string, number>;
  loading: boolean;
}

export function TutorialsTab({ tutorials, progress, loading }: TutorialsTabProps) {
  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[1, 2, 3, 4, 5, 6].map((i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader className="pb-2">
              <div className="h-5 w-3/4 bg-muted rounded mb-1"></div>
              <div className="h-4 w-full bg-muted rounded"></div>
            </CardHeader>
            <CardContent>
              <div className="h-24 bg-muted rounded mb-2"></div>
              <div className="flex justify-between">
                <div className="h-4 w-1/4 bg-muted rounded"></div>
                <div className="h-4 w-1/4 bg-muted rounded"></div>
              </div>
            </CardContent>
            <CardFooter>
              <div className="h-9 w-full bg-muted rounded"></div>
            </CardFooter>
          </Card>
        ))}
      </div>
    );
  }

  // Define our featured tutorials that have dedicated components
  const featuredTutorials = [
    {
      id: "blockchain-fundamentals",
      title: "Blockchain Fundamentals",
      description: "Learn the core concepts of blockchain technology and how it powers cryptocurrencies.",
      category: "Technology",
      level: "Beginner",
      estimatedTime: "25 minutes",
      thumbnail: "https://images.unsplash.com/photo-1639322537228-f710d846310a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80"
    },
    {
      id: "defi-introduction",
      title: "Introduction to DeFi",
      description: "Discover the world of Decentralized Finance and its revolutionary applications.",
      category: "Finance",
      level: "Intermediate",
      estimatedTime: "35 minutes",
      thumbnail: "https://images.unsplash.com/photo-1645589801117-99a1c2670351?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80"
    },
    {
      id: "investment-strategies",
      title: "Crypto Investment Strategies",
      description: "Learn different approaches to investing in cryptocurrency markets.",
      category: "Investing",
      level: "Intermediate",
      estimatedTime: "40 minutes",
      thumbnail: "https://images.unsplash.com/photo-1625806335347-76813a130c32?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80"
    },
    {
      id: "wallet-security",
      title: "Wallet Security",
      description: "Essential practices to keep your cryptocurrency wallets and assets safe.",
      category: "Security",
      level: "Beginner",
      estimatedTime: "20 minutes",
      thumbnail: "https://images.unsplash.com/photo-1632406898163-2a8ee3527098?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80"
    },
    {
      id: "blockchain-basics",
      title: "Blockchain Basics",
      description: "Understanding the fundamental concepts of blockchain technology.",
      category: "Technology",
      level: "Beginner",
      estimatedTime: "30 minutes",
      thumbnail: "https://images.unsplash.com/photo-1639762681485-074b7f938ba0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80"
    },
    {
      id: "buying-first-crypto",
      title: "Buying Your First Crypto",
      description: "Learn about exchanges, DEXs, fees, and best practices for purchasing cryptocurrency.",
      category: "Beginner",
      level: "Beginner",
      estimatedTime: "30 minutes",
      thumbnail: "https://images.unsplash.com/photo-1518235506717-e1ed3306a89b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80"
    }
  ];

  // Combine featured tutorials with API-provided tutorials
  const allTutorials = [...featuredTutorials, ...tutorials.filter(t =>
    !featuredTutorials.some(ft => ft.id === t.id)
  )];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {allTutorials.map((tutorial) => (
        <Card key={tutorial.id} className="overflow-hidden border border-border hover:shadow-md transition-shadow">
          <div className="relative h-40 bg-muted">
            {tutorial.thumbnail ? (
              <img
                src={tutorial.thumbnail}
                alt={tutorial.title}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-primary/20 to-secondary/20">
                <BookOpen className="h-16 w-16 text-primary/40" />
              </div>
            )}
            <Badge
              className="absolute top-2 right-2"
              variant={
                tutorial.level === "Beginner" ? "default" :
                tutorial.level === "Intermediate" ? "outline" : "secondary"
              }
            >
              {tutorial.level}
            </Badge>
          </div>

          <CardHeader className="pb-2">
            <div className="flex justify-between items-center">
              <CardTitle className="text-lg">{tutorial.title}</CardTitle>
            </div>
            <CardDescription className="line-clamp-2">{tutorial.description}</CardDescription>
          </CardHeader>

          <CardContent>
            <div className="flex justify-between text-sm text-muted-foreground mb-1.5">
              <span>{tutorial.category}</span>
              <span>{tutorial.estimatedTime}</span>
            </div>

            <div className="w-full">
              <Progress value={progress[tutorial.id] || 0} className="h-2" />
              <p className="text-xs text-muted-foreground mt-1 text-right">
                {progress[tutorial.id] ? `${progress[tutorial.id]}% complete` : "Not started"}
              </p>
            </div>
          </CardContent>

          <CardFooter>
            <Link to={`/education/tutorial/${tutorial.id}`} className="w-full">
              <Button className="w-full" variant={progress[tutorial.id] ? "outline" : "default"}>
                {progress[tutorial.id] ? "Continue Learning" : "Start Learning"}
                <ArrowRight className="ml-1 h-4 w-4" />
              </Button>
            </Link>
          </CardFooter>
        </Card>
      ))}
    </div>
  );
}
