
import { coinGeckoAxios, handleApiError, cacheResponse } from "../coinGeckoClient";
import { fetchTokenomicsData } from "./tokenomicsService";
import { fetchDevelopmentActivity } from "./developerService";
import { fetchOnChainMetrics } from "./onChainService";
import { calculateFundamentalScore } from "./scoringService";

// Fetch fundamental analysis for multiple coins
export const fetchFundamentalAnalysis = async (coinIds: string[]) => {
  try {
    const results = await Promise.all(coinIds.map(async (coinId) => {
      // Get market data first
      const marketResponse = await coinGeckoAxios.get(`/coins/markets`, {
        params: {
          vs_currency: 'usd',
          ids: coinId,
          per_page: 1,
          page: 1,
          sparkline: false
        }
      });
      const marketData = marketResponse.data[0];
      
      // Get all fundamental data in parallel
      const [tokenomics, developerData, onChainMetrics] = await Promise.all([
        fetchTokenomicsData(coinId),
        fetchDevelopmentActivity(coinId),
        fetchOnChainMetrics(coinId)
      ]);
      
      // Calculate fundamental score
      const score = calculateFundamentalScore(tokenomics, developerData, onChainMetrics, marketData);
      
      return {
        id: coinId,
        name: marketData.name,
        symbol: marketData.symbol.toUpperCase(),
        price: marketData.current_price,
        image: marketData.image,
        marketCap: marketData.market_cap,
        change24h: marketData.price_change_percentage_24h || 0,
        tokenomics,
        developerData,
        onChainMetrics,
        fundamentalScore: score
      };
    }));
    
    cacheResponse('fundamental_analysis', results);
    return results;
  } catch (error) {
    return handleApiError(error, {
      key: 'fundamental_analysis',
      data: []
    });
  }
};

// Get top coins by fundamental score
export const getTopFundamentalCoins = async (limit = 30) => {
  try {
    // First get top market cap coins
    const topCoins = await coinGeckoAxios.get('/coins/markets', {
      params: {
        vs_currency: 'usd',
        order: 'market_cap_desc',
        per_page: limit,
        page: 1,
        sparkline: false
      }
    });
    
    // Get fundamental analysis for these coins
    const coinIds = topCoins.data.map((coin: any) => coin.id);
    const fundamentalData = await fetchFundamentalAnalysis(coinIds);
    
    // Sort by fundamental score
    const sortedData = fundamentalData.sort((a, b) => 
      (b.fundamentalScore?.total || 0) - (a.fundamentalScore?.total || 0)
    );
    
    cacheResponse('top_fundamental_coins', sortedData);
    return sortedData;
  } catch (error) {
    return handleApiError(error, {
      key: 'top_fundamental_coins',
      data: []
    });
  }
};
