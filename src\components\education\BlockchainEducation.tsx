
import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from '@/components/ui/separator';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { ArrowRight, CheckCircle, XCircle } from 'lucide-react';

export default function BlockchainEducation({ loading = false }: { loading?: boolean }) {
  const [selectedBlock, setSelectedBlock] = useState<number | null>(null);
  const [quizStarted, setQuizStarted] = useState(false);
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [score, setScore] = useState(0);
  const [quizCompleted, setQuizCompleted] = useState(false);

  // Quiz questions
  const quizQuestions = [
    {
      question: "What is the primary function of blockchain technology?",
      options: [
        "To increase internet speed",
        "To create a secure, decentralized ledger of transactions",
        "To mine cryptocurrency exclusively",
        "To replace traditional banking systems"
      ],
      correctAnswer: 1,
      explanation: "Blockchain technology primarily serves as a distributed ledger that records transactions across many computers so that the record cannot be altered retroactively."
    },
    {
      question: "What cryptographic technique is fundamental to blockchain security?",
      options: [
        "Symmetric encryption",
        "Hash functions",
        "Steganography",
        "Quantum encryption"
      ],
      correctAnswer: 1,
      explanation: "Hash functions are essential to blockchain security as they create fixed-size outputs (hashes) from input data of any size, enabling data integrity verification and linking blocks together."
    },
    {
      question: "What happens when a new block is added to a blockchain?",
      options: [
        "Old blocks are deleted",
        "The blockchain redistributes tokens",
        "It's cryptographically linked to the previous block",
        "The entire blockchain must be validated again"
      ],
      correctAnswer: 2,
      explanation: "When a new block is added, it contains a hash of the previous block's data, creating an unbreakable chain. This ensures that altering any block would require changing all subsequent blocks."
    },
    {
      question: "What is a '51% attack' in blockchain?",
      options: [
        "When 51% of users download a new update",
        "When a single entity controls more than half of the network's mining power",
        "When 51% of transactions fail",
        "When a blockchain splits into two chains"
      ],
      correctAnswer: 1,
      explanation: "A 51% attack occurs when a single entity controls more than half of the network's mining power, potentially allowing them to manipulate transactions and double-spend coins."
    },
    {
      question: "Which of the following is NOT a characteristic of blockchain technology?",
      options: [
        "Immutability",
        "Centralization",
        "Transparency",
        "Distributed consensus"
      ],
      correctAnswer: 1,
      explanation: "Centralization is the opposite of what blockchain represents. Blockchain technology is designed to be decentralized, with no single point of control or failure."
    }
  ];

  // Block explorer data
  const blockData = [
    {
      blockNumber: 1,
      timestamp: "2023-06-15 14:30:22",
      hash: "0x8f7d2c9b46e90d1a8a614b1c6b1b334f6f88a596",
      prevHash: "0x0000000000000000000000000000000000000000",
      nonce: 34251,
      transactions: 5,
      difficulty: "Medium",
      description: "The Genesis Block is the first block in a blockchain. It's typically hardcoded into the blockchain's software and has special properties that establish the initial state of the ledger."
    },
    {
      blockNumber: 2,
      timestamp: "2023-06-15 14:45:18",
      hash: "******************************************",
      prevHash: "0x8f7d2c9b46e90d1a8a614b1c6b1b334f6f88a596",
      nonce: 28764,
      transactions: 12,
      difficulty: "Medium",
      description: "This block contains the first actual transactions on the network. It builds upon the genesis block and begins the chain of linked blocks."
    },
    {
      blockNumber: 3,
      timestamp: "2023-06-15 15:02:37",
      hash: "******************************************",
      prevHash: "******************************************",
      nonce: 41932,
      transactions: 8,
      difficulty: "Medium-High",
      description: "This block demonstrates how each block references the previous block's hash, creating an unbreakable chain of cryptographic links."
    },
    {
      blockNumber: 4,
      timestamp: "2023-06-15 15:18:05",
      hash: "******************************************",
      prevHash: "******************************************",
      nonce: 19567,
      transactions: 23,
      difficulty: "Medium-High",
      description: "This block contains more complex transactions, showing how the blockchain can handle increasing usage and transaction diversity."
    },
    {
      blockNumber: 5,
      timestamp: "2023-06-15 15:33:41",
      hash: "******************************************",
      prevHash: "******************************************",
      nonce: 56204,
      transactions: 17,
      difficulty: "High",
      description: "This recent block represents the growing complexity of the chain. As more blocks are added, the entire blockchain becomes more secure due to the accumulated proof-of-work."
    }
  ];

  const handleBlockClick = (blockIndex: number) => {
    setSelectedBlock(blockIndex);
    toast.info(`Exploring Block ${blockIndex + 1}`);
  };

  const handleQuizStart = () => {
    setQuizStarted(true);
    setCurrentQuestion(0);
    setScore(0);
    setQuizCompleted(false);
    toast.info("Blockchain Quiz started! Test your knowledge.");
  };

  const handleAnswerSubmit = (selectedAnswer: number) => {
    const isCorrect = selectedAnswer === quizQuestions[currentQuestion].correctAnswer;

    if (isCorrect) {
      setScore(score + 1);
      toast.success("Correct answer!");
    } else {
      toast.error("Not quite right!");
    }

    // Move to next question or end quiz
    if (currentQuestion < quizQuestions.length - 1) {
      setTimeout(() => {
        setCurrentQuestion(currentQuestion + 1);
      }, 1500);
    } else {
      setTimeout(() => {
        setQuizCompleted(true);
      }, 1500);
    }
  };

  if (loading) {
    return (
      <Card className="animate-pulse">
        <CardHeader className="pb-2">
          <div className="h-5 w-3/4 bg-muted rounded mb-1"></div>
          <div className="h-4 w-full bg-muted rounded"></div>
        </CardHeader>
        <CardContent>
          <div className="h-[400px] bg-muted rounded mb-2"></div>
        </CardContent>
      </Card>
    );
  }

  // Quiz component
  if (quizStarted) {
    return (
      <Card className="max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle>Blockchain Fundamentals Quiz</CardTitle>
          <CardDescription>
            {!quizCompleted
              ? `Question ${currentQuestion + 1} of ${quizQuestions.length}`
              : "Quiz Completed"}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {!quizCompleted ? (
            <div className="space-y-4">
              <div className="text-xl font-medium">{quizQuestions[currentQuestion].question}</div>
              <div className="space-y-2">
                {quizQuestions[currentQuestion].options.map((option, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    className="w-full justify-start text-left h-auto py-3 px-4"
                    onClick={() => handleAnswerSubmit(index)}
                  >
                    {option}
                  </Button>
                ))}
              </div>
              <div className="pt-4">
                <p className="text-muted-foreground text-sm">
                  Select the best answer. Your score will be calculated at the end.
                </p>
              </div>
            </div>
          ) : (
            <div className="space-y-6 py-4">
              <div className="text-center">
                <div className="text-3xl font-bold mb-2">
                  Your Score: {score}/{quizQuestions.length}
                </div>
                <div className="text-muted-foreground">
                  {score === quizQuestions.length ?
                    "Perfect! You're a blockchain expert!" :
                    score >= quizQuestions.length / 2 ?
                    "Good job! You have a solid understanding of blockchain fundamentals." :
                    "Keep learning! Blockchain concepts take time to master."}
                </div>
              </div>

              <div className="space-y-4 mt-6">
                <h3 className="font-medium text-lg">Review:</h3>
                {quizQuestions.map((q, idx) => (
                  <div key={idx} className="p-4 border rounded-md">
                    <div className="flex gap-2">
                      <div className="mt-1">
                        {idx < currentQuestion ? (
                          score >= idx + 1 ? <CheckCircle className="text-green-500" size={18} /> : <XCircle className="text-red-500" size={18} />
                        ) : null}
                      </div>
                      <div>
                        <p className="font-medium">{q.question}</p>
                        <p className="text-sm text-muted-foreground mt-1">Correct answer: {q.options[q.correctAnswer]}</p>
                        <p className="text-sm mt-1">{q.explanation}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              <div className="flex justify-center pt-4">
                <Button onClick={() => setQuizStarted(false)} className="mr-2">
                  Return to Lesson
                </Button>
                <Button onClick={handleQuizStart} variant="outline">
                  Retry Quiz
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Blockchain Fundamentals</CardTitle>
          <CardDescription>Learn the foundational concepts of blockchain technology</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <section>
            <h3 className="text-lg font-medium mb-2">What is Blockchain?</h3>
            <p className="text-muted-foreground">
              A blockchain is a distributed, immutable ledger that records transactions across many computers.
              This technology enables the secure transfer of data or value without requiring a trusted central authority.
              First introduced as the technology behind Bitcoin in 2008, blockchain has evolved far beyond
              cryptocurrencies to applications in finance, supply chain management, healthcare, and more.
            </p>

            <div className="my-4 p-4 bg-muted rounded-md">
              <h4 className="font-medium mb-2">Key Blockchain Properties</h4>
              <ul className="list-disc pl-5 space-y-1">
                <li><span className="font-medium">Decentralized:</span> No single entity controls the network</li>
                <li><span className="font-medium">Immutable:</span> Once recorded, data cannot be altered</li>
                <li><span className="font-medium">Transparent:</span> All transactions are publicly verifiable</li>
                <li><span className="font-medium">Secure:</span> Cryptography protects the integrity of the data</li>
                <li><span className="font-medium">Distributed:</span> The ledger is replicated across multiple nodes</li>
                <li><span className="font-medium">Consensus-based:</span> Network participants agree on the state of the ledger</li>
              </ul>
            </div>
          </section>

          <Separator />

          <section>
            <h3 className="text-lg font-medium mb-2">How Blockchain Works</h3>
            <p className="text-muted-foreground mb-4">
              Blockchain operates through a series of interconnected "blocks" that contain transaction data.
              Each block is cryptographically linked to the previous one, forming a chain. This structure makes
              it extremely difficult to alter historical data without consensus from the network.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <motion.div
                className="p-4 border rounded-md"
                whileHover={{ scale: 1.02 }}
                transition={{ type: "spring", stiffness: 400, damping: 10 }}
              >
                <h4 className="font-medium mb-2">1. Transaction Creation</h4>
                <p className="text-sm text-muted-foreground">Users initiate transactions that are broadcast to the network. These transactions are cryptographically signed using the sender's private key, ensuring authenticity.</p>
              </motion.div>

              <motion.div
                className="p-4 border rounded-md"
                whileHover={{ scale: 1.02 }}
                transition={{ type: "spring", stiffness: 400, damping: 10 }}
              >
                <h4 className="font-medium mb-2">2. Validation</h4>
                <p className="text-sm text-muted-foreground">Network nodes verify the transaction's validity using consensus mechanisms. They check signatures, balances, and adherence to protocol rules before including transactions in a block.</p>
              </motion.div>

              <motion.div
                className="p-4 border rounded-md"
                whileHover={{ scale: 1.02 }}
                transition={{ type: "spring", stiffness: 400, damping: 10 }}
              >
                <h4 className="font-medium mb-2">3. Block Creation</h4>
                <p className="text-sm text-muted-foreground">Verified transactions are grouped into a block with a unique hash. The block also contains a timestamp and reference to the previous block's hash, creating the "chain" structure.</p>
              </motion.div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <motion.div
                className="p-4 border rounded-md"
                whileHover={{ scale: 1.02 }}
                transition={{ type: "spring", stiffness: 400, damping: 10 }}
              >
                <h4 className="font-medium mb-2">4. Consensus</h4>
                <p className="text-sm text-muted-foreground">The network reaches agreement on the valid state of the blockchain through consensus mechanisms like Proof of Work or Proof of Stake.</p>
              </motion.div>

              <motion.div
                className="p-4 border rounded-md"
                whileHover={{ scale: 1.02 }}
                transition={{ type: "spring", stiffness: 400, damping: 10 }}
              >
                <h4 className="font-medium mb-2">5. Block Addition</h4>
                <p className="text-sm text-muted-foreground">Once consensus is reached, the new block is added to the chain and distributed across the network. Miners or validators may receive rewards for their role in this process.</p>
              </motion.div>

              <motion.div
                className="p-4 border rounded-md"
                whileHover={{ scale: 1.02 }}
                transition={{ type: "spring", stiffness: 400, damping: 10 }}
              >
                <h4 className="font-medium mb-2">6. Immutable Record</h4>
                <p className="text-sm text-muted-foreground">The transaction is now permanently recorded and cannot be altered without changing all subsequent blocks, which would require majority control of the network's computing power.</p>
              </motion.div>
            </div>
          </section>

          <Separator />

          <section>
            <h3 className="text-lg font-medium mb-2">Consensus Mechanisms</h3>
            <p className="text-muted-foreground mb-4">
              Consensus mechanisms are the protocols that ensure all nodes in the network agree on the valid state of the blockchain.
              These mechanisms solve the "Byzantine Generals' Problem" - ensuring trustworthy coordination in a decentralized system.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-base">Proof of Work (PoW)</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-2">
                    Miners compete to solve complex mathematical puzzles. The first to solve it gets to add the new block and receive rewards.
                    Used by Bitcoin and (historically) Ethereum.
                  </p>
                  <div className="space-y-1 text-sm">
                    <p><span className="font-medium">Pros:</span> Secure, battle-tested, decentralization-friendly</p>
                    <p><span className="font-medium">Cons:</span> Energy-intensive, potential for mining centralization, slower transaction speeds</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-base">Proof of Stake (PoS)</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-2">
                    Validators are selected to create new blocks based on the amount of cryptocurrency they "stake" as collateral.
                    Used by Ethereum, Cardano, and Solana.
                  </p>
                  <div className="space-y-1 text-sm">
                    <p><span className="font-medium">Pros:</span> Energy efficient, potentially faster, economic incentives for honest behavior</p>
                    <p><span className="font-medium">Cons:</span> Potential for stake centralization, "nothing at stake" problem, more complex</p>
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-base">Delegated Proof of Stake (DPoS)</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-2">
                    Token holders vote to elect a small number of delegates who secure the network.
                    Used by EOS, Tron, and BitShares.
                  </p>
                  <div className="space-y-1 text-sm">
                    <p><span className="font-medium">Pros:</span> Very high throughput, energy efficient, quick finality</p>
                    <p><span className="font-medium">Cons:</span> More centralized, potential for collusion among delegates</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-base">Practical Byzantine Fault Tolerance (PBFT)</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-2">
                    A voting-based consensus algorithm where nodes communicate to agree on the state of the system.
                    Used by Hyperledger Fabric and adaptations in Stellar.
                  </p>
                  <div className="space-y-1 text-sm">
                    <p><span className="font-medium">Pros:</span> High transaction throughput, low resource costs, immediate finality</p>
                    <p><span className="font-medium">Cons:</span> Requires known validators, scales poorly with many nodes</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </section>

          <Separator />

          <section>
            <h3 className="text-lg font-medium mb-2">Interactive Block Explorer</h3>
            <p className="text-muted-foreground mb-4">
              Click on the blocks below to learn more about their structure and contents. This interactive
              tool demonstrates how blocks link together to form the blockchain.
            </p>

            <div className="flex flex-wrap gap-3 justify-center mb-4">
              {blockData.map((block, index) => (
                <motion.div
                  key={index}
                  className={`w-20 h-20 ${selectedBlock === index ? 'bg-primary/20 border-primary' : 'bg-primary/10 border-primary/20'} border rounded flex items-center justify-center cursor-pointer relative`}
                  whileHover={{ scale: 1.05, backgroundColor: 'rgba(var(--primary), 0.2)' }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => handleBlockClick(index)}
                >
                  <span className="font-mono">Block {block.blockNumber}</span>
                  {index > 0 && (
                    <motion.div
                      className="absolute -left-3 top-1/2 h-0.5 w-3 bg-primary/50"
                      initial={{ scaleX: 0 }}
                      animate={{ scaleX: 1 }}
                      transition={{ delay: 0.2 }}
                    />
                  )}
                </motion.div>
              ))}
            </div>

            {selectedBlock !== null && (
              <motion.div
                className="bg-muted p-4 rounded-md"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ type: "spring", stiffness: 300, damping: 30 }}
              >
                <h4 className="font-medium mb-2">Block #{blockData[selectedBlock].blockNumber} Details</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <p><span className="font-medium">Timestamp:</span> {blockData[selectedBlock].timestamp}</p>
                    <p><span className="font-medium">Hash:</span> <span className="font-mono text-xs">{blockData[selectedBlock].hash}</span></p>
                    <p><span className="font-medium">Previous Hash:</span> <span className="font-mono text-xs">{blockData[selectedBlock].prevHash}</span></p>
                  </div>
                  <div>
                    <p><span className="font-medium">Nonce:</span> {blockData[selectedBlock].nonce}</p>
                    <p><span className="font-medium">Transactions:</span> {blockData[selectedBlock].transactions}</p>
                    <p><span className="font-medium">Difficulty:</span> {blockData[selectedBlock].difficulty}</p>
                  </div>
                  <div className="col-span-2">
                    <p className="mt-2">{blockData[selectedBlock].description}</p>
                  </div>
                </div>
              </motion.div>
            )}
          </section>

          <section>
            <h3 className="text-lg font-medium mb-2">Blockchain Use Cases</h3>
            <p className="text-muted-foreground mb-4">
              While cryptocurrencies are the most well-known application of blockchain, the technology
              has far-reaching implications across numerous industries:
            </p>

            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
              <div className="p-4 border rounded-md">
                <h4 className="font-medium mb-2">Finance & Banking</h4>
                <ul className="list-disc pl-5 text-sm space-y-1 text-muted-foreground">
                  <li>Cross-border payments</li>
                  <li>Asset tokenization</li>
                  <li>Trade finance</li>
                  <li>Smart contracts for derivatives</li>
                </ul>
              </div>

              <div className="p-4 border rounded-md">
                <h4 className="font-medium mb-2">Supply Chain</h4>
                <ul className="list-disc pl-5 text-sm space-y-1 text-muted-foreground">
                  <li>Product provenance tracking</li>
                  <li>Counterfeit prevention</li>
                  <li>Automated supplier payments</li>
                  <li>Inventory management</li>
                </ul>
              </div>

              <div className="p-4 border rounded-md">
                <h4 className="font-medium mb-2">Healthcare</h4>
                <ul className="list-disc pl-5 text-sm space-y-1 text-muted-foreground">
                  <li>Medical record management</li>
                  <li>Drug traceability</li>
                  <li>Clinical trial data integrity</li>
                  <li>Insurance claim processing</li>
                </ul>
              </div>

              <div className="p-4 border rounded-md">
                <h4 className="font-medium mb-2">Government & Public Sector</h4>
                <ul className="list-disc pl-5 text-sm space-y-1 text-muted-foreground">
                  <li>Voting systems</li>
                  <li>Identity management</li>
                  <li>Land registry</li>
                  <li>Public record keeping</li>
                </ul>
              </div>

              <div className="p-4 border rounded-md">
                <h4 className="font-medium mb-2">Digital Media & Entertainment</h4>
                <ul className="list-disc pl-5 text-sm space-y-1 text-muted-foreground">
                  <li>Royalty distribution</li>
                  <li>NFTs for digital ownership</li>
                  <li>Content monetization</li>
                  <li>Anti-piracy measures</li>
                </ul>
              </div>

              <div className="p-4 border rounded-md">
                <h4 className="font-medium mb-2">Energy & Sustainability</h4>
                <ul className="list-disc pl-5 text-sm space-y-1 text-muted-foreground">
                  <li>Peer-to-peer energy trading</li>
                  <li>Carbon credit tracking</li>
                  <li>Renewable energy certificates</li>
                  <li>Efficient grid management</li>
                </ul>
              </div>
            </div>
          </section>

          <Separator />

          <section>
            <h3 className="text-lg font-medium mb-2">Challenges & Considerations</h3>
            <p className="text-muted-foreground mb-4">
              While blockchain presents numerous opportunities, it also faces several challenges:
            </p>

            <div className="space-y-3">
              <div>
                <h4 className="font-medium">Scalability</h4>
                <p className="text-sm text-muted-foreground">Traditional blockchains face throughput limitations. Solutions like layer-2 scaling, sharding, and alternative consensus mechanisms aim to address these issues.</p>
              </div>

              <div>
                <h4 className="font-medium">Energy Consumption</h4>
                <p className="text-sm text-muted-foreground">Proof of Work blockchains consume significant energy. More sustainable alternatives like Proof of Stake are gaining adoption.</p>
              </div>

              <div>
                <h4 className="font-medium">Regulatory Uncertainty</h4>
                <p className="text-sm text-muted-foreground">The regulatory landscape for blockchain and cryptocurrencies varies globally and continues to evolve, creating uncertainty for businesses and users.</p>
              </div>

              <div>
                <h4 className="font-medium">Privacy Concerns</h4>
                <p className="text-sm text-muted-foreground">Public blockchains offer transparency but can challenge privacy. Privacy-focused solutions like zero-knowledge proofs aim to balance transparency with confidentiality.</p>
              </div>

              <div>
                <h4 className="font-medium">Interoperability</h4>
                <p className="text-sm text-muted-foreground">Many blockchain networks operate in silos. Cross-chain technologies are being developed to enable communication between different blockchain systems.</p>
              </div>
            </div>
          </section>

          <div className="flex justify-center mt-6">
            <Button onClick={handleQuizStart}>
              Take Blockchain Quiz <ArrowRight className="ml-1 h-4 w-4" />
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
