
import { <PERSON><PERSON><PERSON>, Line, XAxis, <PERSON>Axis, CartesianGrid, Tooltip, ResponsiveContainer, Area, ReferenceLine } from "recharts";
import { formatDate, formatPrice } from "./formatters";

export interface EnhancedDataPoint {
  date: string;
  price: number;
  upperBound?: number;
  lowerBound?: number;
}

interface PriceChartProps {
  enhancedChartData: EnhancedDataPoint[];
  currentDate: string | null;
}

export function PriceChart({ enhancedChartData, currentDate }: PriceChartProps) {
  return (
    <div className="h-[300px]">
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={enhancedChartData}>
          <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
          <XAxis
            dataKey="date"
            tick={{ fontSize: 12 }}
            tickFormatter={formatDate}
          />
          <YAxis
            tickFormatter={formatPrice}
            width={80}
          />
          <Tooltip
            formatter={(value: number) => [formatPrice(value), '']}
            labelFormatter={(label) => `Date: ${formatDate(label)}`}
          />

          {currentDate && (
            <ReferenceLine
              x={currentDate}
              stroke="hsl(var(--muted-foreground))"
              strokeDasharray="3 3"
              label={{
                value: 'Current',
                position: 'top',
                fill: 'hsl(var(--muted-foreground))',
                fontSize: 12
              }}
            />
          )}

          <defs>
            <linearGradient id="colorUpper" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor="hsl(var(--crypto-positive))" stopOpacity={0.2}/>
              <stop offset="95%" stopColor="hsl(var(--crypto-positive))" stopOpacity={0.05}/>
            </linearGradient>
            <linearGradient id="colorLower" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor="hsl(var(--crypto-negative))" stopOpacity={0.05}/>
              <stop offset="95%" stopColor="hsl(var(--crypto-negative))" stopOpacity={0.2}/>
            </linearGradient>
          </defs>

          <Area
            type="monotone"
            dataKey="upperBound"
            stroke="none"
            fill="url(#colorUpper)"
            activeDot={false}
            isAnimationActive={false}
          />
          <Area
            type="monotone"
            dataKey="lowerBound"
            stroke="none"
            fill="url(#colorLower)"
            activeDot={false}
            isAnimationActive={false}
          />

          <Line
            type="monotone"
            dataKey="price"
            stroke="hsl(var(--chart-1))"
            strokeWidth={2}
            dot={{ r: 2 }}
            name="Price"
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
}
