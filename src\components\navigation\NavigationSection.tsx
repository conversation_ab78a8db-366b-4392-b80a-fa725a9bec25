
import { cn } from "@/lib/utils";
import { NavigationLink } from "./NavigationLink";

interface NavigationLinkType {
  title: string;
  href: string;
  icon: React.ReactNode;
  isNew?: boolean;
  isUpdated?: boolean;
}

interface NavigationSectionProps {
  title: string;
  links: NavigationLinkType[];
  pathname: string;
  collapsed: boolean;
}

export function NavigationSection({ title, links, pathname, collapsed }: NavigationSectionProps) {
  return (
    <div className="grid gap-1">
      <h3 className={cn(
        "text-xs font-medium text-muted-foreground", 
        collapsed && "sr-only"
      )}>
        {title}
      </h3>
      <div className="grid gap-1">
        {links.map((link) => (
          <NavigationLink
            key={link.href}
            href={link.href}
            icon={link.icon}
            title={link.title}
            isActive={pathname === link.href}
            isNew={link.isNew}
            isUpdated={link.isUpdated}
            collapsed={collapsed}
          />
        ))}
      </div>
    </div>
  );
}
