
import { useState, useMemo } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le, CardContent, CardDescription } from "@/components/ui/card";
import { useDefiData } from "@/hooks/useDefiData";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Fuel, SlidersHorizontal } from "lucide-react";

// Import newly created components
import CurrentGasPrice from "./gas/CurrentGasPrice";
import TransactionSpeedSelector from "./gas/TransactionSpeedSelector";
import OptimalTransactionTime from "./gas/OptimalTransactionTime";
import GasForecastChart from "./gas/GasForecastChart";
import TransactionCostsTable from "./gas/TransactionCostsTable";
import { formatTimeRemaining, formatGasPrice, calculateTxCost } from "./gas/gasUtils";

export default function GasOptimizer({ isLoading }: { isLoading: boolean }) {
  const { gasData } = useDefiData();
  const [selectedNetwork, setSelectedNetwork] = useState<string>("ethereum");
  const [speedOption, setSpeedOption] = useState<"fast" | "standard" | "slow">("standard");

  // Get current network data
  const currentNetwork = useMemo(() => {
    if (!gasData || gasData.length === 0) return null;
    const network = gasData.find(n => n.networkId === selectedNetwork);
    return network || gasData[0];
  }, [gasData, selectedNetwork]);

  // Process historical gas data for chart display
  const processedHistoricalData = useMemo(() => {
    if (!currentNetwork) return [];

    return currentNetwork.historicalData.map(item => ({
      time: new Date(item.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
      price: item.price,
      timestamp: item.timestamp
    }));
  }, [currentNetwork]);

  // Process forecast data for chart display
  const processedForecastData = useMemo(() => {
    if (!currentNetwork) return [];

    const now = new Date();
    const currentHour = now.getHours();

    return currentNetwork.forecast.map(item => {
      // Calculate proper timestamp - handle crossing midnight
      const forecastDate = new Date(now);
      if (item.hour < currentHour) {
        forecastDate.setDate(forecastDate.getDate() + 1); // Next day
      }
      forecastDate.setHours(item.hour, 0, 0, 0);

      return {
        time: forecastDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
        predicted: item.price,
        confidence: item.confidence,
        timestamp: forecastDate.getTime()
      };
    });
  }, [currentNetwork]);

  // Combine historical and forecast data for continuous chart
  const combinedChartData = useMemo(() => {
    if (!processedHistoricalData.length || !processedForecastData.length) return [];

    // Take last 12 hours of historical data
    const recentHistorical = processedHistoricalData.slice(-12);

    const combined = [
      ...recentHistorical.map(item => ({
        ...item,
        predicted: null
      })),
      // Mark first forecast point to connect line
      {
        time: processedForecastData[0].time,
        price: null,
        predicted: processedForecastData[0].predicted,
        timestamp: processedForecastData[0].timestamp
      },
      ...processedForecastData.slice(1)
    ];

    return combined;
  }, [processedHistoricalData, processedForecastData]);

  // Get transaction cost estimate based on selected speed
  const txCostEstimate = useMemo(() => {
    if (!currentNetwork) return null;

    const gasPrice = currentNetwork.recommendations[speedOption].price;
    return calculateTxCost(gasPrice, currentNetwork.networkId);
  }, [currentNetwork, speedOption]);

  // Get optimal time based on selected speed
  const optimalTime = useMemo(() => {
    if (!currentNetwork) return null;
    return currentNetwork.recommendations[speedOption].time;
  }, [currentNetwork, speedOption]);

  // Create default estimatedTimes object to handle potential missing data
  const estimatedTimes = useMemo(() => {
    if (!currentNetwork) {
      return { fast: 1, standard: 3, slow: 10 }; // Default values if no data
    }

    // Ensure we have the required properties by providing defaults if needed
    return {
      fast: currentNetwork.estimatedTimes?.fast || 1,
      standard: currentNetwork.estimatedTimes?.standard || 3,
      slow: currentNetwork.estimatedTimes?.slow || 10
    };
  }, [currentNetwork]);

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <div className="lg:col-span-1">
        <Card className="h-full">
          <CardHeader>
            <CardTitle className="flex items-center gap-1">
              <Fuel className="h-5 w-5" /> Gas Optimizer
            </CardTitle>
            <CardDescription>
              Monitor gas prices and find optimal transaction times
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {isLoading || !currentNetwork ? (
              <div className="space-y-4">
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-20 w-full" />
                <Skeleton className="h-40 w-full" />
              </div>
            ) : (
              <div className="space-y-5">
                <div>
                  <label className="block text-sm font-medium mb-1">Select Network</label>
                  <Select value={selectedNetwork} onValueChange={setSelectedNetwork}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {gasData.map(network => (
                        <SelectItem key={network.networkId} value={network.networkId}>
                          {network.networkName}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <CurrentGasPrice
                  currentGwei={currentNetwork.currentGwei}
                  baseFee={currentNetwork.baseFee}
                  priorityFee={currentNetwork.priorityFee}
                  networkId={currentNetwork.networkId}
                  formatGasPrice={formatGasPrice}
                />

                <TransactionSpeedSelector
                  speedOption={speedOption}
                  setSpeedOption={setSpeedOption}
                  estimatedTimes={estimatedTimes}
                  recommendations={currentNetwork.recommendations}
                  networkId={currentNetwork.networkId}
                  formatGasPrice={formatGasPrice}
                />

                <OptimalTransactionTime
                  optimalTime={optimalTime}
                  formatTimeRemaining={formatTimeRemaining}
                />
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      <div className="lg:col-span-2">
        <Card className="h-full">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Gas Price Forecast</CardTitle>
                <CardDescription>
                  Historical data and price predictions for {currentNetwork?.networkName || 'the network'}
                </CardDescription>
              </div>

              <Button variant="outline" size="sm" className="flex items-center gap-1">
                <SlidersHorizontal className="h-4 w-4" />
                <span className="hidden sm:inline">Advanced Options</span>
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            {isLoading || !currentNetwork ? (
              <Skeleton className="h-[350px] w-full" />
            ) : (
              <>
                <GasForecastChart
                  combinedChartData={combinedChartData}
                  recommendedPrice={currentNetwork.recommendations[speedOption].price}
                />

                <TransactionCostsTable
                  txCostEstimate={txCostEstimate}
                  currentNetworkId={currentNetwork.networkId}
                  currentGwei={currentNetwork.currentGwei}
                  calculateTxCost={calculateTxCost}
                />
              </>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
