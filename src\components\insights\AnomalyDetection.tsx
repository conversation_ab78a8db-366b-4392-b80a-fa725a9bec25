
import { Anomaly<PERSON>lert } from "@/types/aiInsights";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON><PERSON><PERSON>cle, Alert<PERSON><PERSON>gle, <PERSON><PERSON>dingU<PERSON>, <PERSON><PERSON>, <PERSON> } from "lucide-react";
import { cn } from "@/lib/utils";

interface AnomalyDetectionProps {
  alerts: AnomalyAlert[];
  isLoading: boolean;
}

export function AnomalyDetection({ alerts, isLoading }: AnomalyDetectionProps) {
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Anomaly Detection
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3].map(i => (
              <div key={i} className="flex items-center gap-4">
                <Skeleton className="h-10 w-10 rounded-full" />
                <div className="space-y-2 flex-1">
                  <Skeleton className="h-4 w-[200px]" />
                  <Skeleton className="h-3 w-full" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  // Function to get appropriate icon and color for each anomaly type
  const getAnomalyDetails = (anomaly: AnomalyAlert) => {
    switch (anomaly.type) {
      case 'price_movement':
        return {
          icon: <TrendingUp className="h-5 w-5" />,
          color: 'text-blue-500',
          bgColor: 'bg-blue-500/20'
        };
      case 'trading_volume':
        return {
          icon: <AlertCircle className="h-5 w-5" />,
          color: 'text-purple-500',
          bgColor: 'bg-purple-500/20'
        };
      case 'whale_movement':
        return {
          icon: <Dna className="h-5 w-5" />,
          color: 'text-emerald-500',
          bgColor: 'bg-emerald-500/20'
        };
      case 'network_activity':
        return {
          icon: <AlertTriangle className="h-5 w-5" />,
          color: 'text-chart-3',
          bgColor: 'bg-chart-3/20'
        };
      default:
        return {
          icon: <Bell className="h-5 w-5" />,
          color: 'text-muted-foreground',
          bgColor: 'bg-muted'
        };
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high':
        return 'bg-destructive/20 text-destructive border-destructive/50';
      case 'medium':
        return 'bg-chart-3/20 text-chart-3 border-chart-3/50';
      case 'low':
        return 'bg-chart-2/20 text-chart-2 border-chart-2/50';
      default:
        return 'bg-muted text-muted-foreground border-border';
    }
  };

  // Format relative time
  const getRelativeTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));

    if (diffMins < 60) {
      return `${diffMins} min${diffMins !== 1 ? 's' : ''} ago`;
    } else if (diffMins < 1440) { // less than a day
      const hours = Math.floor(diffMins / 60);
      return `${hours} hr${hours !== 1 ? 's' : ''} ago`;
    } else {
      const days = Math.floor(diffMins / 1440);
      return `${days} day${days !== 1 ? 's' : ''} ago`;
    }
  };

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center gap-2">
          <Bell className="h-5 w-5" />
          Anomaly Detection
        </CardTitle>
      </CardHeader>
      <CardContent>
        {alerts.length === 0 ? (
          <div className="p-8 text-center text-muted-foreground">
            No anomalies detected in the current market conditions
          </div>
        ) : (
          <div className="space-y-4">
            {alerts.map(anomaly => {
              const { icon, color, bgColor } = getAnomalyDetails(anomaly);

              return (
                <div key={anomaly.id} className="flex gap-4">
                  <div className={cn("p-2 rounded-full h-10 w-10 flex items-center justify-center", bgColor)}>
                    <span className={color}>{icon}</span>
                  </div>

                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium flex items-center">
                        {anomaly.asset}
                        <span className="text-sm ml-1.5 text-muted-foreground">({anomaly.symbol})</span>
                      </h4>
                      <Badge variant="outline" className={getSeverityColor(anomaly.severity)}>
                        {anomaly.severity}
                      </Badge>
                    </div>

                    <p className="text-sm text-muted-foreground mt-1">{anomaly.description}</p>

                    <div className="text-xs text-muted-foreground mt-2">
                      {getRelativeTime(anomaly.timestamp)}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
