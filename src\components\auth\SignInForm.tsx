import { useState, FormEvent } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Mail, Lock } from 'lucide-react';
import { useAuth } from '@/contexts/auth/useAuth';

interface SignInFormProps {
  email: string;
  setEmail: (email: string) => void;
  password: string;
  setPassword: (password: string) => void;
  isSubmitting: boolean;
  onSubmit: (e: FormEvent) => Promise<void>;
  onPasswordReset: () => Promise<void>;
}

export function SignInForm({
  email,
  setEmail,
  password,
  setPassword,
  isSubmitting,
  onSubmit,
  onPasswordReset
}: SignInFormProps) {
  const [isSubmittingLocal, setIsSubmittingLocal] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (isSubmittingLocal) return; // Prevent double submission

    setIsSubmittingLocal(true);
    try {
      await onSubmit(e);
    } finally {
      // Keep this flag set to true during auth process to prevent multiple form submissions
      // The Auth component will handle navigation after successful auth
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="email">Email</Label>
        <div className="relative">
          <Mail className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
          <Input
            id="email"
            type="email"
            placeholder="<EMAIL>"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="pl-10"
            required
            disabled={isSubmitting || isSubmittingLocal}
          />
        </div>
      </div>
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <Label htmlFor="password">Password</Label>
          <Button
            variant="link"
            size="sm"
            className="px-0 font-normal h-auto"
            type="button"
            onClick={onPasswordReset}
            disabled={isSubmitting || isSubmittingLocal}
          >
            Forgot password?
          </Button>
        </div>
        <div className="relative">
          <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
          <Input
            id="password"
            type="password"
            placeholder="••••••••"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            className="pl-10"
            required
            disabled={isSubmitting || isSubmittingLocal}
          />
        </div>
      </div>
      <Button
        type="submit"
        className="w-full"
        disabled={isSubmitting || isSubmittingLocal}
      >
        {isSubmitting || isSubmittingLocal ? 'Signing In...' : 'Sign In'}
      </Button>
    </form>
  );
}
