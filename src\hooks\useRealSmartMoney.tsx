
import { useState, useEffect } from "react";
import { fetchWhaleTransactions, fetchRealWalletDistribution, transformWhaleData } from "@/services/api/whaleAlertApi";
import { fetchTopCoins } from "@/services/api/coinMarketData";

// Types for real smart money data
type Movement = {
  id: string;
  wallet: string;
  label: string;
  action: "Buy" | "Sell" | "Transfer";
  asset: string;
  amount: number;
  valueUSD: number;
  timeAgo: string;
};

type Distribution = {
  name: string;
  percentage: number;
  change: number;
  color: string;
};

type TimeframeType = "24h" | "7d" | "30d";

export function useRealSmartMoney() {
  const [isLoading, setIsLoading] = useState(true);
  const [timeframe, setTimeframe] = useState<TimeframeType>("24h");
  const [movements, setMovements] = useState<Movement[]>([]);
  const [walletDistribution, setWalletDistribution] = useState<Distribution[]>([]);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const [whaleTransactions, distributionData, topCoins] = await Promise.all([
        fetchWhaleTransactions(500000, 50),
        fetchRealWalletDistribution(),
        fetchTopCoins(10)
      ]);
      
      // Transform whale transactions to movements format
      const transformedMovements = transformWhaleData(whaleTransactions);
      
      // Enhance with real price data from top coins
      const enhancedMovements = transformedMovements.map((movement, index) => {
        const coin = topCoins[index % topCoins.length];
        return {
          ...movement,
          asset: coin ? `${coin.name} (${coin.symbol.toUpperCase()})` : movement.asset,
          valueUSD: coin ? movement.amount * coin.current_price : movement.valueUSD
        };
      });
      
      setMovements(enhancedMovements);
      setWalletDistribution(distributionData);
    } catch (err: any) {
      console.error('Error fetching real smart money data:', err);
      setError(err.message || 'Failed to load smart money data.');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
    
    // Set up real-time updates every 30 seconds
    const interval = setInterval(fetchData, 30000);
    
    return () => clearInterval(interval);
  }, [timeframe]);

  return {
    movements,
    walletDistribution,
    isLoading,
    timeframe,
    setTimeframe,
    error,
    refresh: fetchData
  };
}
