
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { AlertTriangle, Shield, TrendingDown, Users, Lock, Code } from "lucide-react";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from "recharts";

interface ScamRiskMetricsProps {
  riskData: any;
  isLoading: boolean;
}

export default function ScamRiskMetrics({ riskData, isLoading }: ScamRiskMetricsProps) {
  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="animate-pulse">
          <div className="h-64 bg-gray-200 rounded-lg"></div>
        </div>
      </div>
    );
  }

  // Mock risk trend data
  const riskTrendData = [
    { time: "1h", risk: 65 },
    { time: "6h", risk: 68 },
    { time: "12h", risk: 70 },
    { time: "1d", risk: 72 },
    { time: "3d", risk: 75 },
    { time: "7d", risk: 78 },
    { time: "14d", risk: 80 }
  ];

  // Risk factor breakdown
  const riskFactors = [
    { name: "Liquidity Risk", value: 25, color: "hsl(var(--chart-4))" },
    { name: "Team Risk", value: 20, color: "hsl(var(--chart-3))" },
    { name: "Code Risk", value: 15, color: "hsl(var(--chart-2))" },
    { name: "Social Risk", value: 10, color: "hsl(var(--chart-1))" },
    { name: "Market Risk", value: 30, color: "hsl(var(--chart-5))" }
  ];

  const riskMetrics = [
    {
      title: "Contract Security",
      score: riskData?.auditScore || 45,
      icon: Code,
      color: "text-chart-1",
      description: "Smart contract vulnerability assessment"
    },
    {
      title: "Liquidity Health",
      score: riskData?.liquidityLocked ? 85 : 25,
      icon: Lock,
      color: "text-chart-2",
      description: "Liquidity pool security and stability"
    },
    {
      title: "Team Transparency",
      score: riskData?.communityScore || 60,
      icon: Users,
      color: "text-chart-5",
      description: "Development team verification and history"
    },
    {
      title: "Market Stability",
      score: riskData?.communityScore ? Math.max(30, Math.min(70, riskData.communityScore)) : 45,
      icon: TrendingDown,
      color: "text-chart-3",
      description: "Price volatility and market manipulation risk"
    }
  ];

  return (
    <div className="space-y-6">
      {/* Risk Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {riskMetrics.map((metric, index) => {
          const IconComponent = metric.icon;
          return (
            <Card key={index}>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <IconComponent size={16} className={metric.color} />
                  {metric.title}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-2xl font-bold">{metric.score}%</span>
                    <Badge variant={metric.score > 70 ? "default" : metric.score > 40 ? "secondary" : "destructive"}>
                      {metric.score > 70 ? "Good" : metric.score > 40 ? "Fair" : "Poor"}
                    </Badge>
                  </div>
                  <Progress value={metric.score} className="h-2" />
                  <p className="text-xs text-muted-foreground">{metric.description}</p>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Risk Trend Chart */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingDown className="h-5 w-5 text-destructive" />
            Risk Trend Analysis
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={riskTrendData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="time" />
                <YAxis domain={[0, 100]} />
                <Tooltip
                  formatter={(value) => [`${value}%`, "Risk Score"]}
                  labelFormatter={(label) => `Time: ${label}`}
                />
                <Line
                  type="monotone"
                  dataKey="risk"
                  stroke="hsl(var(--destructive))"
                  strokeWidth={2}
                  dot={{ fill: "hsl(var(--destructive))", strokeWidth: 2, r: 4 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      {/* Risk Factor Breakdown */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-chart-3" />
              Risk Factor Distribution
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={riskFactors}
                    cx="50%"
                    cy="50%"
                    innerRadius={60}
                    outerRadius={100}
                    paddingAngle={5}
                    dataKey="value"
                  >
                    {riskFactors.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => [`${value}%`, "Risk Contribution"]} />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5 text-chart-1" />
              Security Checklist
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {[
                { check: "Contract Verified", passed: riskData?.auditScore > 50, critical: true },
                { check: "Liquidity Locked", passed: riskData?.liquidityLocked, critical: true },
                { check: "Team Doxxed", passed: riskData?.communityScore > 70, critical: false },
                { check: "Audit Completed", passed: riskData?.auditScore > 80, critical: false },
                { check: "No Mint Function", passed: riskData?.auditScore ? riskData.auditScore > 60 : false, critical: true },
                { check: "No Proxy Contract", passed: riskData?.auditScore ? riskData.auditScore > 70 : false, critical: false }
              ].map((item, index) => (
                <div key={index} className="flex items-center justify-between p-3 rounded-lg bg-muted/50">
                  <div className="flex items-center gap-2">
                    <div className={`w-3 h-3 rounded-full ${item.passed ? 'bg-crypto-positive' : 'bg-destructive'}`} />
                    <span className="text-sm font-medium">{item.check}</span>
                    {item.critical && <Badge variant="outline" className="text-xs">Critical</Badge>}
                  </div>
                  <Badge variant={item.passed ? "default" : "destructive"}>
                    {item.passed ? "Pass" : "Fail"}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
