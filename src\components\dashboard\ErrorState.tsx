
import { BarChart3 } from "lucide-react";
import { HeaderBar } from "@/components/HeaderBar";

interface ErrorStateProps {
  error: string;
  onRefresh: () => void;
}

export default function ErrorState({ error, onRefresh }: ErrorStateProps) {
  return (
    <div className="flex-1 flex flex-col min-w-0 bg-background">
      <HeaderBar title="Dashboard" description="Professional cryptocurrency analytics platform" />
      <div className="flex-1 flex items-center justify-center bg-background">
        <div className="text-center space-y-4">
          <div className="h-16 w-16 rounded-full bg-destructive/10 flex items-center justify-center mx-auto">
            <BarChart3 className="h-8 w-8 text-destructive" />
          </div>
          <h2 className="text-2xl font-bold text-destructive">Market Data Unavailable</h2>
          <p className="text-muted-foreground max-w-md">{error}</p>
          <button
            onClick={onRefresh}
            className="px-6 py-3 bg-primary text-primary-foreground rounded hover:bg-primary/90 transition-colors font-semibold"
          >
            Reconnect
          </button>
        </div>
      </div>
    </div>
  );
}
