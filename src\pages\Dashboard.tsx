
import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import HeaderBar from "@/components/HeaderBar";
import TokenHeatmap from "@/components/TokenHeatmap";
import { fetchPriceHistory } from "@/services/api/priceHistory";
import { Home } from "lucide-react";

const Dashboard = () => {
  const navigate = useNavigate();
  const [bitcoinData, setBitcoinData] = useState([]);
  const [ethereumData, setEthereumData] = useState([]);
  const [marketOverviewData, setMarketOverviewData] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [btcData, ethData] = await Promise.all([
          fetchPriceHistory('bitcoin', 30),
          fetchPriceHistory('ethereum', 30)
        ]);
        
        setBitcoinData(btcData.data || []);
        setEthereumData(ethData.data || []);
        
        // Generate market overview data based on BTC and ETH
        const marketData = btcData.data.map((item, index) => {
          const ethItem = ethData.data[index] || {};
          return {
            date: item.date,
            btcPrice: item.price,
            ethPrice: ethItem.price || 0,
            totalMarketCap: item.price * 19000000 + (ethItem.price || 0) * 120000000,
            volume: Math.random() * 50000000000 + 30000000000
          };
        });
        
        setMarketOverviewData(marketData);
        setIsLoading(false);
      } catch (error) {
        console.error("Error fetching price data:", error);
        setIsLoading(false);
      }
    };
    
    fetchData();
  }, []);

  return (
    <div className="flex-1 flex flex-col min-w-0">
      <HeaderBar 
        title="Dashboard" 
        description="Cryptocurrency market overview"
        actions={
          <Button variant="outline" size="sm" onClick={() => navigate("/")} className="flex items-center gap-1">
            <Home className="h-4 w-4" />
            Market Overview
          </Button>
        }
      />
      
      <main className="flex-1 overflow-y-auto p-4 md:p-6 lg:p-8">
        <div className="max-w-screen-2xl mx-auto space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardContent className="p-6">
                <h2 className="text-xl font-medium mb-4">Market Overview</h2>
                {isLoading ? (
                  <div className="h-[300px] flex items-center justify-center">
                    <p>Loading market data...</p>
                  </div>
                ) : (
                  <div className="h-[300px]">
                    {/* Market overview chart would go here */}
                    <p>Market data loaded successfully.</p>
                  </div>
                )}
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <h2 className="text-xl font-medium mb-4">Token Heatmap</h2>
                <TokenHeatmap />
              </CardContent>
            </Card>
          </div>
          
          <Card>
            <CardContent className="p-6">
              <h2 className="text-xl font-medium mb-4">Price Charts</h2>
              <Tabs defaultValue="bitcoin">
                <TabsList className="mb-4">
                  <TabsTrigger value="bitcoin">Bitcoin</TabsTrigger>
                  <TabsTrigger value="ethereum">Ethereum</TabsTrigger>
                </TabsList>
                
                <TabsContent value="bitcoin">
                  {isLoading ? (
                    <div className="h-[300px] flex items-center justify-center">
                      <p>Loading BTC price data...</p>
                    </div>
                  ) : (
                    <div className="h-[300px]">
                      {/* Bitcoin chart would go here */}
                      <p>Bitcoin price data loaded ({bitcoinData.length} data points)</p>
                    </div>
                  )}
                </TabsContent>
                
                <TabsContent value="ethereum">
                  {isLoading ? (
                    <div className="h-[300px] flex items-center justify-center">
                      <p>Loading ETH price data...</p>
                    </div>
                  ) : (
                    <div className="h-[300px]">
                      {/* Ethereum chart would go here */}
                      <p>Ethereum price data loaded ({ethereumData.length} data points)</p>
                    </div>
                  )}
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
          
          <div className="flex gap-4">
            <Button onClick={() => navigate("/forecasting")}>
              Price Forecasting
            </Button>
            <Button variant="outline" onClick={() => navigate("/portfolio")}>
              Portfolio Analytics
            </Button>
          </div>
        </div>
      </main>
    </div>
  );
};

export default Dashboard;
