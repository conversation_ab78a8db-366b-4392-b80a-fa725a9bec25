
import { useMemo } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent, CardDescription } from "@/components/ui/card";
import { useDefiData } from "@/hooks/useDefiData";
import { Skeleton } from "@/components/ui/skeleton";
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, Tooltip, Legend, ResponsiveContainer, Cell, ScatterChart, Scatter, ZAxis } from "recharts";

export default function RiskAnalysis({ isLoading }: { isLoading: boolean }) {
  const { protocolRisks } = useDefiData();

  const sortedRisks = useMemo(() => {
    return [...(protocolRisks || [])]
      .sort((a, b) => b.riskScore - a.riskScore);
  }, [protocolRisks]);

  // Calculate risk factors for visualization
  const riskFactorData = useMemo(() => {
    if (!protocolRisks || protocolRisks.length === 0) return [];

    const factors = [
      { name: "Age", value: 0 },
      { name: "Audit Count", value: 0 },
      { name: "Security Incidents", value: 0 },
      { name: "Contract Risk", value: 0 },
    ];

    // Sum up risk factors across protocols
    protocolRisks.forEach(risk => {
      factors[0].value += risk.age > 2 ? 0 : (2 - risk.age);
      factors[1].value += risk.auditCount < 3 ? (3 - risk.auditCount) : 0;
      factors[2].value += risk.securityIncidents * 2;
      factors[3].value += risk.contractRisk;
    });

    // Normalize values
    const max = Math.max(...factors.map(f => f.value));
    return factors.map(f => ({
      ...f,
      value: max > 0 ? (f.value / protocolRisks.length) * 10 : 0
    }));
  }, [protocolRisks]);

  // Create bubble chart data
  const bubbleData = useMemo(() => {
    return (protocolRisks || []).map(risk => ({
      name: risk.name,
      riskScore: risk.riskScore,
      tvl: risk.tvl / 1000000, // Convert to millions for better visualization
      auditCount: risk.auditCount,
      incidents: risk.securityIncidents,
    }));
  }, [protocolRisks]);

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <Card className="col-span-1">
        <CardHeader>
          <CardTitle>Protocol Risk Assessment</CardTitle>
          <CardDescription>Comparative risk evaluation of major DeFi protocols</CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <Skeleton className="h-[400px] w-full" />
          ) : (
            <div className="h-[400px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={sortedRisks}
                  layout="vertical"
                  margin={{ top: 5, right: 30, left: 80, bottom: 5 }}
                >
                  <XAxis type="number" domain={[0, 10]} />
                  <YAxis type="category" dataKey="name" width={70} />
                  <Tooltip
                    formatter={(value: number) => [`${value.toFixed(1)}`, 'Risk Score']}
                    labelFormatter={(label) => `Protocol: ${label}`}
                  />
                  <Legend />
                  <Bar dataKey="riskScore" name="Risk Score" barSize={20}>
                    {(sortedRisks || []).map((entry, index) => (
                      <Cell
                        key={`cell-${index}`}
                        fill={entry.riskScore > 6 ? '#ef4444' : entry.riskScore > 3.5 ? '#f59e0b' : '#10b981'}
                      />
                    ))}
                  </Bar>
                </BarChart>
              </ResponsiveContainer>
            </div>
          )}
        </CardContent>
      </Card>

      <Card className="col-span-1">
        <CardHeader>
          <CardTitle>Risk Factor Analysis</CardTitle>
          <CardDescription>Breakdown of major risk factors across protocols</CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <Skeleton className="h-[400px] w-full" />
          ) : (
            <div className="h-[400px]">
              <ResponsiveContainer width="100%" height="100%">
                <ScatterChart
                  margin={{ top: 20, right: 20, bottom: 20, left: 20 }}
                >
                  <XAxis type="number" dataKey="auditCount" name="Audit Count" />
                  <YAxis type="number" dataKey="riskScore" name="Risk Score" domain={[0, 10]} />
                  <ZAxis type="number" dataKey="tvl" name="TVL (millions)" range={[50, 600]} />
                  <Tooltip cursor={{ strokeDasharray: '3 3' }} formatter={(value: number, name: string) => [
                    name === 'Risk Score' ? value.toFixed(1) :
                    name === 'Audit Count' ? value :
                    `$${(value).toFixed(2)}M`,
                    name
                  ]} />
                  <Legend />
                  <Scatter name="Protocols" data={bubbleData} fill="#8884d8">
                    {(bubbleData || []).map((entry, index) => (
                      <Cell
                        key={`cell-${index}`}
                        fill={entry.riskScore > 6 ? '#ef4444' : entry.riskScore > 3.5 ? '#f59e0b' : '#10b981'}
                      />
                    ))}
                  </Scatter>
                </ScatterChart>
              </ResponsiveContainer>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
