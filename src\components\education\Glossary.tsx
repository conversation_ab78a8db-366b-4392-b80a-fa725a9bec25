
import { useState, useMemo } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Search } from "lucide-react";
import { GlossaryTerm } from "@/hooks/useEducation";

interface GlossaryProps {
  terms: GlossaryTerm[];
  loading: boolean;
}

export default function Glossary({ terms, loading }: GlossaryProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [activeCategory, setActiveCategory] = useState<string>("all");
  
  // Get unique categories
  const categories = useMemo(() => {
    if (!terms) return [];
    const categoriesSet = new Set<string>(terms.map(term => term.category));
    return ["all", ...Array.from(categoriesSet)];
  }, [terms]);
  
  // Filter terms based on search and category
  const filteredTerms = useMemo(() => {
    if (!terms) return [];
    
    return terms.filter(term => {
      // Filter by search query
      const matchesSearch = searchQuery === "" || 
        term.term.toLowerCase().includes(searchQuery.toLowerCase()) ||
        term.definition.toLowerCase().includes(searchQuery.toLowerCase());
      
      // Filter by category
      const matchesCategory = activeCategory === "all" || term.category === activeCategory;
      
      return matchesSearch && matchesCategory;
    });
  }, [terms, searchQuery, activeCategory]);
  
  // Group terms alphabetically
  const groupedTerms = useMemo(() => {
    const groups: { [key: string]: GlossaryTerm[] } = {};
    
    filteredTerms.forEach(term => {
      const firstLetter = term.term.charAt(0).toUpperCase();
      if (!groups[firstLetter]) {
        groups[firstLetter] = [];
      }
      groups[firstLetter].push(term);
    });
    
    // Sort groups alphabetically
    return Object.entries(groups).sort((a, b) => a[0].localeCompare(b[0]));
  }, [filteredTerms]);
  
  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="animate-pulse bg-muted h-10 rounded-md flex-1"></div>
          <div className="animate-pulse bg-muted h-10 w-64 rounded-md"></div>
        </div>
        
        {[1, 2, 3].map((i) => (
          <div key={i} className="animate-pulse space-y-2">
            <div className="h-6 w-12 bg-muted rounded-md"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {[1, 2, 3, 4].map((j) => (
                <div key={j} className="bg-muted h-40 rounded-md"></div>
              ))}
            </div>
          </div>
        ))}
      </div>
    );
  }
  
  return (
    <div className="space-y-6">
      {/* Search and filter */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="text"
            placeholder="Search glossary terms..."
            className="pl-9"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        
        <Tabs value={activeCategory} onValueChange={setActiveCategory} className="w-full sm:w-auto">
          <TabsList className="w-full sm:w-auto">
            {categories.slice(0, 5).map((category) => (
              <TabsTrigger key={category} value={category} className="capitalize">
                {category}
              </TabsTrigger>
            ))}
          </TabsList>
        </Tabs>
      </div>
      
      {/* Display results count */}
      {searchQuery && (
        <div className="text-sm text-muted-foreground">
          Found {filteredTerms.length} {filteredTerms.length === 1 ? 'term' : 'terms'} matching "{searchQuery}"
        </div>
      )}
      
      {/* No results message */}
      {filteredTerms.length === 0 && (
        <div className="text-center py-12">
          <h3 className="text-lg font-medium">No terms found</h3>
          <p className="text-muted-foreground mt-1">Try adjusting your search or filter</p>
        </div>
      )}
      
      {/* Glossary terms by alphabetical sections */}
      <ScrollArea className="h-[calc(100vh-300px)]">
        <div className="space-y-8 pr-4">
          {groupedTerms.map(([letter, letterTerms]) => (
            <div key={letter} className="space-y-3">
              <h2 className="text-xl font-bold">{letter}</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {letterTerms.map((term) => (
                  <Card key={term.id} className="overflow-hidden">
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-center">
                        <CardTitle>{term.term}</CardTitle>
                        <Badge variant="outline">{term.category}</Badge>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <p>{term.definition}</p>
                      
                      {term.relatedTerms.length > 0 && (
                        <div>
                          <p className="text-xs font-medium text-muted-foreground mb-1">Related Terms:</p>
                          <div className="flex flex-wrap gap-1">
                            {term.relatedTerms.map((relatedTerm, i) => (
                              <Badge key={i} variant="secondary" className="cursor-pointer" 
                                onClick={() => setSearchQuery(relatedTerm)}>
                                {relatedTerm}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          ))}
        </div>
      </ScrollArea>
    </div>
  );
}
