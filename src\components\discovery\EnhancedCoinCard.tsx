import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import {
  TrendingUp,
  TrendingDown,
  Activity,
  Shield,
  Target,
  BarChart3,
  Eye,
  Star,
  AlertTriangle
} from "lucide-react";
import { EnhancedCoinData } from "@/services/api/discovery/enhancedDiscoveryService";

interface EnhancedCoinCardProps {
  coin: EnhancedCoinData;
  onAnalyze?: (coinId: string) => void;
  onWatchlist?: (coinId: string) => void;
}

export default function EnhancedCoinCard({ coin, onAnalyze, onWatchlist }: EnhancedCoinCardProps) {
  const [activeView, setActiveView] = useState("overview");

  const isPositive = (coin.price_change_percentage_24h || 0) >= 0;

  const getSentimentColor = (sentiment: string) => {
    switch (sentiment) {
      case 'bullish': return 'text-crypto-positive bg-crypto-positive/10';
      case 'bearish': return 'text-destructive bg-destructive/10';
      default: return 'text-muted-foreground bg-muted';
    }
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'low': return 'text-crypto-positive';
      case 'medium': return 'text-chart-3';
      case 'high': return 'text-destructive';
      default: return 'text-muted-foreground';
    }
  };

  const formatLargeNumber = (num: number | undefined | null) => {
    if (num === undefined || num === null || isNaN(num)) {
      return 'N/A';
    }

    const numValue = Number(num);
    if (numValue >= 1e9) return `$${(numValue / 1e9).toFixed(1)}B`;
    if (numValue >= 1e6) return `$${(numValue / 1e6).toFixed(1)}M`;
    if (numValue >= 1e3) return `$${(numValue / 1e3).toFixed(1)}K`;
    return `$${numValue.toFixed(2)}`;
  };

  const safeToFixed = (value: number | undefined | null, decimals: number = 1) => {
    if (value === undefined || value === null || isNaN(value)) return 'N/A';
    return Number(value).toFixed(decimals);
  };

  const safeValue = (value: any, fallback: any = 'N/A') => {
    return value !== undefined && value !== null ? value : fallback;
  };

  return (
    <Card className="hover:shadow-lg transition-all duration-300 border-l-4 border-l-primary/20">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2">
            {coin.image && (
              <img src={coin.image} alt={coin.name} className="w-8 h-8 rounded-full" onError={(e) => {
                e.currentTarget.src = '/placeholder.svg';
              }} />
            )}
            <div>
              <span className="block">{safeValue(coin.name, 'Unknown')}</span>
              <span className="text-sm font-normal text-muted-foreground">
                {safeValue(coin.symbol, 'N/A').toString().toUpperCase()}
              </span>
            </div>
          </CardTitle>
          <div className="text-right">
            <div className="flex flex-col items-end gap-1">
              <Badge variant="outline" className="text-xs">
                #{safeValue(coin.market_cap_rank, 'N/A')}
              </Badge>
              {coin.trending_reason && (
                <Badge variant="secondary" className="text-xs">
                  {coin.trending_reason}
                </Badge>
              )}
            </div>
            <div className="flex items-center gap-1 mt-1">
              {isPositive ? (
                <TrendingUp className="h-4 w-4 text-green-500" />
              ) : (
                <TrendingDown className="h-4 w-4 text-red-500" />
              )}
              <span className={`text-sm font-medium ${isPositive ? 'text-green-600' : 'text-red-600'}`}>
                {isPositive ? '+' : ''}{safeToFixed(coin.price_change_percentage_24h)}%
              </span>
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        <Tabs value={activeView} onValueChange={setActiveView} className="w-full">
          <TabsList className="grid w-full grid-cols-3 h-8">
            <TabsTrigger value="overview" className="text-xs">Overview</TabsTrigger>
            <TabsTrigger value="metrics" className="text-xs">Metrics</TabsTrigger>
            <TabsTrigger value="analysis" className="text-xs">Analysis</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-3 mt-3">
            <div className="grid grid-cols-2 gap-3 text-sm">
              <div>
                <span className="text-muted-foreground">Price</span>
                <p className="font-medium">{formatLargeNumber(coin.current_price)}</p>
              </div>
              <div>
                <span className="text-muted-foreground">Market Cap</span>
                <p className="font-medium">{formatLargeNumber(coin.market_cap)}</p>
              </div>
              <div>
                <span className="text-muted-foreground">Volume (24h)</span>
                <p className="font-medium">{formatLargeNumber(coin.total_volume)}</p>
              </div>
              <div>
                <span className="text-muted-foreground">Volatility</span>
                <Badge variant="outline" className={`text-xs ${getRiskColor(coin.volatility_rating || 'unknown')}`}>
                  {(coin.volatility_rating || 'UNKNOWN').toUpperCase()}
                </Badge>
              </div>
            </div>

            <div className="flex flex-wrap gap-1">
              <Badge className={getSentimentColor(coin.market_sentiment || 'neutral')} variant="outline">
                {(coin.market_sentiment || 'NEUTRAL').toUpperCase()}
              </Badge>
              <Badge variant="outline" className={getRiskColor(coin.risk_level || 'unknown')}>
                {(coin.risk_level || 'UNKNOWN').toUpperCase()} RISK
              </Badge>
              {coin.search_rank && (
                <Badge variant="outline" className="text-xs">
                  Search Rank #{coin.search_rank}
                </Badge>
              )}
            </div>
          </TabsContent>

          <TabsContent value="metrics" className="space-y-3 mt-3">
            <div className="space-y-3">
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span>Momentum Score</span>
                  <span className="font-medium">{safeToFixed(coin.momentum_score, 0)}/100</span>
                </div>
                <Progress value={coin.momentum_score || 0} className="h-2" />
              </div>

              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span>Opportunity Score</span>
                  <span className="font-medium">{safeToFixed(coin.opportunity_score, 0)}/100</span>
                </div>
                <Progress value={coin.opportunity_score || 0} className="h-2" />
              </div>

              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span>Trading Intensity</span>
                  <span className="font-medium">{safeToFixed(coin.trading_intensity, 2)}%</span>
                </div>
                <Progress value={Math.min(100, (coin.trading_intensity || 0) * 10)} className="h-2" />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="analysis" className="space-y-3 mt-3">
            <div className="grid grid-cols-3 gap-2 text-center">
              <div className="p-2 bg-muted/30 rounded">
                <Target className="h-4 w-4 mx-auto mb-1 text-blue-500" />
                <span className="text-xs block">Target</span>
                <span className="text-xs font-medium">
                  {formatLargeNumber((coin.current_price || 0) * 1.2)}
                </span>
              </div>
              <div className="p-2 bg-muted/30 rounded">
                <Shield className="h-4 w-4 mx-auto mb-1 text-green-500" />
                <span className="text-xs block">Support</span>
                <span className="text-xs font-medium">
                  {formatLargeNumber((coin.current_price || 0) * 0.85)}
                </span>
              </div>
              <div className="p-2 bg-muted/30 rounded">
                <AlertTriangle className="h-4 w-4 mx-auto mb-1 text-red-500" />
                <span className="text-xs block">Stop</span>
                <span className="text-xs font-medium">
                  {formatLargeNumber((coin.current_price || 0) * 0.8)}
                </span>
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <div className="flex gap-2 pt-2">
          <Button
            variant="outline"
            size="sm"
            className="flex-1"
            onClick={() => onAnalyze?.(coin.id)}
          >
            <BarChart3 className="h-4 w-4 mr-1" />
            Analyze
          </Button>
          <Button
            size="sm"
            className="flex-1"
            onClick={() => onWatchlist?.(coin.id)}
          >
            <Star className="h-4 w-4 mr-1" />
            Watch
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
