
import { FeeCalculator } from "./FeeCalculator";
import { NextStepsSection } from "@/components/education/buying-crypto/NextStepsSection";

interface CalculatorContentProps {
  onReset: () => void;
}

export function CalculatorContent({ onReset }: CalculatorContentProps) {
  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-semibold mb-4">Cryptocurrency Exchange Fee Calculator</h2>
      <p className="mb-6">Compare costs across different platforms to find the most cost-effective option for your trades.</p>

      <FeeCalculator />

      <NextStepsSection onReset={onReset} />
    </div>
  );
}
