
import { useState, useEffect } from 'react';
import { toast } from '@/hooks/use-toast';

// Types for our educational content
export interface TutorialStep {
  id: string;
  title: string;
  description: string;
  content: string;
  imageUrl?: string;
  videoUrl?: string;
  quizQuestion?: {
    question: string;
    options: string[];
    correctAnswer: number;
    explanation: string;
  };
}

export interface Tutorial {
  id: string;
  title: string;
  description: string;
  level: 'Beginner' | 'Intermediate' | 'Advanced';
  estimatedTime: string;
  category: string;
  thumbnail: string;
  steps: TutorialStep[];
}

export interface TokenDeepDive {
  id: string;
  name: string;
  symbol: string;
  logoUrl: string;
  category: string;
  description: string;
  foundedIn: string;
  founder: string;
  useCase: string;
  tokenomics: {
    totalSupply: string;
    circulatingSupply: string;
    distribution: { name: string; percentage: number }[];
    unlockSchedule?: { date: string; amount: string; percentage: number }[];
  };
  technology: string;
  ecosystem: string;
  partnerships: string[];
  roadmap: { quarter: string; milestones: string[] }[];
  risks: string[];
  links: { name: string; url: string }[];
}

export interface GlossaryTerm {
  id: string;
  term: string;
  definition: string;
  category: string;
  relatedTerms: string[];
}

export interface ProtocolExplainer {
  id: string;
  name: string;
  category: string;
  description: string;
  howItWorks: string;
  keyComponents: { name: string; description: string }[];
  diagram: string;
  useCases: string[];
  risks: string[];
  examples: { name: string; url: string }[];
}

export interface KnowledgeArticle {
  id: string;
  title: string;
  content: string;
  category: string;
  tags: string[];
  level: 'Beginner' | 'Intermediate' | 'Advanced';
  lastUpdated: string;
  author: string;
}

export function useEducation() {
  const [tutorials, setTutorials] = useState<Tutorial[]>([]);
  const [tokenDeepDives, setTokenDeepDives] = useState<TokenDeepDive[]>([]);
  const [glossaryTerms, setGlossaryTerms] = useState<GlossaryTerm[]>([]);
  const [protocolExplainers, setProtocolExplainers] = useState<ProtocolExplainer[]>([]);
  const [knowledgeArticles, setKnowledgeArticles] = useState<KnowledgeArticle[]>([]);
  const [loading, setLoading] = useState(true);
  const [progress, setProgress] = useState<Record<string, number>>({});
  
  // Function to track tutorial progress
  const updateProgress = (tutorialId: string, stepIndex: number) => {
    const tutorialSteps = tutorials.find(t => t.id === tutorialId)?.steps.length || 1;
    const progressPercentage = Math.min(100, Math.round((stepIndex + 1) / tutorialSteps * 100));
    
    setProgress(prev => ({
      ...prev,
      [tutorialId]: progressPercentage
    }));
    
    // Store progress in localStorage
    localStorage.setItem('tutorialProgress', JSON.stringify({
      ...progress,
      [tutorialId]: progressPercentage
    }));
  };

  // Load data function - in a real app this would fetch from API
  useEffect(() => {
    const loadEducationData = async () => {
      try {
        setLoading(true);
        
        // Load saved progress from localStorage
        const savedProgress = localStorage.getItem('tutorialProgress');
        if (savedProgress) {
          setProgress(JSON.parse(savedProgress));
        }
        
        // Simulate API calls with setTimeout
        setTimeout(() => {
          // Load mock data - in real implementation, this would come from your backend
          setTutorials(mockTutorials);
          setTokenDeepDives(mockTokenDeepDives);
          setGlossaryTerms(mockGlossaryTerms);
          setProtocolExplainers(mockProtocolExplainers);
          setKnowledgeArticles(mockKnowledgeArticles);
          setLoading(false);
        }, 800);
      } catch (error) {
        console.error('Error loading education data:', error);
        toast({
          title: "Error loading educational content",
          description: "Please try again later",
          variant: "destructive"
        });
        setLoading(false);
      }
    };
    
    loadEducationData();
  }, []);
  
  // Function to mark a tutorial as complete
  const completeTutorial = (tutorialId: string) => {
    setProgress(prev => ({
      ...prev,
      [tutorialId]: 100
    }));
    
    // Store completion in localStorage
    const updatedProgress = {
      ...progress,
      [tutorialId]: 100
    };
    localStorage.setItem('tutorialProgress', JSON.stringify(updatedProgress));
    
    toast({
      title: "Tutorial completed!",
      description: "Great job on finishing this tutorial",
    });
  };
  
  // Function to get a specific tutorial by ID
  const getTutorialById = (id: string) => {
    return tutorials.find(tutorial => tutorial.id === id);
  };
  
  // Function to get a specific token deep dive by ID
  const getTokenDeepDiveById = (id: string) => {
    return tokenDeepDives.find(token => token.id === id);
  };
  
  // Function to get a specific protocol explainer by ID
  const getProtocolExplainerById = (id: string) => {
    return protocolExplainers.find(protocol => protocol.id === id);
  };
  
  // Function to search across all educational content
  const searchEducation = (query: string) => {
    if (!query.trim()) return { tutorials: [], tokens: [], glossary: [], protocols: [], articles: [] };
    
    const searchTerm = query.toLowerCase();
    
    return {
      tutorials: tutorials.filter(tutorial => 
        tutorial.title.toLowerCase().includes(searchTerm) || 
        tutorial.description.toLowerCase().includes(searchTerm)
      ),
      tokens: tokenDeepDives.filter(token => 
        token.name.toLowerCase().includes(searchTerm) || 
        token.symbol.toLowerCase().includes(searchTerm) ||
        token.description.toLowerCase().includes(searchTerm)
      ),
      glossary: glossaryTerms.filter(term => 
        term.term.toLowerCase().includes(searchTerm) || 
        term.definition.toLowerCase().includes(searchTerm)
      ),
      protocols: protocolExplainers.filter(protocol => 
        protocol.name.toLowerCase().includes(searchTerm) || 
        protocol.description.toLowerCase().includes(searchTerm)
      ),
      articles: knowledgeArticles.filter(article => 
        article.title.toLowerCase().includes(searchTerm) || 
        article.content.toLowerCase().includes(searchTerm) ||
        article.tags.some(tag => tag.toLowerCase().includes(searchTerm))
      ),
    };
  };

  return {
    tutorials,
    tokenDeepDives,
    glossaryTerms,
    protocolExplainers,
    knowledgeArticles,
    loading,
    progress,
    updateProgress,
    completeTutorial,
    getTutorialById,
    getTokenDeepDiveById,
    getProtocolExplainerById,
    searchEducation,
  };
}

// Mock data
const mockTutorials: Tutorial[] = [
  {
    id: "blockchain-basics",
    title: "Blockchain Fundamentals",
    description: "Learn the core concepts of blockchain technology and how it powers cryptocurrencies.",
    level: "Beginner",
    estimatedTime: "25 minutes",
    category: "Technology",
    thumbnail: "https://images.unsplash.com/photo-1639322537228-f710d846310a?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTB8fGJsb2NrY2hhaW58ZW58MHx8MHx8fDA%3D",
    steps: [
      {
        id: "what-is-blockchain",
        title: "What is Blockchain?",
        description: "Understanding the fundamentals of distributed ledger technology",
        content: "A blockchain is a distributed database or ledger shared among computer network nodes. It stores information in digital format. Blockchains guarantee the fidelity and security of data records and generate trust without the need for a trusted third party. They are best known for their crucial role in cryptocurrency systems for maintaining a secure and decentralized record of transactions, but they are not limited to cryptocurrency uses.",
        imageUrl: "https://images.unsplash.com/photo-1639322537228-f710d846310a?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTB8fGJsb2NrY2hhaW58ZW58MHx8MHx8fDA%3D"
      },
      {
        id: "how-blockchain-works",
        title: "How Blockchain Works",
        description: "The mechanics behind blockchain technology",
        content: "Blockchain architecture consists of a chain of blocks, each containing batches of transactions. When a transaction is initiated, it's grouped together in a block with other transactions. This block is verified by a network of computers (or nodes) using cryptography. Once verified, the block is added to the existing blockchain in a way that is permanent and unalterable. This decentralized verification process is what makes blockchains secure and trustworthy without requiring a central authority.",
        imageUrl: "https://images.unsplash.com/photo-1621501103258-3e135c8c1caa?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTR8fGJsb2NrY2hhaW58ZW58MHx8MHx8fDA%3D"
      },
      {
        id: "consensus-mechanisms",
        title: "Consensus Mechanisms",
        description: "How blockchain networks agree on the state of the ledger",
        content: "Consensus mechanisms are protocols that ensure all nodes in the blockchain network agree on the current state of the blockchain. The two most common consensus mechanisms are:\n\n1. **Proof of Work (PoW)**: Used by Bitcoin and some other cryptocurrencies, PoW requires miners to solve complex mathematical puzzles to validate transactions and create new blocks.\n\n2. **Proof of Stake (PoS)**: A more energy-efficient alternative where validators are selected based on the number of coins they hold and are willing to \"stake\" as collateral.",
        imageUrl: "https://images.unsplash.com/photo-1639322537504-6427a16b0a28?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8M3x8YmxvY2tjaGFpbnxlbnwwfHwwfHx8MA%3D%3D"
      },
      {
        id: "blockchain-quiz",
        title: "Test Your Knowledge",
        description: "Let's check what you've learned about blockchain",
        content: "Now that you've learned about the basics of blockchain technology, let's test your understanding with a simple quiz question.",
        quizQuestion: {
          question: "What makes blockchain technology resistant to data tampering?",
          options: [
            "Government regulation",
            "Central authority oversight",
            "Decentralized validation and cryptographic linking of blocks",
            "Regular system backups"
          ],
          correctAnswer: 2,
          explanation: "Blockchain's resistance to tampering comes from its decentralized nature and the cryptographic linking of blocks. Each block contains a hash of the previous block, creating an unbreakable chain. Any attempt to alter data would require changing not just one block but all subsequent blocks across the majority of the network, making tampering practically impossible."
        }
      }
    ]
  },
  {
    id: "defi-introduction",
    title: "Introduction to DeFi",
    description: "Discover the world of Decentralized Finance and its revolutionary applications.",
    level: "Intermediate",
    estimatedTime: "35 minutes",
    category: "Finance",
    thumbnail: "https://images.unsplash.com/photo-*************-596af9009e82?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NHx8ZGVmaXxlbnwwfHwwfHx8MA%3D%3D",
    steps: [
      {
        id: "what-is-defi",
        title: "What is DeFi?",
        description: "Understanding Decentralized Finance",
        content: "Decentralized Finance, or DeFi, refers to financial services built on blockchain technology that operate without centralized intermediaries like banks or financial institutions. DeFi aims to create an open, permissionless, and transparent financial system accessible to anyone with an internet connection. These applications enable activities such as lending, borrowing, trading, earning interest, and more—all without requiring traditional financial intermediaries.",
        imageUrl: "https://images.unsplash.com/photo-*************-596af9009e82?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NHx8ZGVmaXxlbnwwfHwwfHx8MA%3D%3D"
      },
      {
        id: "defi-use-cases",
        title: "Common DeFi Use Cases",
        description: "Exploring popular DeFi applications",
        content: "Some of the most common DeFi applications include:\n\n1. **Decentralized Exchanges (DEXs)**: Platforms that allow users to trade cryptocurrencies without intermediaries.\n\n2. **Lending and Borrowing**: Protocols that allow users to lend their crypto assets to earn interest or borrow assets by providing collateral.\n\n3. **Yield Farming**: Strategies to maximize returns by leveraging various DeFi protocols and incentive structures.\n\n4. **Stablecoins**: Cryptocurrencies designed to maintain a stable value, often pegged to fiat currencies.\n\n5. **Insurance**: Decentralized coverage for smart contract risks and other DeFi-related risks.",
        imageUrl: "https://images.unsplash.com/photo-1620321023374-d1a68fbc720d?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8ZGVmaXxlbnwwfHwwfHx8MA%3D%3D"
      },
      {
        id: "defi-risks",
        title: "Understanding DeFi Risks",
        description: "Key risks in the DeFi ecosystem",
        content: "While DeFi offers exciting opportunities, it also comes with significant risks:\n\n1. **Smart Contract Risk**: Vulnerabilities in smart contract code can lead to hacks and loss of funds.\n\n2. **Liquidation Risk**: In lending platforms, if collateral value falls below required thresholds, it may be liquidated.\n\n3. **Impermanent Loss**: A risk for liquidity providers when the price of tokens changes after depositing them.\n\n4. **Regulatory Uncertainty**: The evolving regulatory landscape may impact DeFi protocols and users.\n\n5. **Oracle Failures**: DeFi relies on oracles for external data; incorrect data can cause system failures.",
        imageUrl: "https://images.unsplash.com/photo-1622630998477-20aa696ecb05?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8N3x8ZGVmaXxlbnwwfHwwfHx8MA%3D%3D"
      }
    ]
  },
  {
    id: "crypto-investing",
    title: "Crypto Investment Strategies",
    description: "Learn different approaches to investing in cryptocurrency markets.",
    level: "Intermediate",
    estimatedTime: "40 minutes",
    category: "Investing",
    thumbnail: "https://images.unsplash.com/photo-1590283603385-17ffb3a7f29f?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTZ8fGNyeXB0byUyMGludmVzdGluZ3xlbnwwfHwwfHx8MA%3D%3D",
    steps: [
      {
        id: "investment-fundamentals",
        title: "Investment Fundamentals",
        description: "Core principles of crypto investing",
        content: "Before diving into specific strategies, it's important to understand some fundamental principles of crypto investing:\n\n1. **Risk Management**: Never invest more than you can afford to lose. Cryptocurrencies are highly volatile investments.\n\n2. **Diversification**: Spread investments across different assets to reduce risk.\n\n3. **Research**: Always conduct thorough research (fundamental and technical) before investing.\n\n4. **Long-Term Thinking**: Consider both short-term volatility and long-term potential.\n\n5. **Security**: Learn how to securely store your crypto assets to prevent theft or loss.",
        imageUrl: "https://images.unsplash.com/photo-1590283603385-17ffb3a7f29f?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTZ8fGNyeXB0byUyMGludmVzdGluZ3xlbnwwfHwwfHx8MA%3D%3D"
      },
      {
        id: "investment-strategies",
        title: "Common Investment Strategies",
        description: "Different approaches to crypto investing",
        content: "Several strategies are popular among crypto investors:\n\n1. **HODLing**: Long-term buying and holding regardless of price fluctuations.\n\n2. **Dollar-Cost Averaging (DCA)**: Investing a fixed amount at regular intervals regardless of price.\n\n3. **Value Investing**: Identifying undervalued projects with strong fundamentals.\n\n4. **Swing Trading**: Capturing gains from market 'swings' over days or weeks.\n\n5. **Yield Farming**: Maximizing returns through various DeFi protocols.\n\n6. **Index Investing**: Building a diversified portfolio that mimics a sector or market.",
        imageUrl: "https://images.unsplash.com/photo-1629339942248-45d4b10d3636?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTl8fGNyeXB0byUyMGludmVzdGluZ3xlbnwwfHwwfHx8MA%3D%3D"
      },
      {
        id: "market-analysis",
        title: "Market Analysis Techniques",
        description: "Tools for evaluating crypto investments",
        content: "Two main approaches to market analysis are:\n\n1. **Fundamental Analysis**: Evaluating a cryptocurrency's intrinsic value based on:\n   - Team expertise and track record\n   - Technology and innovation\n   - Tokenomics and supply mechanics\n   - Community and adoption metrics\n   - Regulatory environment\n\n2. **Technical Analysis**: Studying price charts and trading patterns using:\n   - Support and resistance levels\n   - Moving averages\n   - Relative Strength Index (RSI)\n   - MACD (Moving Average Convergence Divergence)\n   - Volume analysis",
        imageUrl: "https://images.unsplash.com/photo-1618044733300-9472054094ee?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTR8fGNyeXB0byUyMGludmVzdGluZ3xlbnwwfHwwfHx8MA%3D%3D"
      }
    ]
  }
];

const mockTokenDeepDives: TokenDeepDive[] = [
  {
    id: "ethereum",
    name: "Ethereum",
    symbol: "ETH",
    logoUrl: "https://cryptologos.cc/logos/ethereum-eth-logo.png",
    category: "Smart Contract Platform",
    description: "Ethereum is a decentralized, open-source blockchain with smart contract functionality. Ether is the native cryptocurrency of the platform. It is the second-largest cryptocurrency by market capitalization, after Bitcoin. Ethereum is the most actively used blockchain.",
    foundedIn: "2015",
    founder: "Vitalik Buterin, Gavin Wood, Charles Hoskinson, Anthony Di Iorio, and Joseph Lubin",
    useCase: "Ethereum serves as a platform for many decentralized applications (dApps), decentralized finance (DeFi) protocols, and non-fungible tokens (NFTs). It allows developers to build and deploy smart contracts, which are self-executing contracts with the terms directly written into code.",
    tokenomics: {
      totalSupply: "No maximum cap",
      circulatingSupply: "~120 million ETH",
      distribution: [
        { name: "Initial Sale", percentage: 72 },
        { name: "Foundation", percentage: 6 },
        { name: "Early Development", percentage: 12 },
        { name: "Ecosystem Development", percentage: 10 }
      ]
    },
    technology: "Ethereum initially used a Proof of Work (PoW) consensus mechanism, similar to Bitcoin, but transitioned to Proof of Stake (PoS) through a series of upgrades culminating in 'The Merge' in September 2022. This shift significantly reduced Ethereum's energy consumption and laid the groundwork for future scalability improvements.",
    ecosystem: "Ethereum has the largest ecosystem of decentralized applications, with thousands of projects built on its blockchain. These include decentralized exchanges (DEXs), lending protocols, insurance platforms, games, and more. The Ethereum ecosystem is also supported by a large community of developers, researchers, and users.",
    partnerships: [
      "Enterprise Ethereum Alliance",
      "Microsoft",
      "Hyperledger",
      "Santander",
      "JPMorgan Chase"
    ],
    roadmap: [
      {
        quarter: "2023 Q3-Q4",
        milestones: ["Shanghai Upgrade", "Implementation of EIP-4844 (proto-danksharding)"]
      },
      {
        quarter: "2024",
        milestones: ["Full Sharding Implementation", "Improved Scalability Solutions"]
      }
    ],
    risks: [
      "Scalability challenges and high gas fees during peak usage",
      "Competition from alternative smart contract platforms",
      "Regulatory uncertainty in various jurisdictions",
      "Technical risks associated with protocol upgrades"
    ],
    links: [
      { name: "Website", url: "https://ethereum.org" },
      { name: "GitHub", url: "https://github.com/ethereum" },
      { name: "Documentation", url: "https://ethereum.org/en/developers/docs/" },
      { name: "Etherscan", url: "https://etherscan.io" }
    ]
  },
  {
    id: "bitcoin",
    name: "Bitcoin",
    symbol: "BTC",
    logoUrl: "https://cryptologos.cc/logos/bitcoin-btc-logo.png",
    category: "Digital Currency",
    description: "Bitcoin is the world's first cryptocurrency, a form of electronic cash. It is a decentralized digital currency that can be sent from user to user on the peer-to-peer bitcoin network without the need for intermediaries. Transactions are verified by network nodes through cryptography and recorded in a public distributed ledger called a blockchain.",
    foundedIn: "2009",
    founder: "Satoshi Nakamoto (pseudonym)",
    useCase: "Bitcoin was designed primarily as a peer-to-peer electronic cash system that enables online payments to be sent directly from one party to another without going through a financial institution. Over time, it has also gained prominence as a store of value, often referred to as 'digital gold.'",
    tokenomics: {
      totalSupply: "21 million BTC",
      circulatingSupply: "~19 million BTC",
      distribution: [
        { name: "Mining Rewards", percentage: 100 }
      ],
      unlockSchedule: [
        { date: "2024", amount: "~450 BTC/day", percentage: 3.125 },
        { date: "2028", amount: "~225 BTC/day", percentage: 1.5625 }
      ]
    },
    technology: "Bitcoin uses a Proof of Work (PoW) consensus mechanism, where miners compete to solve complex mathematical puzzles to validate transactions and create new blocks. This process requires significant computational resources. Bitcoin undergoes 'halving' events approximately every four years, which reduce the mining reward by half, creating a deflationary supply schedule.",
    ecosystem: "The Bitcoin ecosystem includes wallets, exchanges, payment processors, mining equipment manufacturers, and various financial services. Layer 2 solutions like the Lightning Network aim to improve Bitcoin's scalability by enabling faster and cheaper transactions off the main blockchain.",
    partnerships: [
      "PayPal",
      "Square",
      "MicroStrategy",
      "Tesla",
      "El Salvador (legal tender)"
    ],
    roadmap: [
      {
        quarter: "2023-2024",
        milestones: ["Taproot Upgrade Adoption", "Lightning Network Expansion"]
      }
    ],
    risks: [
      "Regulatory crackdowns in various jurisdictions",
      "Environmental concerns related to energy consumption",
      "Mining concentration in specific geographic regions",
      "Potential scalability limitations"
    ],
    links: [
      { name: "Website", url: "https://bitcoin.org" },
      { name: "GitHub", url: "https://github.com/bitcoin/bitcoin" },
      { name: "Bitcoin Talk", url: "https://bitcointalk.org" },
      { name: "Blockchain Explorer", url: "https://blockchain.com/explorer" }
    ]
  },
  {
    id: "cardano",
    name: "Cardano",
    symbol: "ADA",
    logoUrl: "https://cryptologos.cc/logos/cardano-ada-logo.png",
    category: "Smart Contract Platform",
    description: "Cardano is a third-generation, decentralized proof-of-stake blockchain platform designed to be a more secure and sustainable blockchain ecosystem. Cardano was created with a research-first approach, incorporating peer-reviewed academic research into its development.",
    foundedIn: "2017",
    founder: "Charles Hoskinson (co-founder of Ethereum)",
    useCase: "Cardano aims to provide a more balanced and sustainable platform for developing decentralized applications, systems, and societies. It focuses on use cases in education, retail, agriculture, government, finance, and healthcare, particularly in developing regions.",
    tokenomics: {
      totalSupply: "45 billion ADA",
      circulatingSupply: "~35 billion ADA",
      distribution: [
        { name: "Initial Sale", percentage: 57.6 },
        { name: "IOHK (Development Team)", percentage: 16 },
        { name: "Cardano Foundation", percentage: 11.7 },
        { name: "Emurgo", percentage: 14.7 }
      ]
    },
    technology: "Cardano uses a unique proof-of-stake consensus mechanism called Ouroboros. It's built using Haskell, a functional programming language that enables a high degree of fault tolerance. The platform is developed in phases (or 'eras'): Byron, Shelley, Goguen, Basho, and Voltaire, each introducing new functionality.",
    ecosystem: "The Cardano ecosystem includes decentralized applications, DeFi protocols, NFT platforms, and identity solutions. Development is supported by three main organizations: IOHK (technology and engineering), the Cardano Foundation (supervision and advancement), and Emurgo (commercial adoption).",
    partnerships: [
      "Ethiopian Ministry of Education",
      "New Balance",
      "Dish Network",
      "PwC",
      "University of Wyoming"
    ],
    roadmap: [
      {
        quarter: "2023",
        milestones: ["Basho Era Development", "Hydra Layer 2 Solution"]
      },
      {
        quarter: "2024-2025",
        milestones: ["Voltaire Era", "Governance Implementation"]
      }
    ],
    risks: [
      "Competition from established smart contract platforms",
      "Relatively slower development pace due to academic approach",
      "Adoption challenges for its unique technologies",
      "Regulatory uncertainty"
    ],
    links: [
      { name: "Website", url: "https://cardano.org" },
      { name: "Documentation", url: "https://docs.cardano.org" },
      { name: "GitHub", url: "https://github.com/input-output-hk/cardano-node" },
      { name: "Cardano Explorer", url: "https://explorer.cardano.org" }
    ]
  }
];

const mockGlossaryTerms: GlossaryTerm[] = [
  {
    id: "blockchain",
    term: "Blockchain",
    definition: "A blockchain is a distributed database or ledger that is shared among computer network nodes. As a database, a blockchain stores information electronically in digital format. Blockchains are best known for their crucial role in cryptocurrency systems for maintaining a secure and decentralized record of transactions, but they are not limited to cryptocurrency uses.",
    category: "Technology",
    relatedTerms: ["Distributed Ledger", "Block", "Consensus Mechanism", "Decentralization"]
  },
  {
    id: "smart-contract",
    term: "Smart Contract",
    definition: "A smart contract is a self-executing contract with the terms of the agreement between buyer and seller being directly written into lines of code. The code and the agreements contained therein exist on a blockchain network. Smart contracts permit trusted transactions and agreements to be carried out among disparate, anonymous parties without the need for a central authority or external enforcement mechanism.",
    category: "Technology",
    relatedTerms: ["Ethereum", "Solidity", "DApp", "Gas"]
  },
  {
    id: "defi",
    term: "DeFi (Decentralized Finance)",
    definition: "DeFi is an umbrella term for a variety of financial applications in cryptocurrency or blockchain geared toward disrupting financial intermediaries. DeFi draws inspiration from blockchain, the technology behind the digital currency bitcoin, which allows several entities to hold a copy of a history of transactions, meaning it isn't controlled by a single, central source.",
    category: "Finance",
    relatedTerms: ["Yield Farming", "Liquidity Mining", "DEX", "AMM", "Lending Protocol"]
  },
  {
    id: "nft",
    term: "NFT (Non-Fungible Token)",
    definition: "A non-fungible token (NFT) is a unique digital identifier that is recorded on a blockchain and is used to certify ownership and authenticity of a specific digital asset. Unlike cryptocurrencies, which are fungible and can be exchanged on a one-to-one basis, each NFT is unique and cannot be exchanged on a like-for-like basis.",
    category: "Digital Assets",
    relatedTerms: ["Digital Art", "Collectibles", "Metadata", "ERC-721", "ERC-1155"]
  },
  {
    id: "dao",
    term: "DAO (Decentralized Autonomous Organization)",
    definition: "A decentralized autonomous organization (DAO) is an organization represented by rules encoded as a computer program that is transparent, controlled by the organization members, and not influenced by a central government. DAOs use smart contracts to establish and enforce rules, make decisions, and manage resources collectively without centralized leadership.",
    category: "Governance",
    relatedTerms: ["Governance Token", "Voting Mechanism", "Treasury", "Proposal"]
  },
  {
    id: "consensus",
    term: "Consensus Mechanism",
    definition: "A consensus mechanism is a fault-tolerant algorithm that is used in blockchain systems to achieve the necessary agreement on a single data value or a network state among distributed processes or multi-agent systems. Common consensus mechanisms include Proof of Work (PoW), Proof of Stake (PoS), Delegated Proof of Stake (DPoS), and Practical Byzantine Fault Tolerance (PBFT).",
    category: "Technology",
    relatedTerms: ["Proof of Work", "Proof of Stake", "Validator", "Mining", "Finality"]
  },
  {
    id: "stablecoin",
    term: "Stablecoin",
    definition: "A stablecoin is a type of cryptocurrency that is designed to maintain a stable value relative to a specified asset or basket of assets. Stablecoins are often pegged to fiat currencies like the US dollar, but can also be linked to commodities like gold or other cryptocurrencies. They aim to reduce volatility relative to unpegged cryptocurrencies like Bitcoin.",
    category: "Finance",
    relatedTerms: ["USDC", "USDT", "DAI", "Collateralization", "Algorithmic Stablecoin"]
  },
  {
    id: "amm",
    term: "AMM (Automated Market Maker)",
    definition: "An automated market maker is a type of decentralized exchange protocol that relies on a mathematical formula to price assets. Instead of using an order book like a traditional exchange, assets are priced according to a pricing algorithm. This allows digital assets to be traded without permission and automatically by using liquidity pools instead of a traditional market of buyers and sellers.",
    category: "Finance",
    relatedTerms: ["Liquidity Pool", "Impermanent Loss", "Slippage", "Uniswap", "Constant Product Formula"]
  },
  {
    id: "yield-farming",
    term: "Yield Farming",
    definition: "Yield farming, also referred to as liquidity mining, is a way to generate rewards with cryptocurrency holdings. In simple terms, it means locking up cryptocurrencies and getting rewards. Yield farmers will typically move their funds around different lending platforms to maximize their returns. Returns are expressed as an annual percentage yield (APY).",
    category: "Finance",
    relatedTerms: ["APY", "Liquidity Mining", "Staking", "DeFi", "Yield Optimization"]
  },
  {
    id: "gas",
    term: "Gas",
    definition: "In blockchain networks, particularly Ethereum, gas refers to the fee required to successfully conduct a transaction or execute a contract on the blockchain. Gas is paid in the native cryptocurrency of the network (e.g., ETH for Ethereum). Gas prices are denoted in tiny fractions of the cryptocurrency known as gwei (or nanoeth).",
    category: "Technology",
    relatedTerms: ["Gas Limit", "Gas Price", "Gwei", "Transaction Fee", "EIP-1559"]
  }
];

const mockProtocolExplainers: ProtocolExplainer[] = [
  {
    id: "uniswap",
    name: "Uniswap",
    category: "Decentralized Exchange",
    description: "Uniswap is a decentralized cryptocurrency exchange that uses an automated market maker system instead of traditional order books. It allows users to trade cryptocurrencies without intermediaries, using liquidity pools to facilitate trading.",
    howItWorks: "Uniswap works through liquidity pools where users can contribute equal values of two tokens to create a market. Traders can then swap between these tokens, with prices determined by the constant product formula: x * y = k, where x and y are the amounts of the two tokens in the pool, and k is a constant. As one token is withdrawn, the other must be deposited at a rate that maintains this constant product, naturally leading to price changes based on supply and demand.",
    keyComponents: [
      { name: "Liquidity Pools", description: "Pools of tokens locked in smart contracts that provide liquidity for trading" },
      { name: "Liquidity Providers", description: "Users who deposit equal values of two tokens into a pool to enable trading and earn fees" },
      { name: "Swap Function", description: "The mechanism that allows users to trade one token for another via the pool" },
      { name: "Automated Market Maker", description: "The algorithm (x * y = k) that determines prices based on the ratio of tokens in the pool" },
      { name: "UNI Token", description: "The governance token that allows holders to vote on changes to the protocol" }
    ],
    diagram: "https://storage.googleapis.com/lovable-assets/how_uniswap_works.png",
    useCases: [
      "Trading between any ERC-20 tokens without requiring a counterparty",
      "Providing liquidity to earn trading fees",
      "Creating markets for new tokens without centralized listing processes",
      "Flash swaps for developers to build unique financial applications"
    ],
    risks: [
      "Impermanent loss for liquidity providers when token prices change",
      "Smart contract vulnerabilities could lead to loss of funds",
      "Front-running and MEV (Miner Extractable Value) exploitation",
      "Regulatory uncertainty surrounding decentralized exchanges"
    ],
    examples: [
      { name: "Uniswap App", url: "https://app.uniswap.org" },
      { name: "Uniswap Documentation", url: "https://docs.uniswap.org" }
    ]
  },
  {
    id: "aave",
    name: "Aave",
    category: "Lending Protocol",
    description: "Aave is a decentralized non-custodial liquidity protocol where users can participate as depositors or borrowers. Depositors provide liquidity to the market to earn a passive income, while borrowers can borrow in an overcollateralized or undercollateralized way.",
    howItWorks: "Aave operates through lending pools where users deposit assets that borrowers can take loans against. When users deposit, they receive aTokens that represent their deposit plus earned interest. Borrowers must provide collateral worth more than their loan (overcollateralization) to protect against volatility. Interest rates are algorithmically adjusted based on supply and demand in each pool. Aave also offers unique features like flash loans, which allow uncollateralized loans as long as they're repaid within the same blockchain transaction.",
    keyComponents: [
      { name: "Lending Pools", description: "Smart contracts holding deposited assets available for borrowing" },
      { name: "aTokens", description: "Interest-bearing tokens received by depositors that automatically increase in value" },
      { name: "Collateralization", description: "Assets deposited as security against loans, typically requiring >100% of the loan value" },
      { name: "Interest Rate Model", description: "Algorithm that determines borrowing and lending rates based on utilization" },
      { name: "Flash Loans", description: "Uncollateralized loans that must be borrowed and repaid within one transaction block" }
    ],
    diagram: "https://storage.googleapis.com/lovable-assets/how_aave_works.png",
    useCases: [
      "Earning interest on cryptocurrency holdings",
      "Borrowing assets while maintaining long exposure to deposited collateral",
      "Rate switching between stable and variable interest rates",
      "Flash loans for arbitrage, collateral swaps, or self-liquidation"
    ],
    risks: [
      "Liquidation risk if collateral value falls below required threshold",
      "Smart contract vulnerabilities despite security audits",
      "Interest rate fluctuations based on market conditions",
      "Oracle failures that could affect price feeds and liquidations"
    ],
    examples: [
      { name: "Aave App", url: "https://app.aave.com" },
      { name: "Aave Documentation", url: "https://docs.aave.com" }
    ]
  },
  {
    id: "compound",
    name: "Compound",
    category: "Lending Protocol",
    description: "Compound is an algorithmic, autonomous interest rate protocol designed for developers to unlock a universe of open financial applications. It lets users supply assets to earn interest or borrow assets against collateral.",
    howItWorks: "Compound creates money markets for various cryptocurrencies, where users can supply assets to earn interest or borrow against their supplied assets. When assets are supplied, users receive cTokens that represent their claim to the underlying asset plus accrued interest. Interest rates are determined algorithmically based on supply and demand in each market. Borrowers must maintain sufficient collateral or risk liquidation, where their collateral is sold at a discount to repay their debt.",
    keyComponents: [
      { name: "Money Markets", description: "Pools of assets with algorithmically determined interest rates" },
      { name: "cTokens", description: "Tokens representing claims on underlying assets and accrued interest" },
      { name: "Collateral Factor", description: "The percentage of an asset's value that can be borrowed against" },
      { name: "Interest Rate Model", description: "Algorithm that adjusts rates based on utilization of each market" },
      { name: "Governance", description: "COMP token holders can propose and vote on protocol changes" }
    ],
    diagram: "https://storage.googleapis.com/lovable-assets/how_compound_works.png",
    useCases: [
      "Earning interest on cryptocurrency holdings",
      "Borrowing assets for leverage, short selling, or liquidity",
      "Integrating with other DeFi protocols in composable applications",
      "Participating in governance through the COMP token"
    ],
    risks: [
      "Liquidation risk if collateral value decreases too much",
      "Smart contract vulnerabilities despite audits",
      "Interest rate fluctuations affecting borrowers",
      "Oracle manipulation or failures affecting price feeds"
    ],
    examples: [
      { name: "Compound App", url: "https://app.compound.finance" },
      { name: "Compound Documentation", url: "https://compound.finance/docs" }
    ]
  }
];

const mockKnowledgeArticles: KnowledgeArticle[] = [
  {
    id: "crypto-market-cycles",
    title: "Understanding Cryptocurrency Market Cycles",
    content: "Cryptocurrency markets are known for their cyclical nature, typically moving through four main phases: accumulation (when smart money begins buying), uptrend/markup (when prices rise and public interest grows), distribution (when smart money begins selling to new market entrants), and downtrend/markdown (when prices fall and sentiment turns negative).\n\nThese cycles are influenced by various factors including Bitcoin halving events (occurring approximately every four years), macroeconomic conditions, regulatory developments, technological advancements, and market sentiment.\n\nHistorically, Bitcoin has experienced several notable cycles. After the 2016 halving, Bitcoin rose from around $650 to nearly $20,000 in December 2017, before declining to around $3,200 in 2018. Following the 2020 halving, Bitcoin eventually reached nearly $69,000 in November 2021, before entering another corrective phase.\n\nUnderstanding these cycles can help investors make more informed decisions, though past patterns do not guarantee future results. Many investors use these cycles to inform strategies like dollar-cost averaging during downtrends and taking profits during euphoric uptrends.",
    category: "Markets",
    tags: ["Market Cycles", "Bitcoin", "Investing", "Trading", "Halving"],
    level: "Intermediate",
    lastUpdated: "2023-09-15",
    author: "Crypto Toolkit Team"
  },
  {
    id: "wallet-security",
    title: "Securing Your Cryptocurrency: Wallet Security Best Practices",
    content: "Securing your cryptocurrency holdings is paramount in the decentralized world of digital assets, where transactions are irreversible and the responsibility of security lies solely with you. This guide covers essential wallet security practices.\n\n**Types of Wallets**\n\n1. **Hardware Wallets**: Physical devices like Ledger or Trezor that store private keys offline, offering the highest security for long-term holders.\n\n2. **Software Wallets**: Applications on computers or smartphones, convenient but more vulnerable to malware.\n\n3. **Paper Wallets**: Physical documents containing public and private keys, secure against hacking but vulnerable to physical damage.\n\n4. **Custodial Wallets**: Managed by third parties like exchanges, convenient but giving up control of your private keys.\n\n**Security Best Practices**\n\n1. **Use Hardware Wallets for Significant Holdings**: Keep the majority of your crypto on hardware wallets disconnected from the internet.\n\n2. **Implement Strong Passwords**: Use unique, complex passwords for all wallet accounts.\n\n3. **Enable Two-Factor Authentication (2FA)**: Preferably using an authenticator app rather than SMS.\n\n4. **Secure Your Seed Phrase**: Store your 12/24-word recovery phrase in multiple secure locations, never digitally.\n\n5. **Regular Software Updates**: Keep wallet software and firmware updated to patch security vulnerabilities.\n\n6. **Verify Addresses**: Always double-check recipient addresses before confirming transactions.\n\n7. **Use Multi-signature Wallets**: For additional security, require multiple approvals for transactions.\n\n8. **Be Wary of Phishing**: Only download wallet apps from official sources and verify websites carefully.\n\nBy following these practices, you can significantly reduce the risk of losing your cryptocurrency to hacks, scams, or technical failures.",
    category: "Security",
    tags: ["Wallets", "Security", "Private Keys", "Hardware Wallets", "Seed Phrase"],
    level: "Beginner",
    lastUpdated: "2023-10-02",
    author: "Crypto Toolkit Team"
  },
  {
    id: "tokenomics",
    title: "Analyzing Tokenomics: What Makes a Sustainable Cryptocurrency",
    content: "Tokenomics—the economic design of a cryptocurrency—is one of the most important aspects to evaluate when researching potential investments. Good tokenomics can drive sustainable growth, while poor designs often lead to value deterioration.\n\n**Key Elements of Tokenomics**\n\n1. **Supply Mechanisms**:\n   - **Total Supply**: The maximum number of tokens that can ever exist\n   - **Circulating Supply**: Currently available tokens in the market\n   - **Inflation Rate**: How quickly new tokens are created\n   - **Burning Mechanisms**: Processes that permanently remove tokens from circulation\n\n2. **Distribution**:\n   - **Initial Allocation**: How tokens were initially distributed (public sale, team, investors, etc.)\n   - **Vesting Schedules**: When team and investor tokens become available to sell\n   - **Concentration**: Whether tokens are held by a small number of wallets (centralization risk)\n\n3. **Utility and Value Accrual**:\n   - **Transaction Fees**: Do fees benefit token holders?\n   - **Staking Rewards**: Returns for securing the network\n   - **Governance Rights**: Voting power in protocol decisions\n   - **Buybacks/Burns**: Mechanisms that reduce supply as usage grows\n\n4. **Incentive Alignment**:\n   - Do tokenomics align interests of developers, users, and investors?\n   - Are there mechanisms to prevent exploitative behaviors?\n\n**Red Flags**:\n   - Extremely high inflation without sufficient utility\n   - Large allocations to team/investors without long vesting\n   - Lack of clear utility driving demand\n   - Complicated emissions schedules designed to obscure inflation\n\n**Positive Indicators**:\n   - Sustainable supply growth or deflationary mechanisms\n   - Clear utility driving organic demand\n   - Fair distribution with appropriate vesting\n   - Value accrual mechanisms that benefit long-term holders\n\nBy thoroughly analyzing these aspects of tokenomics, investors can better identify projects with sustainable economic designs versus those likely to experience significant selling pressure and value decay over time.",
    category: "Fundamentals",
    tags: ["Tokenomics", "Investment Research", "Supply", "Inflation", "Value Accrual"],
    level: "Intermediate",
    lastUpdated: "2023-11-10",
    author: "Crypto Toolkit Team"
  }
];
