
import { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Loader2, Search, RefreshCw, TrendingUp, Info } from 'lucide-react';
import { useFundamentalAnalysis } from '@/hooks/useFundamentalAnalysis';
import { FundamentalTable } from './FundamentalTable';
import { CoinDetailCard } from './CoinDetailCard';
import { toast } from '@/hooks/use-toast';
import { 
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

export function FundamentalAnalysisDashboard() {
  const [customCoinId, setCustomCoinId] = useState<string>('');
  const [customCoinIds, setCustomCoinIds] = useState<string[]>([]);
  
  const { 
    topCoins, 
    customCoins, 
    isLoading, 
    error, 
    selectedCoin, 
    setSelectedCoinId,
    refreshData,
    historicalData,
    metricsData,
    isLoadingDetails
  } = useFundamentalAnalysis(30, customCoinIds);
  
  // Debug log to check what data we're getting
  useEffect(() => {
    console.log("Data loaded:", { 
      topCoins: topCoins.length, 
      customCoins: customCoins.length,
      loading: isLoading,
      error
    });
    
    if (topCoins.length > 0) {
      console.log("First coin sample:", topCoins[0]);
    }
  }, [topCoins, customCoins, isLoading, error]);
  
  const handleAddCoin = () => {
    if (!customCoinId.trim()) return;
    
    // Simple validation - check if it looks like a coin ID
    // In reality, we'd validate this against an API
    const normalizedId = customCoinId.trim().toLowerCase();
    
    if (customCoinIds.includes(normalizedId)) {
      toast({
        title: "Coin already added",
        description: "This coin is already in your custom list",
        variant: "destructive"
      });
      return;
    }
    
    setCustomCoinIds(prev => [...prev, normalizedId]);
    setCustomCoinId('');
  };
  
  const handleRefresh = () => {
    refreshData();
    toast({
      title: "Refreshing data",
      description: "Fetching the latest fundamental analysis data"
    });
  };
  
  // Auto-select first coin if none is selected and data is loaded
  useEffect(() => {
    if (!selectedCoin && topCoins.length > 0 && !isLoading) {
      setSelectedCoinId(topCoins[0].id);
    }
  }, [topCoins, selectedCoin, isLoading, setSelectedCoinId]);
  
  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between gap-4">
        <div className="flex-1">
          {/* Removed duplicate heading here */}
          <p className="text-sm text-muted-foreground mt-1">
            Analyze the underlying value of cryptocurrencies based on tokenomics, development activity, and utility metrics
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-2 w-full sm:max-w-xs">
            <Input
              placeholder="Add coin by ID (e.g., solana)"
              value={customCoinId}
              onChange={(e) => setCustomCoinId(e.target.value)}
              onKeyDown={(e) => e.key === 'Enter' && handleAddCoin()}
            />
            <Button variant="outline" onClick={handleAddCoin}>
              <Search className="h-4 w-4" />
            </Button>
          </div>
          <Button variant="outline" onClick={handleRefresh} disabled={isLoading || isLoadingDetails}>
            {isLoading || isLoadingDetails ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4" />
            )}
          </Button>
        </div>
      </div>
      
      {error ? (
        <div className="bg-destructive/10 border border-destructive/30 text-destructive p-4 rounded-md">
          Error loading fundamental data. Please try again later.
        </div>
      ) : (
        <div className="space-y-6">
          {selectedCoin && (
            <div className="animate-fade-in">
              <CoinDetailCard 
                coin={selectedCoin} 
                historicalData={historicalData}
                metricsData={metricsData}
                isLoadingDetails={isLoadingDetails}
              />
            </div>
          )}
          
          <Tabs defaultValue="top" className="w-full">
            <TabsList className="grid w-full max-w-md grid-cols-2">
              <TabsTrigger value="top" className="flex items-center gap-1">
                <TrendingUp className="h-4 w-4" />
                <span>Top Fundamentals</span>
              </TabsTrigger>
              <TabsTrigger value="custom" className="flex items-center gap-1" disabled={customCoinIds.length === 0}>
                <Search className="h-4 w-4" />
                <span>Custom Analysis ({customCoinIds.length})</span>
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="top" className="mt-4">
              {isLoading ? (
                <div className="flex items-center justify-center h-60">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : (
                <FundamentalTable 
                  coins={topCoins} 
                  onSelectCoin={setSelectedCoinId} 
                  isCompact={false}
                />
              )}
            </TabsContent>
            
            <TabsContent value="custom" className="mt-4">
              {customCoinIds.length === 0 ? (
                <div className="text-center py-12 text-muted-foreground bg-secondary/20 rounded-lg">
                  Add coins to your custom list using the search box above
                </div>
              ) : isLoading ? (
                <div className="flex items-center justify-center h-60">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : (
                <FundamentalTable 
                  coins={customCoins} 
                  onSelectCoin={setSelectedCoinId} 
                  isCompact={false}
                />
              )}
            </TabsContent>
          </Tabs>
        </div>
      )}
    </div>
  );
}
