
import axios from "axios";
import { cacheResponse, handleApiError } from "../coinGeckoClient";
import { YieldOpportunity } from "./types";

// Fetch yield opportunities from DefiLlama API
export const fetchYieldOpportunities = async (): Promise<YieldOpportunity[]> => {
  try {
    // DefiLlama API provides yield data across protocols
    const response = await axios.get("https://yields.llama.fi/pools");
    
    if (response.data && response.data.data) {
      // Map to our YieldOpportunity interface
      const opportunities = response.data.data
        .filter((pool: any) => pool.tvlUsd > 1000000) // Filter out low TVL pools
        .slice(0, 12) // Take top pools
        .map((pool: any) => {
          // Calculate risk score (simplified)
          const riskFactors = [
            pool.tvlUsd < 5000000 ? 2 : 0, // Low TVL adds risk
            pool.apy > 20 ? 1 : 0,         // High APY can indicate higher risk
            pool.stablecoin ? -1 : 0,      // Stablecoins reduce risk
            pool.il_risk === "no" ? -1 : 1 // Impermanent loss risk
          ];
          
          const riskScore = Math.min(10, Math.max(1, 
            5 + riskFactors.reduce((sum, val) => sum + val, 0)
          ));
          
          return {
            protocol: pool.project,
            pool: pool.symbol,
            chain: pool.chain,
            baseApy: pool.apy / 2, // Estimate base APY
            rewardsApr: pool.apy / 2, // Estimate rewards APR
            realApy: pool.apy,
            riskScore: riskScore,
            tvl: pool.tvlUsd
          };
        });
      
      cacheResponse("yield_opportunities", opportunities);
      return opportunities;
    }
    
    throw new Error("Invalid response format from DefiLlama yield API");
  } catch (error) {
    return handleApiError(error, {
      key: "yield_opportunities",
      data: []
    });
  }
};
