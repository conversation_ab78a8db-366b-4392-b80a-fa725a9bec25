
import { useState, useEffect } from "react";
import { useStats } from "@/hooks/useStats";
import { useRatingSystem } from "@/hooks/useRatingSystem";
import { HeaderBar } from "@/components/HeaderBar";
import { MarketTickerTape } from "@/components/MarketTickerTape";
import MarketStatusHeader from "@/components/dashboard/MarketStatusHeader";
import { PrimaryAnalyticsGrid } from "@/components/dashboard/PrimaryAnalyticsGrid";
import LoadingState from "@/components/dashboard/LoadingState";
import ErrorState from "@/components/dashboard/ErrorState";

export function DashboardContainer() {
  const { loading, error, stats, tvlData, fearGreed, trending, recentProjects, refreshData } = useStats();
  const { assets: ratingAssets, isLoading: ratingLoading } = useRatingSystem();

  if (loading) {
    return <LoadingState />;
  }

  if (error) {
    return <ErrorState error={error} onRefresh={refreshData} />;
  }

  const transformedProjects = recentProjects.map((project, index) => ({
    id: project.id,
    name: project.name,
    symbol: project.symbol,
    brokerScore: Math.random() * 4 + 6,
    price: project.price || Math.random() * 100,
    priceChange: project.change24h || (Math.random() * 20 - 10),
    rank: index + 1
  }));

  return (
    <div className="flex-1 flex flex-col min-w-0 bg-background text-foreground overflow-hidden">
      <HeaderBar
        title="Dashboard"
        description="Professional cryptocurrency analytics platform"
        onRefresh={refreshData}
        isLoading={loading}
      />

      <MarketTickerTape />
      <MarketStatusHeader stats={stats} />

      <main className="flex-1 overflow-y-auto bg-background">
        <div className="max-w-screen-2xl mx-auto p-6 space-y-6">
          <PrimaryAnalyticsGrid
            stats={stats}
            tvlData={tvlData}
            fearGreed={fearGreed}
            trending={trending}
            ratingAssets={ratingAssets}
            ratingLoading={ratingLoading}
            transformedProjects={transformedProjects}
          />
        </div>
      </main>
    </div>
  );
}
