
import { useMemo } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent, CardDescription } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { ArrowUpDown, AlertTriangle, Info } from "lucide-react";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import SecurityBadge from "./SecurityBadge";
import { calculateFees, formatCurrency } from "./bridgeUtils";

interface BridgeComparisonTableProps {
  isLoading: boolean;
  sourceChain: string;
  targetChain: string;
  amount: string;
  bridges: any[];
  sortField: string;
  sortDirection: "asc" | "desc";
  selectedBridge: string | null;
  onSortChange: (field: string) => void;
  onDirectionChange: (direction: "asc" | "desc") => void;
  onBridgeSelect: (bridgeId: string) => void;
}

const BridgeComparisonTable = ({
  isLoading,
  sourceChain,
  targetChain,
  amount,
  bridges,
  sortField,
  sortDirection,
  selectedBridge,
  onSortChange,
  onDirectionChange,
  onBridgeSelect,
}: BridgeComparisonTableProps) => {

  // Filter bridges that support both source and target chains
  const filteredBridges = useMemo(() => {
    if (!bridges || bridges.length === 0) return [];

    return bridges.filter(bridge =>
      bridge.supportedNetworks.includes(sourceChain) &&
      bridge.supportedNetworks.includes(targetChain) &&
      sourceChain !== targetChain
    );
  }, [bridges, sourceChain, targetChain]);

  // Sort filtered bridges
  const sortedBridges = useMemo(() => {
    if (!filteredBridges || filteredBridges.length === 0) return [];

    return [...filteredBridges].sort((a, b) => {
      let valueA: any = a[sortField as keyof typeof a];
      let valueB: any = b[sortField as keyof typeof b];

      // Special handling for nested properties
      if (sortField === "fees.percentage") {
        valueA = a.fees.percentage;
        valueB = b.fees.percentage;
      } else if (sortField === "speed") {
        valueA = a.speed[sourceChain.toLowerCase()] || 999;
        valueB = b.speed[sourceChain.toLowerCase()] || 999;
      }

      // Handle string vs number comparison
      if (typeof valueA === "string" && typeof valueB === "string") {
        return sortDirection === "asc"
          ? valueA.localeCompare(valueB)
          : valueB.localeCompare(valueA);
      } else {
        // Numeric comparison
        valueA = valueA as number;
        valueB = valueB as number;
        return sortDirection === "asc" ? valueA - valueB : valueB - valueA;
      }
    });
  }, [filteredBridges, sortField, sortDirection, sourceChain]);

  // Handle sort click
  const handleSort = (field: string) => {
    if (sortField === field) {
      onDirectionChange(sortDirection === "asc" ? "desc" : "asc");
    } else {
      onSortChange(field);
      onDirectionChange("desc");
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Bridge Comparison</CardTitle>
        <CardDescription>
          Bridges supporting {sourceChain} to {targetChain} transfer
        </CardDescription>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <Skeleton className="h-64 w-full" />
        ) : sortedBridges.length === 0 ? (
          <div className="flex flex-col items-center justify-center p-8 text-center">
            <AlertTriangle className="h-10 w-10 text-yellow-500 mb-2" />
            <h3 className="text-lg font-semibold">No Compatible Bridges Found</h3>
            <p className="text-muted-foreground">
              No bridges currently support direct transfers from {sourceChain} to {targetChain}.
              Please try a different chain pair.
            </p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead
                    className="cursor-pointer"
                    onClick={() => handleSort("name")}
                  >
                    Bridge
                    {sortField === "name" && (
                      <ArrowUpDown className="ml-1 h-3 w-3 inline" />
                    )}
                  </TableHead>
                  <TableHead
                    className="text-right cursor-pointer"
                    onClick={() => handleSort("securityScore")}
                  >
                    Security
                    {sortField === "securityScore" && (
                      <ArrowUpDown className="ml-1 h-3 w-3 inline" />
                    )}
                  </TableHead>
                  <TableHead
                    className="text-right cursor-pointer"
                    onClick={() => handleSort("fees.percentage")}
                  >
                    Fees
                    {sortField === "fees.percentage" && (
                      <ArrowUpDown className="ml-1 h-3 w-3 inline" />
                    )}
                  </TableHead>
                  <TableHead
                    className="text-right cursor-pointer"
                    onClick={() => handleSort("speed")}
                  >
                    Speed
                    {sortField === "speed" && (
                      <ArrowUpDown className="ml-1 h-3 w-3 inline" />
                    )}
                  </TableHead>
                  <TableHead
                    className="text-right cursor-pointer"
                    onClick={() => handleSort("userRating")}
                  >
                    User Rating
                    {sortField === "userRating" && (
                      <ArrowUpDown className="ml-1 h-3 w-3 inline" />
                    )}
                  </TableHead>
                  <TableHead className="text-right">
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger className="flex items-center gap-1">
                          Est. Cost <Info className="h-3.5 w-3.5" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p className="w-60">Estimated cost for transferring {amount} USD</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {sortedBridges.map((bridge) => {
                  const fees = calculateFees(bridge, parseFloat(amount), sourceChain);
                  const speedMinutes = bridge.speed[sourceChain.toLowerCase()] || "N/A";

                  return (
                    <TableRow
                      key={bridge.id}
                      className={`cursor-pointer ${selectedBridge === bridge.id ? 'bg-primary/10' : ''}`}
                      onClick={() => onBridgeSelect(bridge.id)}
                    >
                      <TableCell className="font-medium">{bridge.name}</TableCell>
                      <TableCell className="text-right">
                        <SecurityBadge score={bridge.securityScore} />
                      </TableCell>
                      <TableCell className="text-right">
                        {(bridge.fees.percentage * 100).toFixed(2)}% + ${bridge.fees.fixed[sourceChain.toLowerCase()]?.toFixed(2) || "0.00"}
                      </TableCell>
                      <TableCell className="text-right">
                        <Badge variant="outline" className="font-medium">
                          {typeof speedMinutes === 'number' ? `~${speedMinutes} min` : speedMinutes}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center justify-end">
                          {[...Array(5)].map((_, i) => (
                            <span
                              key={i}
                              className={`text-sm ${i < Math.floor(bridge.userRating) ? 'text-yellow-500' : 'text-gray-300'}`}
                            >
                              ★
                            </span>
                          ))}
                          <span className="ml-1 text-xs">{bridge.userRating.toFixed(1)}</span>
                        </div>
                      </TableCell>
                      <TableCell className="font-medium text-right">
                        {formatCurrency(fees.total)}
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default BridgeComparisonTable;
