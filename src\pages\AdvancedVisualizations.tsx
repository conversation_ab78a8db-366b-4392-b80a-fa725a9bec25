import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import HeaderBar from "@/components/HeaderBar";
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import CandlestickChart from "@/components/CandlestickChart";
import HeatMapChart from "@/components/HeatMapChart";
import NetworkGraph from "@/components/NetworkGraph";
import { Button } from "@/components/ui/button";
import { getPriceForecastData } from "@/services/api/priceHistory"; // Updated import path
import { fetchTopCoins, generateCorrelationData, fetchRecentlyAdded } from "@/services/api";
import { Loader2, ChartCandlestick, Grid3X3, Network, Activity } from "lucide-react";
import { toast } from "@/hooks/use-toast";

export default function AdvancedVisualizations() {
  const [selectedCoin, setSelectedCoin] = useState({ id: "bitcoin", name: "Bitcoin", symbol: "BTC" });
  const [selectedTimeframe, setSelectedTimeframe] = useState("30D");
  
  // Map timeframe to days
  const timeframeMap: Record<string, number> = {
    "1D": 1,
    "7D": 7,
    "30D": 30,
    "90D": 90,
    "1Y": 365
  };
  
  // 1. Fetch top coins for the button selection
  const { 
    data: topCoins, 
    isLoading: isLoadingCoins,
    refetch: refetchCoins
  } = useQuery({
    queryKey: ['visualization-coins'],
    queryFn: async () => {
      const coins = await fetchTopCoins(10);
      return coins.map((coin: any) => ({
        id: coin.id,
        name: coin.name,
        symbol: coin.symbol.toUpperCase()
      }));
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
  
  // 2. Fetch technical data for selected coin
  const {
    data: chartData,
    isLoading: isLoadingChart,
    refetch: refetchChart
  } = useQuery({
    queryKey: ['technical-data', selectedCoin.id, selectedTimeframe],
    queryFn: () => getPriceForecastData(selectedCoin.id, timeframeMap[selectedTimeframe]),
    enabled: !!selectedCoin.id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
  
  // 3. Fetch heatmap data (market sectors with performance)
  const {
    data: heatmapData,
    isLoading: isLoadingHeatmap,
    refetch: refetchHeatmap
  } = useQuery({
    queryKey: ['heatmap-data'],
    queryFn: async () => {
      // Normally we'd fetch real heatmap data, but we'll generate mock data 
      // from recent coins and sectors
      const recentCoins = await fetchRecentlyAdded(30);
      
      // Group by categories
      const sectors = [
        "DeFi", "Layer 1", "Layer 2", "Privacy", "Gaming", 
        "NFT", "Metaverse", "Storage", "Oracle", "Exchange"
      ];
      
      return recentCoins.map((coin: any, index: number) => ({
        name: coin.symbol.toUpperCase(),
        value: coin.current_price || (Math.random() * 100 + 1),
        percentChange: coin.price_change_percentage_24h || (Math.random() * 40 - 20),
        category: coin.categories?.[0] || sectors[index % sectors.length]
      }));
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
  
  // 4. Fetch correlation data (relationships between assets)
  const {
    data: correlationData,
    isLoading: isLoadingCorrelation,
    refetch: refetchCorrelation
  } = useQuery({
    queryKey: ['correlation-data', selectedCoin.id],
    queryFn: () => generateCorrelationData(selectedCoin.id, 30),
    enabled: !!selectedCoin.id,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
  
  // 5. Generate network graph data (DeFi protocols, token flows)
  const {
    data: networkData,
    isLoading: isLoadingNetwork
  } = useQuery({
    queryKey: ['network-data'],
    queryFn: () => {
      // Mock data for the network graph
      const nodes = [
        { id: 'ethereum', name: 'Ethereum', group: 'chain', value: 100 },
        { id: 'bitcoin', name: 'Bitcoin', group: 'chain', value: 120 },
        { id: 'solana', name: 'Solana', group: 'chain', value: 80 },
        { id: 'polygon', name: 'Polygon', group: 'chain', value: 60 },
        { id: 'uniswap', name: 'Uniswap', group: 'defi', value: 70 },
        { id: 'aave', name: 'Aave', group: 'defi', value: 55 },
        { id: 'curve', name: 'Curve', group: 'defi', value: 50 },
        { id: 'maker', name: 'MakerDAO', group: 'defi', value: 45 },
        { id: 'usdt', name: 'USDT', group: 'token', value: 85 },
        { id: 'usdc', name: 'USDC', group: 'token', value: 75 },
        { id: 'dai', name: 'DAI', group: 'token', value: 40 },
        { id: 'chainlink', name: 'Chainlink', group: 'oracle', value: 65 },
        { id: 'binance', name: 'Binance', group: 'exchange', value: 95 },
        { id: 'coinbase', name: 'Coinbase', group: 'exchange', value: 85 },
        { id: 'wormhole', name: 'Wormhole', group: 'bridge', value: 40 },
        { id: 'stargate', name: 'Stargate', group: 'bridge', value: 35 }
      ];
      
      const links = [
        { source: 'ethereum', target: 'uniswap', value: 10, type: 'supply' },
        { source: 'ethereum', target: 'aave', value: 8, type: 'supply' },
        { source: 'ethereum', target: 'maker', value: 7, type: 'supply' },
        { source: 'ethereum', target: 'usdt', value: 15, type: 'supply' },
        { source: 'ethereum', target: 'usdc', value: 12, type: 'supply' },
        { source: 'ethereum', target: 'dai', value: 9, type: 'supply' },
        { source: 'ethereum', target: 'chainlink', value: 6, type: 'supply' },
        { source: 'solana', target: 'usdc', value: 8, type: 'supply' },
        { source: 'solana', target: 'usdt', value: 6, type: 'supply' },
        { source: 'polygon', target: 'aave', value: 5, type: 'supply' },
        { source: 'polygon', target: 'usdc', value: 7, type: 'supply' },
        { source: 'uniswap', target: 'usdt', value: 9, type: 'swap' },
        { source: 'uniswap', target: 'usdc', value: 8, type: 'swap' },
        { source: 'uniswap', target: 'dai', value: 6, type: 'swap' },
        { source: 'maker', target: 'dai', value: 10, type: 'supply' },
        { source: 'aave', target: 'usdt', value: 7, type: 'liquidity' },
        { source: 'aave', target: 'usdc', value: 6, type: 'liquidity' },
        { source: 'curve', target: 'usdt', value: 8, type: 'liquidity' },
        { source: 'curve', target: 'usdc', value: 7, type: 'liquidity' },
        { source: 'curve', target: 'dai', value: 6, type: 'liquidity' },
        { source: 'chainlink', target: 'ethereum', value: 5, type: 'oracle' },
        { source: 'chainlink', target: 'solana', value: 4, type: 'oracle' },
        { source: 'wormhole', target: 'ethereum', value: 6, type: 'bridge' },
        { source: 'wormhole', target: 'solana', value: 5, type: 'bridge' },
        { source: 'stargate', target: 'ethereum', value: 5, type: 'bridge' },
        { source: 'stargate', target: 'polygon', value: 4, type: 'bridge' },
        { source: 'binance', target: 'usdt', value: 12, type: 'swap' },
        { source: 'binance', target: 'bitcoin', value: 15, type: 'swap' },
        { source: 'coinbase', target: 'bitcoin', value: 14, type: 'swap' },
        { source: 'coinbase', target: 'ethereum', value: 13, type: 'swap' }
      ];
      
      return { nodes, links };
    },
    staleTime: 30 * 60 * 1000, // 30 minutes
  });
  
  // Refresh all data
  const refreshData = async () => {
    toast({
      title: "Refreshing visualization data",
      description: "Fetching latest market data for all charts...",
    });
    
    await Promise.all([
      refetchCoins(), 
      refetchChart(), 
      refetchHeatmap(), 
      refetchCorrelation()
    ]);
    
    toast({
      title: "Data refreshed",
      description: "All visualizations have been updated with the latest data.",
    });
  };
  
  const isLoading = isLoadingCoins || isLoadingChart || isLoadingHeatmap || isLoadingCorrelation || isLoadingNetwork;
  
  return (
    <div className="flex-1 flex flex-col min-w-0">
      <HeaderBar 
        title="Advanced Visualizations" 
        description="Interactive data visualizations for deep market analysis"
        onRefresh={refreshData}
        isLoading={isLoading}
      />
      
      <main className="flex-1 overflow-y-auto p-4 md:p-6 lg:p-8">
        <div className="max-w-screen-2xl mx-auto space-y-6">
          {/* Asset selector */}
          <div className="flex flex-wrap gap-3 mb-4">
            {isLoadingCoins ? (
              <div className="flex items-center gap-2">
                <Loader2 size={16} className="animate-spin" />
                <span>Loading coins...</span>
              </div>
            ) : (
              topCoins?.map((coin: any) => (
                <Button
                  key={coin.id}
                  variant={selectedCoin.id === coin.id ? 'default' : 'outline'}
                  onClick={() => setSelectedCoin(coin)}
                  className="flex items-center gap-2"
                >
                  {coin.name} ({coin.symbol})
                </Button>
              ))
            )}
          </div>
          
          <Tabs defaultValue="candlestick">
            <div className="flex justify-between items-center mb-6">
              <TabsList>
                <TabsTrigger value="candlestick" className="flex items-center gap-2">
                  <ChartCandlestick size={16} />
                  <span>Technical Analysis</span>
                </TabsTrigger>
                <TabsTrigger value="heatmap" className="flex items-center gap-2">
                  <Grid3X3 size={16} />
                  <span>Market Heatmap</span>
                </TabsTrigger>
                <TabsTrigger value="network" className="flex items-center gap-2">
                  <Network size={16} />
                  <span>Network Analysis</span>
                </TabsTrigger>
              </TabsList>
            </div>
            
            {/* Technical Analysis Tab */}
            <TabsContent value="candlestick" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Advanced Technical Analysis</CardTitle>
                  <CardDescription>
                    Interactive candlestick chart with technical indicators and price predictions
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <CandlestickChart 
                    data={chartData?.data || []}
                    title={`${selectedCoin.name} (${selectedCoin.symbol})`}
                    description="Price chart with technical indicators and forecast"
                    timeframes={Object.keys(timeframeMap)}
                    onTimeframeChange={setSelectedTimeframe}
                    isLoading={isLoadingChart}
                    height={500}
                    showTechnicalIndicators={true}
                  />
                </CardContent>
              </Card>
              
              {/* Correlation chart */}
              <Card>
                <CardHeader>
                  <CardTitle>Asset Correlation Analysis</CardTitle>
                  <CardDescription>
                    Visualizing price correlations between {selectedCoin.name} and other major assets
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {isLoadingCorrelation ? (
                    <div className="flex items-center justify-center h-[300px]">
                      <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                    </div>
                  ) : correlationData && correlationData.length > 0 ? (
                    <HeatMapChart 
                      title="Asset Correlation Heatmap"
                      data={correlationData.map((item: any) => ({
                        name: item.assetSymbol,
                        value: item.correlation,
                        percentChange: item.comparedChange,
                        category: "Correlation with " + selectedCoin.symbol
                      }))}
                      isLoading={isLoadingCorrelation}
                      colorScale="correlation"
                    />
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      No correlation data available for this asset
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
            
            {/* Market Heatmap Tab */}
            <TabsContent value="heatmap">
              <Card>
                <CardHeader>
                  <CardTitle>Market Sector Performance Heatmap</CardTitle>
                  <CardDescription>
                    Visual representation of market performance across different sectors.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <HeatMapChart 
                    title="Market Sector Performance"
                    data={heatmapData || []}
                    isLoading={isLoadingHeatmap}
                    colorScale="performance"
                  />
                </CardContent>
              </Card>
            </TabsContent>
            
            {/* Network Analysis Tab */}
            <TabsContent value="network">
              <Card>
                <CardHeader>
                  <CardTitle>Crypto Ecosystem Network Analysis</CardTitle>
                  <CardDescription>
                    Interactive visualization of relationships between DeFi protocols, tokens, and blockchain networks
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <NetworkGraph 
                    title="DeFi Ecosystem Network"
                    description="Visualizing connections between protocols, tokens, and chains"
                    nodes={networkData?.nodes || []}
                    links={networkData?.links || []}
                    isLoading={isLoadingNetwork}
                    height={600}
                    layoutOptions={{
                      chargeStrength: -120,
                      linkDistance: 120
                    }}
                  />
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
          
          <div className="text-sm text-muted-foreground mt-8 p-4 bg-card rounded-lg border">
            <div className="font-medium mb-2 flex items-center gap-2">
              <Activity className="h-4 w-4" />
              <span>About Advanced Visualizations</span>
            </div>
            <p className="mb-2">
              These advanced data visualizations help you analyze market trends, correlations, and ecosystem relationships at a deeper level:
            </p>
            <ul className="list-disc pl-5 space-y-1">
              <li><strong>Technical Analysis:</strong> Interactive candlestick charts with technical indicators like RSI, MACD, Bollinger Bands, and moving averages.</li>
              <li><strong>Market Heatmap:</strong> Color-coded representation of market performance across different sectors.</li>
              <li><strong>Network Analysis:</strong> Visualize complex relationships between protocols, tokens, and blockchain networks.</li>
              <li><strong>Correlation Analysis:</strong> Understand how different assets move in relation to each other.</li>
            </ul>
          </div>
        </div>
      </main>
    </div>
  );
}
