
import { FC } from "react";
import { Tutorial } from "@/hooks/useEducation";
import BlockchainEducation from "./BlockchainEducation";
import IntroductionToDeFi from "./IntroductionToDeFi";
import CryptoInvestmentStrategies from "./CryptoInvestmentStrategies";
import WalletSecurity from "./WalletSecurity";
import BuyingFirstCrypto from "./BuyingFirstCrypto";
import EducationTutorial from "./EducationTutorial";

interface TutorialContentRendererProps {
  tutorialId: string;
  getTutorialById: (id: string) => Tutorial | undefined;
  loading: boolean;
}

const TutorialContentRenderer: FC<TutorialContentRendererProps> = ({ 
  tutorialId, 
  getTutorialById, 
  loading 
}) => {
  // Map tutorial IDs to specific tutorial components
  switch (tutorialId) {
    case "blockchain-fundamentals":
      return <BlockchainEducation loading={loading} />;
    case "defi-introduction":
      return <IntroductionToDeFi loading={loading} />;
    case "crypto-investing":
      return <CryptoInvestmentStrategies loading={loading} />;
    case "wallet-security":
      return <WalletSecurity loading={loading} />;
    case "blockchain-basics":
      return <BlockchainEducation loading={loading} />;
    case "buying-first-crypto":
      return <BuyingFirstCrypto loading={loading} />;
    default:
      // For regular tutorials from the API
      const tutorial = getTutorialById(tutorialId);
      if (!tutorial) {
        return <div className="p-8 text-center">Tutorial not found</div>;
      }
      return <EducationTutorial tutorial={tutorial} />;
  }
};

export default TutorialContentRenderer;
