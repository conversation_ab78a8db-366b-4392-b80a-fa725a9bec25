
import { supabase } from '@/integrations/supabase/client';
import { Profile } from './types';

export const getNotificationPreference = (preferences: any, key: string): boolean => {
  if (typeof preferences === 'object' && preferences !== null && !Array.isArray(preferences)) {
    return Boolean(preferences[key]);
  }
  return false;
};

export const fetchUserProfile = async (userId: string): Promise<Profile | null> => {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        // No profile found, this is normal for new users
        console.log('No profile found for user, will create on first update');
        return null;
      }
      throw error;
    }

    if (data) {
      const profileData: Profile = {
        id: data.id,
        username: data.username,
        full_name: data.full_name,
        avatar_url: data.avatar_url,
        theme: data.theme || 'light',
        notification_preferences: {
          email: getNotificationPreference(data.notification_preferences, 'email'),
          push: getNotificationPreference(data.notification_preferences, 'push')
        }
      };
      return profileData;
    }
    
    return null;
  } catch (error) {
    console.error('Error fetching user profile:', error);
    return null;
  }
};

export const updateUserProfile = async (userId: string, updates: Partial<Profile>): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('profiles')
      .upsert({
        id: userId,
        ...updates,
        updated_at: new Date().toISOString()
      });
    
    if (error) throw error;
    
    return true;
  } catch (error) {
    console.error('Error updating user profile:', error);
    throw error;
  }
};
