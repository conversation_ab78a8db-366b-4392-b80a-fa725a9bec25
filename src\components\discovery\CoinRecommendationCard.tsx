
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { TrendingUp, TrendingDown, Star, Activity, Eye } from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { getCoinAnalysis } from "@/services/api/coinDiscoveryService";

interface CoinRecommendationCardProps {
  coin: any;
}

export default function CoinRecommendationCard({ coin }: CoinRecommendationCardProps) {
  const [showDetails, setShowDetails] = useState(false);
  
  const { data: analysis, isLoading: analysisLoading } = useQuery({
    queryKey: ['coinAnalysis', coin.id],
    queryFn: () => getCoinAnalysis(coin.id),
    enabled: showDetails,
  });

  const priceChange = coin.price_change_percentage_24h || 0;
  const isPositive = priceChange >= 0;

  return (
    <Card className="hover:shadow-lg transition-shadow duration-200">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2">
            {coin.image && (
              <img src={coin.image} alt={coin.name} className="w-6 h-6 rounded-full" />
            )}
            <span className="truncate">{coin.name}</span>
          </CardTitle>
          <Badge variant="outline" className="text-xs">
            #{coin.market_cap_rank || 'N/A'}
          </Badge>
        </div>
        <div className="flex items-center justify-between">
          <span className="text-sm text-muted-foreground">{coin.symbol?.toUpperCase()}</span>
          <div className="flex items-center gap-1">
            {isPositive ? (
              <TrendingUp className="h-4 w-4 text-green-500" />
            ) : (
              <TrendingDown className="h-4 w-4 text-red-500" />
            )}
            <span className={`text-sm font-medium ${isPositive ? 'text-green-600' : 'text-red-600'}`}>
              {isPositive ? '+' : ''}{priceChange.toFixed(1)}%
            </span>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-3">
        <div className="grid grid-cols-2 gap-2 text-sm">
          <div>
            <span className="text-muted-foreground">Price</span>
            <p className="font-medium">
              ${coin.current_price?.toLocaleString() || 'N/A'}
            </p>
          </div>
          <div>
            <span className="text-muted-foreground">Volume</span>
            <p className="font-medium">
              ${(coin.total_volume / 1000000).toFixed(1)}M
            </p>
          </div>
        </div>

        {coin.developer_score && (
          <div className="flex items-center gap-2">
            <Activity className="h-4 w-4 text-blue-500" />
            <span className="text-sm">Dev Activity: {coin.developer_score}</span>
          </div>
        )}

        {showDetails && (
          <div className="mt-4 p-3 bg-muted/50 rounded-lg">
            {analysisLoading ? (
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              </div>
            ) : (
              <div>
                <p className="text-sm text-muted-foreground mb-2">AI Analysis:</p>
                <p className="text-sm">{analysis?.aiAnalysis || 'Analysis not available.'}</p>
              </div>
            )}
          </div>
        )}

        <div className="flex gap-2 pt-2">
          <Button 
            variant="outline" 
            size="sm" 
            className="flex-1"
            onClick={() => setShowDetails(!showDetails)}
          >
            <Eye className="h-4 w-4 mr-1" />
            {showDetails ? 'Hide' : 'Analyze'}
          </Button>
          <Button size="sm" className="flex-1">
            <Star className="h-4 w-4 mr-1" />
            Watch
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
