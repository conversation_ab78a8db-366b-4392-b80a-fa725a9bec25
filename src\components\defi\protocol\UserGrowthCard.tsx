
// React import removed - using new JSX transform
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ChartBar } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts";

interface UserGrowthCardProps {
  protocol: any | null;
  isLoading: boolean;
}

const UserGrowthCard = ({ protocol, isLoading }: UserGrowthCardProps) => {
  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex justify-between items-center">
          <CardTitle className="text-lg flex items-center gap-1">
            <ChartBar className="h-4 w-4" /> User Growth
          </CardTitle>
          {!isLoading && protocol && (
            <Badge variant={protocol.userGrowth >= 0 ? "default" : "destructive"}>
              {protocol.userGrowth > 0 ? "+" : ""}
              {protocol.userGrowth.toFixed(2)}%
            </Badge>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {isLoading || !protocol ? (
          <div className="space-y-4">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-40 w-full" />
          </div>
        ) : (
          <div className="space-y-3">
            <div>
              <p className="text-sm text-muted-foreground">Active Users</p>
              <p className="text-3xl font-bold">
                {new Intl.NumberFormat().format(protocol.userCount)}
              </p>
            </div>

            <div className="h-[180px]">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={protocol.userTrend}
                  margin={{ top: 5, right: 5, left: 5, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" opacity={0.15} />
                  <XAxis
                    dataKey="date"
                    tickFormatter={(date) => new Date(date).toLocaleDateString(undefined, { month: 'short', day: 'numeric' })}
                    tick={{ fontSize: 10 }}
                    dy={10}
                  />
                  <YAxis
                    tickFormatter={(value) => new Intl.NumberFormat('en', { notation: 'compact' }).format(value)}
                    tick={{ fontSize: 10 }}
                    width={40}
                  />
                  <Tooltip
                    formatter={(value: number) => [new Intl.NumberFormat().format(value), "Users"]}
                    labelFormatter={(label) => new Date(label).toLocaleDateString()}
                  />
                  <Line
                    type="monotone"
                    dataKey="users"
                    stroke="#10b981"
                    dot={false}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default UserGrowthCard;
