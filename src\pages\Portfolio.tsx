import { useState, useEffect } from "react";
import { HeaderBar } from "@/components/HeaderBar";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { fetchPortfolioData, PortfolioData } from "@/services/api/portfolioService";
import { ArrowUpRight, Wallet } from "lucide-react";
import { cn } from "@/lib/utils";
import { toast } from "@/components/ui/use-toast";

export default function Portfolio() {
  const [portfolioData, setPortfolioData] = useState<PortfolioData>({
    totalValue: 0,
    dailyChange: 0,
    monthlyChange: 0,
    assets: []
  });
  const [isLoading, setIsLoading] = useState<boolean>(true);

  useEffect(() => {
    const loadPortfolioData = async () => {
      setIsLoading(true);
      try {
        const data = await fetchPortfolioData();
        setPortfolioData(data);
      } catch (error) {
        console.error("Failed to fetch portfolio data:", error);
        toast({
          title: "Error",
          description: "Failed to load portfolio data. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadPortfolioData();
  }, []);

  return (
    <div className="flex-1 flex flex-col min-w-0">
      <HeaderBar
        title="Portfolio Optimizer"
        description="Modern Portfolio Theory optimization and analysis"
        isLoading={isLoading}
        onRefresh={() => {
          setIsLoading(true);
          fetchPortfolioData().then(data => {
            setPortfolioData(data);
            setIsLoading(false);
            toast({
              title: "Data refreshed",
              description: "Latest portfolio data has been loaded.",
            });
          });
        }}
      />

      <main className="flex-1 overflow-y-auto p-4 md:p-6 lg:p-8">
        <div className="max-w-screen-2xl mx-auto space-y-6">
          <div className="bg-card border border-border rounded-lg p-6 gradient-border">
            <div className="flex items-center justify-between mb-6">
              <div className="space-y-1">
                <h2 className="text-xl font-medium">Current Portfolio</h2>
                <p className="text-muted-foreground text-sm">
                  Connect your wallet or manually input your assets
                </p>
              </div>

              <Button className="flex items-center gap-2">
                <Wallet size={16} />
                <span>Connect Wallet</span>
              </Button>
            </div>

            <div className="flex flex-wrap gap-4 mb-8">
              <div className="bg-secondary p-4 rounded-lg">
                <p className="text-muted-foreground text-sm mb-1">Total Value</p>
                <p className="text-2xl font-semibold">
                  ${portfolioData.totalValue.toLocaleString()}
                </p>
              </div>

              <div className="bg-secondary p-4 rounded-lg">
                <p className="text-muted-foreground text-sm mb-1">24h Change</p>
                <p className={cn(
                  "text-2xl font-semibold flex items-center",
                  portfolioData.dailyChange >= 0 ? "text-crypto-positive" : "text-crypto-negative"
                )}>
                  {portfolioData.dailyChange >= 0 ? <ArrowUpRight size={20} /> : "↓"}
                  {Math.abs(portfolioData.dailyChange)}%
                </p>
              </div>

              <div className="bg-secondary p-4 rounded-lg">
                <p className="text-muted-foreground text-sm mb-1">30d Change</p>
                <p className={cn(
                  "text-2xl font-semibold flex items-center",
                  portfolioData.monthlyChange >= 0 ? "text-crypto-positive" : "text-crypto-negative"
                )}>
                  {portfolioData.monthlyChange >= 0 ? <ArrowUpRight size={20} /> : "↓"}
                  {Math.abs(portfolioData.monthlyChange)}%
                </p>
              </div>
            </div>

            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-border">
                    <th className="text-left p-3 font-medium text-muted-foreground">Asset</th>
                    <th className="text-left p-3 font-medium text-muted-foreground">Allocation</th>
                    <th className="text-left p-3 font-medium text-muted-foreground">Value</th>
                    <th className="text-right p-3 font-medium text-muted-foreground">24h Change</th>
                  </tr>
                </thead>
                <tbody>
                  {portfolioData.assets.map((asset, index) => (
                    <tr key={index} className="border-b border-border/50 hover:bg-secondary/30">
                      <td className="p-3">
                        <div className="flex items-center gap-3">
                          <div className="h-8 w-8 rounded-full bg-secondary flex items-center justify-center">
                            {asset.symbol.charAt(0)}
                          </div>
                          <div>
                            <div className="font-medium">{asset.name}</div>
                            <div className="text-xs text-muted-foreground">{asset.symbol}</div>
                          </div>
                        </div>
                      </td>
                      <td className="p-3">
                        <div className="flex items-center gap-2">
                          <div className="h-2 w-24 bg-secondary rounded-full overflow-hidden">
                            <div
                              className="h-full bg-primary"
                              style={{ width: `${asset.allocation}%` }}
                            ></div>
                          </div>
                          <span>{asset.allocation}%</span>
                        </div>
                      </td>
                      <td className="p-3">${asset.value.toLocaleString()}</td>
                      <td className="p-3 text-right">
                        <span className={cn(
                          asset.change >= 0 ? "text-crypto-positive" : "text-crypto-negative"
                        )}>
                          {asset.change >= 0 ? "+" : ""}{asset.change.toFixed(2)}%
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle>Portfolio Optimization</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-center h-64 border border-dashed border-border rounded-md">
                  <div className="text-center">
                    <p className="text-muted-foreground mb-4">
                      Connect to Supabase to enable portfolio optimization
                    </p>
                    <Button disabled>
                      Connect Supabase
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Optimization Settings</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4 text-sm">
                  <div>
                    <h4 className="font-medium">Risk Tolerance</h4>
                    <input
                      type="range"
                      min="1"
                      max="10"
                      defaultValue="5"
                      className="w-full h-2 bg-secondary rounded-lg appearance-none cursor-pointer mt-2"
                      disabled
                    />
                    <div className="flex justify-between text-xs text-muted-foreground mt-1">
                      <span>Conservative</span>
                      <span>Aggressive</span>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium">Optimization Target</h4>
                    <div className="mt-2 space-y-2">
                      <div className="flex items-center">
                        <input id="max-sharpe" type="radio" name="target" defaultChecked disabled />
                        <label htmlFor="max-sharpe" className="ml-2">Maximum Sharpe Ratio</label>
                      </div>
                      <div className="flex items-center">
                        <input id="min-vol" type="radio" name="target" disabled />
                        <label htmlFor="min-vol" className="ml-2">Minimum Volatility</label>
                      </div>
                      <div className="flex items-center">
                        <input id="max-ret" type="radio" name="target" disabled />
                        <label htmlFor="max-ret" className="ml-2">Maximum Return</label>
                      </div>
                    </div>
                  </div>

                  <Button variant="outline" size="sm" className="w-full mt-2" disabled>
                    Run Optimization
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  );
}
