
import { Loader2 } from "lucide-react";
import { HeaderBar } from "@/components/HeaderBar";

export function LoadingState() {
  return (
    <div className="flex-1 flex flex-col min-w-0 bg-background">
      <HeaderBar title="Dashboard" description="Professional cryptocurrency analytics platform" />
      <div className="flex-1 flex items-center justify-center bg-background">
        <div className="text-center space-y-4">
          <Loader2 className="h-12 w-12 animate-spin text-primary mx-auto" />
          <p className="text-lg text-muted-foreground">Loading market data...</p>
        </div>
      </div>
    </div>
  );
}
