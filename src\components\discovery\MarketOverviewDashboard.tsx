
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  Activity,
  Brain,
  Gauge
} from "lucide-react";
import { MarketOverview } from "@/services/api/discovery/enhancedDiscoveryService";

interface MarketOverviewDashboardProps {
  data: MarketOverview;
  insights: string;
  isLoading: boolean;
}

export default function MarketOverviewDashboard({ data, insights, isLoading }: MarketOverviewDashboardProps) {
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {Array(4).fill(0).map((_, i) => (
          <div key={i} className="animate-pulse h-32 bg-muted rounded-lg"></div>
        ))}
      </div>
    );
  }

  const formatLargeNumber = (num: number) => {
    if (num >= 1e12) return `$${(num / 1e12).toFixed(2)}T`;
    if (num >= 1e9) return `$${(num / 1e9).toFixed(1)}B`;
    if (num >= 1e6) return `$${(num / 1e6).toFixed(1)}M`;
    return `$${num.toFixed(2)}`;
  };

  const getFearGreedColor = (index: number) => {
    if (index >= 75) return 'text-crypto-positive';
    if (index >= 55) return 'text-chart-1';
    if (index >= 45) return 'text-chart-3';
    if (index >= 25) return 'text-chart-4';
    return 'text-destructive';
  };

  const getFearGreedLabel = (index: number) => {
    if (index >= 75) return 'Extreme Greed';
    if (index >= 55) return 'Greed';
    if (index >= 45) return 'Neutral';
    if (index >= 25) return 'Fear';
    return 'Extreme Fear';
  };

  return (
    <div className="space-y-6">
      {/* Market Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Market Cap</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatLargeNumber(data.totalMarketCap)}</div>
            <div className="flex items-center text-xs text-muted-foreground">
              {data.marketCapChange24h >= 0 ? (
                <TrendingUp className="h-3 w-3 mr-1 text-green-500" />
              ) : (
                <TrendingDown className="h-3 w-3 mr-1 text-red-500" />
              )}
              <span className={data.marketCapChange24h >= 0 ? 'text-green-600' : 'text-red-600'}>
                {data.marketCapChange24h >= 0 ? '+' : ''}{data.marketCapChange24h.toFixed(2)}%
              </span>
              <span className="ml-1">from yesterday</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">24h Volume</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatLargeNumber(data.totalVolume)}</div>
            <div className="flex items-center text-xs text-muted-foreground">
              {data.volumeChange24h >= 0 ? (
                <TrendingUp className="h-3 w-3 mr-1 text-green-500" />
              ) : (
                <TrendingDown className="h-3 w-3 mr-1 text-red-500" />
              )}
              <span className={data.volumeChange24h >= 0 ? 'text-green-600' : 'text-red-600'}>
                {data.volumeChange24h >= 0 ? '+' : ''}{data.volumeChange24h.toFixed(1)}%
              </span>
              <span className="ml-1">from yesterday</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Fear & Greed</CardTitle>
            <Gauge className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.fearGreedIndex}</div>
            <Badge className={getFearGreedColor(data.fearGreedIndex)} variant="outline">
              {getFearGreedLabel(data.fearGreedIndex)}
            </Badge>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">BTC Dominance</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.dominance.btc.toFixed(1)}%</div>
            <div className="space-y-1">
              <Progress value={data.dominance.btc} className="h-2" />
              <p className="text-xs text-muted-foreground">
                ETH: {data.dominance.eth.toFixed(1)}%
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* AI Insights */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5 text-purple-500" />
            AI Market Insights
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm leading-relaxed">{insights}</p>
        </CardContent>
      </Card>

      {/* Top Movers */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-green-600">
              <TrendingUp className="h-5 w-5" />
              Top Gainers (24h)
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {data.topGainers.map((coin, index) => (
                <div key={coin.id} className="flex items-center justify-between p-2 rounded-lg bg-muted/50">
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium">#{index + 1}</span>
                    {coin.image && (
                      <img src={coin.image} alt={coin.name} className="w-5 h-5 rounded-full" />
                    )}
                    <span className="font-medium">{coin.symbol?.toUpperCase()}</span>
                    <span className="text-sm text-muted-foreground">{coin.name}</span>
                  </div>
                  <Badge className="bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-100">
                    +{coin.price_change_percentage_24h.toFixed(1)}%
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-red-600">
              <TrendingDown className="h-5 w-5" />
              Top Losers (24h)
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {data.topLosers.map((coin, index) => (
                <div key={coin.id} className="flex items-center justify-between p-2 rounded-lg bg-muted/50">
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium">#{index + 1}</span>
                    {coin.image && (
                      <img src={coin.image} alt={coin.name} className="w-5 h-5 rounded-full" />
                    )}
                    <span className="font-medium">{coin.symbol?.toUpperCase()}</span>
                    <span className="text-sm text-muted-foreground">{coin.name}</span>
                  </div>
                  <Badge className="bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-100">
                    {coin.price_change_percentage_24h.toFixed(1)}%
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
