
import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from '@/components/ui/separator';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import {
  ArrowRight,
  Shield,
  AlertCircle,
  CheckCircle,
  XCircle,
  Key,
  Lock,
  Fingerprint,
  FileWarning,
  Eye,
  EyeOff,
  BookOpen
} from 'lucide-react';

export function WalletSecurity({ loading = false }: { loading?: boolean }) {
  const [activeTab, setActiveTab] = useState("wallets");
  const [phishingTestStarted, setPhishingTestStarted] = useState(false);
  const [phishingStep, setPhishingStep] = useState(0);
  const [phishingScore, setPhishingScore] = useState(0);
  const [phishingComplete, setPhishingComplete] = useState(false);
  const [quizStarted, setQuizStarted] = useState(false);
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [score, setScore] = useState(0);
  const [quizCompleted, setQuizCompleted] = useState(false);

  // Quiz questions
  const quizQuestions = [
    {
      question: "What is the primary advantage of a hardware wallet over a software wallet?",
      options: [
        "They're typically free to use",
        "Private keys remain offline and isolated from internet-connected devices",
        "They process transactions faster",
        "They automatically backup your funds"
      ],
      correctAnswer: 1,
      explanation: "Hardware wallets keep your private keys isolated from the internet, making them much more resistant to online attacks. Even if used on a compromised computer, your keys remain secure."
    },
    {
      question: "What is a seed phrase?",
      options: [
        "A password used to login to a cryptocurrency exchange",
        "A series of random words that can recover your wallet and funds",
        "A security question to reset your wallet password",
        "A verification code for two-factor authentication"
      ],
      correctAnswer: 1,
      explanation: "A seed phrase (or recovery phrase) typically consists of 12-24 words that can regenerate all the private keys for your wallet. It serves as a backup to restore your wallet if lost or damaged."
    },
    {
      question: "What is the safest way to store your seed phrase?",
      options: [
        "In a password manager",
        "As a screenshot on your phone",
        "Written on paper and stored in multiple secure locations",
        "In a text file on your computer"
      ],
      correctAnswer: 2,
      explanation: "Physical storage methods disconnected from the internet are safest. Writing your seed phrase on paper and storing it in secure, preferably multiple locations (like a safe) is recommended practice."
    },
    {
      question: "What does multisig (multi-signature) wallet security mean?",
      options: [
        "Using multiple passwords for one wallet",
        "Requiring signatures from multiple private keys to authorize a transaction",
        "Having multiple copies of the same wallet on different devices",
        "Signing in to your wallet from multiple devices simultaneously"
      ],
      correctAnswer: 1,
      explanation: "Multi-signature wallets require two or more private keys to authorize a transaction, similar to requiring multiple keys to open a safe deposit box. This adds an extra layer of security."
    },
    {
      question: "Which of these is a common sign of a phishing attempt?",
      options: [
        "An email asking you to update your wallet through an official website",
        "A message offering free cryptocurrency for wallet verification",
        "A request to update your wallet software from within the wallet itself",
        "A hardware wallet manufacturer's email about a security patch"
      ],
      correctAnswer: 1,
      explanation: "Offers of free cryptocurrency in exchange for wallet verification or other information are classic phishing techniques. Legitimate companies never ask for your private keys or seed phrase and don't offer free crypto for verification."
    }
  ];

  // Phishing simulation scenarios
  const phishingScenarios = [
    {
      title: "Email Alert",
      content: "You receive an email that appears to be from Metamask with the subject 'URGENT: Security Breach - Action Required'. It states that your wallet needs to be verified immediately due to suspicious activity.",
      options: [
        "Click the provided link and enter your seed phrase to verify ownership",
        "Ignore the email and check official Metamask channels for security announcements",
        "Reply to the email asking for more information",
        "Forward the email to friends to warn them"
      ],
      correctAnswer: 1,
      explanation: "Legitimate wallet providers will never ask for your seed phrase. Security announcements should always be verified through official channels, not email links."
    },
    {
      title: "Direct Message",
      content: "You receive a direct message on Discord from 'Support_Team' offering to help troubleshoot a wallet issue. They send a link to a site that looks like your wallet's interface but the URL is wallet-connect-verify.io.",
      options: [
        "Click the link and connect your wallet to get support",
        "Ask them for official verification before proceeding",
        "Block the user and report them to Discord moderators",
        "Give them your wallet address so they can investigate"
      ],
      correctAnswer: 2,
      explanation: "Legitimate support teams don't initiate direct messages on Discord. The suspicious URL is a clear sign of a phishing attempt. Always block and report suspected scammers."
    },
    {
      title: "Airdrop Offer",
      content: "A website announces a new token airdrop. To claim it, you need to connect your wallet and sign a transaction to 'verify your wallet address'.",
      options: [
        "Connect your wallet and sign the transaction to get free tokens",
        "Share the airdrop with friends first, then participate together",
        "Check the transaction details carefully before signing",
        "Avoid the site, as legitimate airdrops don't require signing unusual transactions"
      ],
      correctAnswer: 3,
      explanation: "Scam airdrops often ask you to sign malicious transactions that can drain your wallet. Legitimate airdrops typically require simple address verification or occur automatically."
    }
  ];

  const handleQuizStart = () => {
    setQuizStarted(true);
    setCurrentQuestion(0);
    setScore(0);
    setQuizCompleted(false);
  };

  const handlePhishingTest = () => {
    setPhishingTestStarted(true);
    setPhishingStep(0);
    setPhishingScore(0);
    setPhishingComplete(false);
  };

  const handlePhishingAnswer = (selectedAnswer: number) => {
    if (selectedAnswer === phishingScenarios[phishingStep].correctAnswer) {
      setPhishingScore(phishingScore + 1);
      toast.success("Correct! You spotted the phishing attempt.");
    } else {
      toast.error("That was a phishing attempt! Be more careful next time.");
    }

    if (phishingStep < phishingScenarios.length - 1) {
      setTimeout(() => {
        setPhishingStep(phishingStep + 1);
      }, 1500);
    } else {
      setTimeout(() => {
        setPhishingComplete(true);
      }, 1500);
    }
  };

  const handleAnswerSubmit = (selectedAnswer: number) => {
    const isCorrect = selectedAnswer === quizQuestions[currentQuestion].correctAnswer;

    if (isCorrect) {
      setScore(score + 1);
      toast.success("Correct answer!");
    } else {
      toast.error("Not quite right!");
    }

    // Move to next question or end quiz
    if (currentQuestion < quizQuestions.length - 1) {
      setTimeout(() => {
        setCurrentQuestion(currentQuestion + 1);
      }, 1500);
    } else {
      setTimeout(() => {
        setQuizCompleted(true);
      }, 1500);
    }
  };

  if (loading) {
    return (
      <Card className="animate-pulse">
        <CardHeader className="pb-2">
          <div className="h-5 w-3/4 bg-muted rounded mb-1"></div>
          <div className="h-4 w-full bg-muted rounded"></div>
        </CardHeader>
        <CardContent>
          <div className="h-[400px] bg-muted rounded mb-2"></div>
        </CardContent>
      </Card>
    );
  }

  // Quiz component
  if (quizStarted) {
    return (
      <Card className="max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle>Wallet Security Quiz</CardTitle>
          <CardDescription>
            {!quizCompleted
              ? `Question ${currentQuestion + 1} of ${quizQuestions.length}`
              : "Quiz Completed"}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {!quizCompleted ? (
            <div className="space-y-4">
              <div className="text-xl font-medium">{quizQuestions[currentQuestion].question}</div>
              <div className="space-y-2">
                {quizQuestions[currentQuestion].options.map((option, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    className="w-full justify-start text-left h-auto py-3 px-4"
                    onClick={() => handleAnswerSubmit(index)}
                  >
                    {option}
                  </Button>
                ))}
              </div>
              <div className="pt-4">
                <p className="text-muted-foreground text-sm">
                  Select the best answer. Your score will be calculated at the end.
                </p>
              </div>
            </div>
          ) : (
            <div className="space-y-6 py-4">
              <div className="text-center">
                <div className="text-3xl font-bold mb-2">
                  Your Score: {score}/{quizQuestions.length}
                </div>
                <div className="text-muted-foreground">
                  {score === quizQuestions.length ?
                    "Perfect! You're well-prepared to protect your crypto assets!" :
                    score >= quizQuestions.length / 2 ?
                    "Good job! You have a solid understanding of wallet security, but there's still room to improve." :
                    "You should review wallet security best practices more carefully. Your crypto depends on it!"}
                </div>
              </div>

              <div className="space-y-4 mt-6">
                <h3 className="font-medium text-lg">Review:</h3>
                {quizQuestions.map((q, idx) => (
                  <div key={idx} className="p-4 border rounded-md">
                    <div className="flex gap-2">
                      <div className="mt-1">
                        {idx < currentQuestion ? (
                          score >= idx + 1 ? <CheckCircle className="text-crypto-positive" size={18} /> : <XCircle className="text-crypto-negative" size={18} />
                        ) : null}
                      </div>
                      <div>
                        <p className="font-medium">{q.question}</p>
                        <p className="text-sm text-muted-foreground mt-1">Correct answer: {q.options[q.correctAnswer]}</p>
                        <p className="text-sm mt-1">{q.explanation}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              <div className="flex justify-center pt-4">
                <Button onClick={() => setQuizStarted(false)} className="mr-2">
                  Return to Wallet Security
                </Button>
                <Button onClick={handleQuizStart} variant="outline">
                  Retry Quiz
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    );
  }

  // Phishing test component
  if (phishingTestStarted) {
    return (
      <Card className="max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle>Phishing Attack Simulator</CardTitle>
          <CardDescription>
            {!phishingComplete
              ? `Scenario ${phishingStep + 1} of ${phishingScenarios.length}: Test your ability to identify phishing attempts`
              : "Simulation Completed"}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {!phishingComplete ? (
            <div className="space-y-4">
              <Alert variant="destructive" className="border border-red-200 bg-red-50">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Training Exercise</AlertTitle>
                <AlertDescription>
                  This is a safe simulation. No real credentials are being requested. Practice identifying and avoiding scams.
                </AlertDescription>
              </Alert>

              <div className="p-6 border rounded-md">
                <h3 className="text-xl font-medium mb-2">{phishingScenarios[phishingStep].title}</h3>
                <p className="text-gray-700 mb-6">{phishingScenarios[phishingStep].content}</p>

                <h4 className="font-medium mb-2">How would you respond?</h4>
                <div className="space-y-2">
                  {phishingScenarios[phishingStep].options.map((option, index) => (
                    <Button
                      key={index}
                      variant="outline"
                      className="w-full justify-start text-left h-auto py-3 px-4"
                      onClick={() => handlePhishingAnswer(index)}
                    >
                      {option}
                    </Button>
                  ))}
                </div>
              </div>
            </div>
          ) : (
            <div className="space-y-6 py-4">
              <div className="text-center">
                <div className="text-3xl font-bold mb-2">
                  Your Score: {phishingScore}/{phishingScenarios.length}
                </div>
                <div className="text-muted-foreground">
                  {phishingScore === phishingScenarios.length ?
                    "Excellent! You successfully identified all phishing attempts." :
                    phishingScore >= phishingScenarios.length / 2 ?
                    "Good awareness, but stay vigilant. Scammers are constantly evolving their tactics." :
                    "You need to improve your phishing detection skills. Your crypto could be at risk!"}
                </div>
              </div>

              <div className="space-y-4 mt-6">
                <h3 className="font-medium text-lg">Review:</h3>
                {phishingScenarios.map((s, idx) => (
                  <div key={idx} className="p-4 border rounded-md">
                    <h4 className="font-medium">{s.title}</h4>
                    <p className="text-sm mt-1 mb-2">{s.content}</p>
                    <div className="bg-muted/50 p-3 rounded-md">
                      <p className="text-sm font-medium">Correct approach:</p>
                      <p className="text-sm">{s.options[s.correctAnswer]}</p>
                      <p className="text-sm mt-2 text-muted-foreground">{s.explanation}</p>
                    </div>
                  </div>
                ))}
              </div>

              <div className="flex justify-center pt-4">
                <Button onClick={() => setPhishingTestStarted(false)} className="mr-2">
                  Return to Wallet Security
                </Button>
                <Button onClick={handlePhishingTest} variant="outline">
                  Retry Simulation
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Wallet Security 101: Safeguarding Your Assets</CardTitle>
          <CardDescription>Learn essential practices to protect your cryptocurrency holdings</CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid grid-cols-4 mb-6">
              <TabsTrigger value="wallets">Wallet Types</TabsTrigger>
              <TabsTrigger value="seedphrase">Seed Phrases</TabsTrigger>
              <TabsTrigger value="advanced">Advanced Security</TabsTrigger>
              <TabsTrigger value="phishing">Phishing Prevention</TabsTrigger>
            </TabsList>

            <TabsContent value="wallets" className="space-y-6">
              <section>
                <div className="flex items-center gap-2">
                  <Shield className="text-primary" size={24} />
                  <h3 className="text-lg font-medium">Understanding Wallet Types</h3>
                </div>
                <p className="text-muted-foreground mt-2">
                  Cryptocurrency wallets come in several forms, each with different security characteristics and
                  trade-offs between accessibility and safety. Understanding these differences is the first step
                  to securing your digital assets.
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                  <Card>
                    <CardHeader>
                      <div className="flex justify-between items-center">
                        <CardTitle className="text-base">Hot Wallets</CardTitle>
                        <Badge variant="outline" className="bg-yellow-50 text-yellow-800">Moderate Security</Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground mb-4">
                        Hot wallets are connected to the internet, making them convenient for frequent transactions
                        but more vulnerable to online threats.
                      </p>

                      <div className="space-y-3">
                        <div>
                          <h5 className="text-sm font-medium">Types of Hot Wallets:</h5>
                          <ul className="list-disc pl-5 text-sm text-muted-foreground">
                            <li><span className="font-medium">Web Wallets:</span> Browser-based, accessible from any device with internet</li>
                            <li><span className="font-medium">Mobile Wallets:</span> Apps on smartphones, convenient for on-the-go transactions</li>
                            <li><span className="font-medium">Desktop Wallets:</span> Software installed on your computer</li>
                            <li><span className="font-medium">Exchange Wallets:</span> Custodial accounts managed by cryptocurrency exchanges</li>
                          </ul>
                        </div>

                        <div>
                          <h5 className="text-sm font-medium">Best Practices:</h5>
                          <ul className="list-disc pl-5 text-sm text-muted-foreground">
                            <li>Use for small amounts intended for regular transactions</li>
                            <li>Enable all security features (2FA, biometrics)</li>
                            <li>Regularly update wallet software</li>
                            <li>Use strong, unique passwords</li>
                            <li>Verify transactions before confirming</li>
                          </ul>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <div className="flex justify-between items-center">
                        <CardTitle className="text-base">Cold Wallets</CardTitle>
                        <Badge variant="outline" className="bg-green-50 text-green-800">High Security</Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground mb-4">
                        Cold wallets remain offline, keeping private keys isolated from internet-connected devices
                        and significantly reducing vulnerability to hacking attempts.
                      </p>

                      <div className="space-y-3">
                        <div>
                          <h5 className="text-sm font-medium">Types of Cold Wallets:</h5>
                          <ul className="list-disc pl-5 text-sm text-muted-foreground">
                            <li><span className="font-medium">Hardware Wallets:</span> Physical devices specifically designed to securely store private keys</li>
                            <li><span className="font-medium">Paper Wallets:</span> Physical documents containing printed keys or QR codes</li>
                            <li><span className="font-medium">Steel/Metal Wallets:</span> Durable physical backups resistant to fire and water damage</li>
                            <li><span className="font-medium">Air-gapped Devices:</span> Computers that have never connected to the internet</li>
                          </ul>
                        </div>

                        <div>
                          <h5 className="text-sm font-medium">Best Practices:</h5>
                          <ul className="list-disc pl-5 text-sm text-muted-foreground">
                            <li>Use for long-term storage and large amounts</li>
                            <li>Purchase hardware wallets directly from manufacturers</li>
                            <li>Keep in secure, private location</li>
                            <li>Consider redundant backup solutions</li>
                            <li>Test recovery process before large deposits</li>
                          </ul>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <Alert className="mt-6 border-blue-200 bg-blue-50">
                  <AlertCircle className="h-4 w-4 text-blue-600" />
                  <AlertTitle className="text-blue-700">Security Recommendation</AlertTitle>
                  <AlertDescription className="text-blue-600">
                    For optimal security, consider a two-wallet strategy: a cold wallet for long-term storage of the majority of your funds,
                    and a hot wallet with small amounts for everyday transactions and trading.
                  </AlertDescription>
                </Alert>
              </section>

              <Separator />

              <section>
                <h3 className="text-lg font-medium mb-2">Wallet Security Comparison</h3>

                <div className="overflow-x-auto">
                  <table className="min-w-full border-collapse">
                    <thead>
                      <tr className="border-b">
                        <th className="py-2 px-4 text-left">Wallet Type</th>
                        <th className="py-2 px-4 text-left">Security Level</th>
                        <th className="py-2 px-4 text-left">Convenience</th>
                        <th className="py-2 px-4 text-left">Ideal Use Case</th>
                        <th className="py-2 px-4 text-left">Key Risks</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr className="border-b">
                        <td className="py-3 px-4 font-medium">Hardware Wallet</td>
                        <td className="py-3 px-4">Very High</td>
                        <td className="py-3 px-4">Medium</td>
                        <td className="py-3 px-4">Long-term holdings, large amounts</td>
                        <td className="py-3 px-4">Physical damage, loss, theft of device</td>
                      </tr>
                      <tr className="border-b">
                        <td className="py-3 px-4 font-medium">Desktop Wallet</td>
                        <td className="py-3 px-4">Medium</td>
                        <td className="py-3 px-4">Medium</td>
                        <td className="py-3 px-4">Regular trading, medium amounts</td>
                        <td className="py-3 px-4">Malware, phishing, system compromise</td>
                      </tr>
                      <tr className="border-b">
                        <td className="py-3 px-4 font-medium">Mobile Wallet</td>
                        <td className="py-3 px-4">Medium</td>
                        <td className="py-3 px-4">High</td>
                        <td className="py-3 px-4">Daily transactions, small amounts</td>
                        <td className="py-3 px-4">Phone theft, malicious apps</td>
                      </tr>
                      <tr className="border-b">
                        <td className="py-3 px-4 font-medium">Web Wallet</td>
                        <td className="py-3 px-4">Low to Medium</td>
                        <td className="py-3 px-4">Very High</td>
                        <td className="py-3 px-4">Quick access, minimal amounts</td>
                        <td className="py-3 px-4">Phishing, browser vulnerabilities</td>
                      </tr>
                      <tr className="border-b">
                        <td className="py-3 px-4 font-medium">Exchange Wallet</td>
                        <td className="py-3 px-4">Low</td>
                        <td className="py-3 px-4">Very High</td>
                        <td className="py-3 px-4">Active trading</td>
                        <td className="py-3 px-4">Exchange hacks, exit scams</td>
                      </tr>
                      <tr className="border-b">
                        <td className="py-3 px-4 font-medium">Paper Wallet</td>
                        <td className="py-3 px-4">High</td>
                        <td className="py-3 px-4">Low</td>
                        <td className="py-3 px-4">Long-term storage, backup</td>
                        <td className="py-3 px-4">Physical damage, loss, readability</td>
                      </tr>
                    </tbody>
                  </table>
                </div>

                <div className="mt-6">
                  <h4 className="font-medium mb-2">Popular Hardware Wallet Options</h4>
                  <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                    <div className="p-4 border rounded-md">
                      <h5 className="font-medium mb-1">Ledger</h5>
                      <p className="text-sm text-muted-foreground">Secure element chip, wide coin support, companion app</p>
                    </div>
                    <div className="p-4 border rounded-md">
                      <h5 className="font-medium mb-1">Trezor</h5>
                      <p className="text-sm text-muted-foreground">Open-source firmware, easy interface, password manager</p>
                    </div>
                    <div className="p-4 border rounded-md">
                      <h5 className="font-medium mb-1">KeepKey</h5>
                      <p className="text-sm text-muted-foreground">Large display, simple design, exchange integration</p>
                    </div>
                  </div>
                </div>
              </section>

              <div className="flex justify-center mt-6">
                <Button onClick={() => setActiveTab("seedphrase")}>
                  Learn About Seed Phrases <ArrowRight className="ml-1 h-4 w-4" />
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="seedphrase" className="space-y-6">
              <section>
                <div className="flex items-center gap-2">
                  <Key className="text-primary" size={24} />
                  <h3 className="text-lg font-medium">Seed Phrases: The Master Key to Your Crypto</h3>
                </div>
                <p className="text-muted-foreground mt-2">
                  A seed phrase (also called a recovery phrase or mnemonic phrase) is a series of words that serves as a
                  master key to your cryptocurrency wallet. It's the most critical piece of information to protect, as
                  anyone with access to your seed phrase has complete control over your funds.
                </p>

                <Alert className="mt-4 border-red-200 bg-red-50">
                  <AlertCircle className="h-4 w-4 text-red-600" />
                  <AlertTitle className="text-red-700">Critical Warning</AlertTitle>
                  <AlertDescription className="text-red-600">
                    Never share your seed phrase with anyone, enter it on websites, or store it digitally in plain text.
                    Legitimate crypto companies, wallet providers, or support staff will <strong>never</strong> ask for your seed phrase.
                  </AlertDescription>
                </Alert>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
                  <div className="space-y-4">
                    <h4 className="font-medium">What Is a Seed Phrase?</h4>
                    <p className="text-sm text-muted-foreground">
                      A seed phrase typically consists of 12, 18, or 24 randomly generated words from a standardized list.
                      These words represent your private keys in a human-readable format and follow the BIP-39 standard
                      across most wallets.
                    </p>

                    <div className="bg-muted/50 p-4 rounded-md">
                      <h5 className="text-sm font-medium mb-2">Example Seed Phrase (DO NOT USE):</h5>
                      <p className="font-mono text-sm">
                        abandon ability able about above absent absorb abstract absurd abuse access accident
                      </p>
                      <p className="text-xs text-muted-foreground mt-2">
                        ⚠️ This is just an example. Never use a seed phrase you've seen online.
                      </p>
                    </div>

                    <h4 className="font-medium pt-2">How Seed Phrases Work</h4>
                    <p className="text-sm text-muted-foreground">
                      When you create a wallet, the seed phrase is generated from random entropy and used to derive all the
                      private keys for your cryptocurrency addresses. If you lose access to your wallet due to device failure,
                      the seed phrase can regenerate all your private keys and restore your funds on a new device.
                    </p>
                  </div>

                  <div className="border rounded-md p-6 space-y-4">
                    <h4 className="font-medium">Secure Seed Phrase Storage Methods</h4>

                    <div className="space-y-3">
                      <div className="flex items-start gap-2">
                        <div className="w-5 h-5 rounded-full bg-green-100 text-green-800 flex items-center justify-center mt-0.5">1</div>
                        <div>
                          <h5 className="text-sm font-medium">Metal Backup</h5>
                          <p className="text-sm text-muted-foreground">
                            Engrave or stamp your seed phrase on metal plates resistant to fire, water, and corrosion.
                            Products like Cryptosteel, Billfodl, or simple stainless steel plates offer durable storage.
                          </p>
                        </div>
                      </div>

                      <div className="flex items-start gap-2">
                        <div className="w-5 h-5 rounded-full bg-green-100 text-green-800 flex items-center justify-center mt-0.5">2</div>
                        <div>
                          <h5 className="text-sm font-medium">Paper in Multiple Locations</h5>
                          <p className="text-sm text-muted-foreground">
                            Write your seed phrase on paper and store copies in multiple secure locations like safes,
                            safety deposit boxes, or with trusted family members. Consider laminating for water resistance.
                          </p>
                        </div>
                      </div>

                      <div className="flex items-start gap-2">
                        <div className="w-5 h-5 rounded-full bg-green-100 text-green-800 flex items-center justify-center mt-0.5">3</div>
                        <div>
                          <h5 className="text-sm font-medium">Distributed Storage</h5>
                          <p className="text-sm text-muted-foreground">
                            Split your seed phrase into multiple parts and store them in different locations,
                            requiring multiple parts to reconstruct the full phrase. For example, store words 1-8 in
                            one location and 9-16 in another.
                          </p>
                        </div>
                      </div>

                      <div className="flex items-start gap-2">
                        <div className="w-5 h-5 rounded-full bg-green-100 text-green-800 flex items-center justify-center mt-0.5">4</div>
                        <div>
                          <h5 className="text-sm font-medium">Encrypted Digital Backup</h5>
                          <p className="text-sm text-muted-foreground">
                            As a supplementary method, you can encrypt your seed phrase using strong encryption and store
                            it on offline media. This should never be your only backup method.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="mt-6">
                  <h4 className="font-medium mb-3">Do's and Don'ts of Seed Phrase Security</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <h5 className="text-sm font-medium flex items-center gap-1">
                        <CheckCircle className="text-green-500" size={16} />
                        Do These:
                      </h5>
                      <ul className="list-disc pl-5 text-sm text-muted-foreground space-y-1">
                        <li>Verify you've recorded your seed phrase correctly by testing the recovery process</li>
                        <li>Create multiple backups in different physical locations</li>
                        <li>Consider using a passphrase (25th word) for additional security</li>
                        <li>Keep your seed phrase storage location private</li>
                        <li>Include instructions for your family in estate planning</li>
                      </ul>
                    </div>

                    <div className="space-y-2">
                      <h5 className="text-sm font-medium flex items-center gap-1">
                        <XCircle className="text-red-500" size={16} />
                        Don't Do These:
                      </h5>
                      <ul className="list-disc pl-5 text-sm text-muted-foreground space-y-1">
                        <li>Take a photo of your seed phrase</li>
                        <li>Store it in cloud storage, email, or note-taking apps</li>
                        <li>Share it with anyone, even tech support</li>
                        <li>Enter it on websites or unverified apps</li>
                        <li>Store it without encryption on your computer</li>
                        <li>Use a seed phrase you've seen elsewhere</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </section>

              <Separator />

              <section>
                <h3 className="text-lg font-medium mb-2">Seed Phrase Security Quiz</h3>
                <p className="text-muted-foreground mb-4">
                  Test your understanding of seed phrase security with this quick true/false quiz:
                </p>

                <div className="space-y-4">
                  <div className="p-4 border rounded-md">
                    <p className="font-medium">1. It's safe to store my seed phrase as a screenshot on my phone for easy access.</p>
                    <div className="flex items-center gap-2 mt-2">
                      <span className="text-red-600 font-medium">FALSE</span>
                      <XCircle className="text-red-600" size={16} />
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">
                      Storing your seed phrase digitally, especially as an image on a device that connects to the internet,
                      significantly increases the risk of theft through malware, hacking, or unauthorized access.
                    </p>
                  </div>

                  <div className="p-4 border rounded-md">
                    <p className="font-medium">2. If I lose my hardware wallet, my funds are gone forever.</p>
                    <div className="flex items-center gap-2 mt-2">
                      <span className="text-red-600 font-medium">FALSE</span>
                      <XCircle className="text-red-600" size={16} />
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">
                      As long as you have your seed phrase backed up, you can restore your wallet and access your funds
                      on a new hardware wallet or compatible software wallet.
                    </p>
                  </div>

                  <div className="p-4 border rounded-md">
                    <p className="font-medium">3. Adding a strong passphrase (sometimes called a "25th word") to my seed phrase adds an extra layer of security.</p>
                    <div className="flex items-center gap-2 mt-2">
                      <span className="text-green-600 font-medium">TRUE</span>
                      <CheckCircle className="text-green-600" size={16} />
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">
                      A passphrase adds an additional security layer that must be provided along with your seed phrase
                      to access your wallet. This protects your funds even if someone discovers your seed phrase.
                    </p>
                  </div>
                </div>
              </section>

              <div className="flex justify-center mt-6">
                <Button onClick={() => setActiveTab("advanced")}>
                  Explore Advanced Security <ArrowRight className="ml-1 h-4 w-4" />
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="advanced" className="space-y-6">
              <section>
                <div className="flex items-center gap-2">
                  <Lock className="text-primary" size={24} />
                  <h3 className="text-lg font-medium">Advanced Security Measures</h3>
                </div>
                <p className="text-muted-foreground mt-2">
                  Beyond basic wallet security and seed phrase protection, several advanced techniques can provide
                  additional layers of security for crypto assets, especially for larger holdings or institutional users.
                </p>

                <div className="mt-6 space-y-6">
                  <div className="p-6 border rounded-md">
                    <div className="flex items-center gap-2 mb-3">
                      <Fingerprint className="text-primary" size={20} />
                      <h4 className="font-medium">Multi-Signature Wallets</h4>
                    </div>

                    <p className="text-sm text-muted-foreground mb-4">
                      Multi-signature (multisig) wallets require multiple private keys to authorize a transaction,
                      similar to how a bank vault might require multiple keys to open. This distributed security
                      model ensures that no single point of failure or compromise can result in lost funds.
                    </p>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <h5 className="text-sm font-medium mb-2">How Multisig Works</h5>
                        <p className="text-sm text-muted-foreground">
                          A multisig wallet is configured to require M-of-N signatures to authorize transactions, where:
                        </p>
                        <ul className="list-disc pl-5 text-sm text-muted-foreground mt-1 space-y-1">
                          <li>M = minimum number of signatures required</li>
                          <li>N = total number of possible signers</li>
                        </ul>
                        <p className="text-sm text-muted-foreground mt-2">
                          Common configurations include 2-of-3 (requiring any 2 of 3 possible keys) or 3-of-5,
                          balancing security with practical usability.
                        </p>
                      </div>

                      <div>
                        <h5 className="text-sm font-medium mb-2">Benefits of Multisig</h5>
                        <ul className="list-disc pl-5 text-sm text-muted-foreground space-y-1">
                          <li>Protection against single key compromise</li>
                          <li>Distributes trust among multiple parties</li>
                          <li>Enables organizational controls and governance</li>
                          <li>Creates redundancy if one key is lost</li>
                          <li>Reduces single points of failure</li>
                        </ul>
                      </div>
                    </div>

                    <div className="mt-4 text-sm">
                      <h5 className="font-medium mb-1">Popular Multisig Solutions</h5>
                      <ul className="list-disc pl-5 text-muted-foreground">
                        <li><span className="font-medium">Gnosis Safe:</span> Smart contract wallet for Ethereum and EVM chains</li>
                        <li><span className="font-medium">Electrum:</span> Bitcoin multisig wallet with advanced features</li>
                        <li><span className="font-medium">Casa:</span> Managed multisig solution with advanced key management</li>
                        <li><span className="font-medium">Blockstream Green:</span> User-friendly mobile multisig wallet</li>
                      </ul>
                    </div>
                  </div>

                  <div className="p-6 border rounded-md">
                    <div className="flex items-center gap-2 mb-3">
                      <Eye className="text-primary" size={20} />
                      <h4 className="font-medium">Tiered Storage System</h4>
                    </div>

                    <p className="text-sm text-muted-foreground mb-4">
                      A tiered approach to cryptocurrency storage distributes your assets across multiple wallets
                      with different security levels based on access needs, creating a balance between security and convenience.
                    </p>

                    <div className="space-y-3">
                      <div className="flex items-start gap-2">
                        <div className="w-5 h-5 rounded-full bg-blue-100 text-blue-800 flex items-center justify-center mt-0.5">1</div>
                        <div>
                          <h5 className="text-sm font-medium">Cold Storage (Deep Reserve)</h5>
                          <p className="text-sm text-muted-foreground">
                            80-90% of total holdings stored in air-gapped cold storage, possibly multisig,
                            with seed phrases secured in multiple physical locations. Accessed rarely, only for
                            significant transfers.
                          </p>
                        </div>
                      </div>

                      <div className="flex items-start gap-2">
                        <div className="w-5 h-5 rounded-full bg-blue-100 text-blue-800 flex items-center justify-center mt-0.5">2</div>
                        <div>
                          <h5 className="text-sm font-medium">Hardware Wallet (Savings)</h5>
                          <p className="text-sm text-muted-foreground">
                            10-15% of holdings in hardware wallets for medium-term storage. Accessed monthly
                            for larger planned transactions and portfolio rebalancing.
                          </p>
                        </div>
                      </div>

                      <div className="flex items-start gap-2">
                        <div className="w-5 h-5 rounded-full bg-blue-100 text-blue-800 flex items-center justify-center mt-0.5">3</div>
                        <div>
                          <h5 className="text-sm font-medium">Hot Wallet (Checking)</h5>
                          <p className="text-sm text-muted-foreground">
                            1-5% of holdings in software wallets for regular transactions and active use.
                            Think of this as spending money that's accessible but limited in amount to minimize
                            potential losses.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="p-6 border rounded-md">
                    <div className="flex items-center gap-2 mb-3">
                      <FileWarning className="text-primary" size={20} />
                      <h4 className="font-medium">Advanced Operational Security</h4>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <h5 className="text-sm font-medium mb-2">Device Security</h5>
                        <ul className="list-disc pl-5 text-sm text-muted-foreground space-y-1">
                          <li>Use a dedicated device for high-value crypto transactions</li>
                          <li>Consider Linux-based operating systems for increased security</li>
                          <li>Keep all software up to date, especially wallet applications</li>
                          <li>Use hardware wallets with secure element chips</li>
                          <li>Verify wallet addresses using multiple sources/devices</li>
                        </ul>
                      </div>

                      <div>
                        <h5 className="text-sm font-medium mb-2">Access Control</h5>
                        <ul className="list-disc pl-5 text-sm text-muted-foreground space-y-1">
                          <li>Implement time-locks for large withdrawals</li>
                          <li>Use address whitelisting where available</li>
                          <li>Consider withdrawal limits for exchange accounts</li>
                          <li>Use unique email addresses for cryptocurrency accounts</li>
                          <li>Implement 2FA everywhere, preferably with hardware keys</li>
                        </ul>
                      </div>
                    </div>

                    <div className="mt-4">
                      <h5 className="text-sm font-medium mb-2">Inheritance Planning</h5>
                      <p className="text-sm text-muted-foreground">
                        Create a secure plan for asset inheritance that balances security during your lifetime with
                        accessibility for designated heirs:
                      </p>
                      <ul className="list-disc pl-5 text-sm text-muted-foreground mt-1 space-y-1">
                        <li>Document wallet locations and access instructions</li>
                        <li>Consider split custody arrangements with trusted attorneys</li>
                        <li>Investigate purpose-built inheritance solutions (Casa, Unchained Capital)</li>
                        <li>Test your inheritance plan to ensure it works as expected</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </section>

              <Separator />

              <section>
                <h3 className="text-lg font-medium mb-2">Advanced Security Best Practices</h3>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="p-4 border rounded-md">
                    <h4 className="font-medium mb-2">Transaction Verification</h4>
                    <ul className="list-disc pl-5 text-sm text-muted-foreground space-y-1">
                      <li>Always verify the full receiving address, not just the first and last characters</li>
                      <li>Send a small test transaction before large transfers</li>
                      <li>Use hardware wallet screen to confirm destination addresses</li>
                      <li>Verify transaction details on multiple devices when possible</li>
                    </ul>
                  </div>

                  <div className="p-4 border rounded-md">
                    <h4 className="font-medium mb-2">Privacy Considerations</h4>
                    <ul className="list-disc pl-5 text-sm text-muted-foreground space-y-1">
                      <li>Use unique addresses for each transaction when possible</li>
                      <li>Consider privacy coins for sensitive transactions</li>
                      <li>Use VPN or Tor when accessing wallet services</li>
                      <li>Never discuss holdings amounts on public forums</li>
                      <li>Be aware of blockchain analytics and tracking</li>
                    </ul>
                  </div>

                  <div className="p-4 border rounded-md">
                    <h4 className="font-medium mb-2">Regular Security Audits</h4>
                    <ul className="list-disc pl-5 text-sm text-muted-foreground space-y-1">
                      <li>Periodically review wallet access and security</li>
                      <li>Test recovery procedures annually</li>
                      <li>Update emergency access plans as needed</li>
                      <li>Review permissions granted to connected applications</li>
                      <li>Check for suspicious transaction history</li>
                    </ul>
                  </div>
                </div>

                <Alert className="mt-6 border-amber-200 bg-amber-50">
                  <AlertCircle className="h-4 w-4 text-amber-600" />
                  <AlertTitle className="text-amber-700">Security Is A Process</AlertTitle>
                  <AlertDescription className="text-amber-600">
                    Cryptocurrency security is not a one-time setup but an ongoing process. The threat landscape
                    evolves constantly, requiring regular reviews and updates to your security practices.
                  </AlertDescription>
                </Alert>
              </section>

              <div className="flex justify-center mt-6">
                <Button onClick={() => setActiveTab("phishing")}>
                  Learn About Phishing Prevention <ArrowRight className="ml-1 h-4 w-4" />
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="phishing" className="space-y-6">
              <section>
                <div className="flex items-center gap-2">
                  <EyeOff className="text-primary" size={24} />
                  <h3 className="text-lg font-medium">Phishing Prevention: Protecting Against Social Engineering</h3>
                </div>
                <p className="text-muted-foreground mt-2">
                  Phishing attacks are the most common way cryptocurrency users lose their funds. These attacks
                  use social engineering to trick you into revealing sensitive information or taking actions that
                  compromise your wallet security.
                </p>

                <Alert className="mt-4 border-red-200 bg-red-50">
                  <AlertCircle className="h-4 w-4 text-red-600" />
                  <AlertTitle className="text-red-700">Important Rule</AlertTitle>
                  <AlertDescription className="text-red-600">
                    No legitimate company, wallet provider, exchange, or project will ever ask for your seed phrase,
                    private keys, or passwords. Anyone requesting this information is attempting to steal your funds.
                  </AlertDescription>
                </Alert>

                <div className="mt-6">
                  <h4 className="font-medium mb-3">Common Crypto Phishing Techniques</h4>

                  <div className="space-y-4">
                    <div className="p-4 border rounded-md">
                      <h5 className="font-medium mb-2">1. Fake Websites & Applications</h5>
                      <p className="text-sm text-muted-foreground">
                        Scammers create near-perfect replicas of legitimate wallet websites, exchanges, or DeFi platforms
                        with slightly altered URLs. When you enter your credentials or connect your wallet, they gain access
                        to your funds.
                      </p>
                      <div className="bg-muted p-3 rounded-md mt-2">
                        <h6 className="text-sm font-medium">Prevention Tips:</h6>
                        <ul className="list-disc pl-5 text-sm text-muted-foreground mt-1">
                          <li>Bookmark official websites instead of using search engines</li>
                          <li>Verify URLs carefully (metamask.io is legitimate, metamask-wallet.io is likely a scam)</li>
                          <li>Use hardware wallets that display and verify transaction details</li>
                          <li>Install browser extensions that alert you to known phishing sites</li>
                        </ul>
                      </div>
                    </div>

                    <div className="p-4 border rounded-md">
                      <h5 className="font-medium mb-2">2. Support Scams</h5>
                      <p className="text-sm text-muted-foreground">
                        Scammers pose as customer support for wallet providers or exchanges, often responding to help
                        requests on social media or through direct messages. They typically direct users to fake websites
                        or request sensitive information to "resolve" issues.
                      </p>
                      <div className="bg-muted p-3 rounded-md mt-2">
                        <h6 className="text-sm font-medium">Prevention Tips:</h6>
                        <ul className="list-disc pl-5 text-sm text-muted-foreground mt-1">
                          <li>Only contact support through official channels (company website or official app)</li>
                          <li>Real support will never DM you first on social media</li>
                          <li>No legitimate support agent will ask for your seed phrase or private keys</li>
                          <li>Be skeptical of anyone contacting you unsolicited about wallet issues</li>
                        </ul>
                      </div>
                    </div>

                    <div className="p-4 border rounded-md">
                      <h5 className="font-medium mb-2">3. Airdrop Scams</h5>
                      <p className="text-sm text-muted-foreground">
                        Fraudsters promise free tokens (airdrops) that require you to connect your wallet to a malicious
                        site or sign a transaction that actually grants them access to your funds.
                      </p>
                      <div className="bg-muted p-3 rounded-md mt-2">
                        <h6 className="text-sm font-medium">Prevention Tips:</h6>
                        <ul className="list-disc pl-5 text-sm text-muted-foreground mt-1">
                          <li>Be skeptical of unexpected or too-good-to-be-true airdrops</li>
                          <li>Always verify airdrops through official project channels</li>
                          <li>Read transaction details carefully before signing anything</li>
                          <li>Use a separate wallet with minimal funds for interacting with new projects</li>
                        </ul>
                      </div>
                    </div>

                    <div className="p-4 border rounded-md">
                      <h5 className="font-medium mb-2">4. Malicious Browser Extensions & Apps</h5>
                      <p className="text-sm text-muted-foreground">
                        Fake wallet extensions, portfolio trackers, or trading tools may contain malware designed
                        to steal your crypto assets or monitor your activity.
                      </p>
                      <div className="bg-muted p-3 rounded-md mt-2">
                        <h6 className="text-sm font-medium">Prevention Tips:</h6>
                        <ul className="list-disc pl-5 text-sm text-muted-foreground mt-1">
                          <li>Only download apps from official stores (Google Play, Apple App Store)</li>
                          <li>Verify publisher identity and extension reviews</li>
                          <li>Check the number of downloads and user ratings</li>
                          <li>Be wary of extensions requesting excessive permissions</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="mt-6">
                  <h4 className="font-medium mb-3">Red Flags to Watch For</h4>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="p-4 border rounded-md">
                      <ul className="list-disc pl-5 text-sm text-muted-foreground space-y-2">
                        <li><span className="font-medium">Urgency:</span> Claims that you need to act immediately or lose funds</li>
                        <li><span className="font-medium">Too Good to Be True:</span> Promises of unrealistic returns or free money</li>
                        <li><span className="font-medium">Verification Requests:</span> Asking you to "verify your wallet" by connecting it</li>
                        <li><span className="font-medium">Suspicious URLs:</span> Domains with misspellings or extra words (metamask-wallet.com)</li>
                      </ul>
                    </div>

                    <div className="p-4 border rounded-md">
                      <ul className="list-disc pl-5 text-sm text-muted-foreground space-y-2">
                        <li><span className="font-medium">Unusual Contact:</span> Support reaching out to you unprompted</li>
                        <li><span className="font-medium">Private Messages:</span> Help offers through DMs rather than official channels</li>
                        <li><span className="font-medium">Seed Phrase Requests:</span> Any request for recovery phrases or private keys</li>
                        <li><span className="font-medium">Suspicious Apps:</span> Apps requesting excessive permissions</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </section>

              <Separator />

              <section>
                <h3 className="text-lg font-medium mb-2">Phishing Prevention Checklist</h3>

                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="text-green-500" size={18} />
                    <p className="text-muted-foreground">Bookmark official websites for wallets and exchanges</p>
                  </div>

                  <div className="flex items-center gap-2">
                    <CheckCircle className="text-green-500" size={18} />
                    <p className="text-muted-foreground">Always verify URLs before entering any credentials</p>
                  </div>

                  <div className="flex items-center gap-2">
                    <CheckCircle className="text-green-500" size={18} />
                    <p className="text-muted-foreground">Use hardware wallets for transaction verification when possible</p>
                  </div>

                  <div className="flex items-center gap-2">
                    <CheckCircle className="text-green-500" size={18} />
                    <p className="text-muted-foreground">Never share seed phrases or private keys with anyone</p>
                  </div>

                  <div className="flex items-center gap-2">
                    <CheckCircle className="text-green-500" size={18} />
                    <p className="text-muted-foreground">Be skeptical of unsolicited messages, even from apparent friends</p>
                  </div>

                  <div className="flex items-center gap-2">
                    <CheckCircle className="text-green-500" size={18} />
                    <p className="text-muted-foreground">Double-check contract addresses against official sources</p>
                  </div>

                  <div className="flex items-center gap-2">
                    <CheckCircle className="text-green-500" size={18} />
                    <p className="text-muted-foreground">Read all transaction details before signing</p>
                  </div>

                  <div className="flex items-center gap-2">
                    <CheckCircle className="text-green-500" size={18} />
                    <p className="text-muted-foreground">Use a separate "hot wallet" with minimal funds for daily transactions</p>
                  </div>

                  <div className="flex items-center gap-2">
                    <CheckCircle className="text-green-500" size={18} />
                    <p className="text-muted-foreground">Enable 2FA on all accounts using an authenticator app (not SMS)</p>
                  </div>

                  <div className="flex items-center gap-2">
                    <CheckCircle className="text-green-500" size={18} />
                    <p className="text-muted-foreground">Keep informed about the latest phishing techniques in crypto</p>
                  </div>
                </div>

                <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-medium mb-3">If You Suspect a Phishing Attempt</h4>
                    <ol className="list-decimal pl-5 text-muted-foreground space-y-1">
                      <li>Don't click any links or open attachments</li>
                      <li>Report the attempt to the legitimate company being impersonated</li>
                      <li>Block the sender on all platforms</li>
                      <li>Report the account to the platform (Discord, Twitter, etc.)</li>
                      <li>Share details with community (without clicking suspicious links)</li>
                    </ol>
                  </div>

                  <div>
                    <h4 className="font-medium mb-3">If You've Been Phished</h4>
                    <ol className="list-decimal pl-5 text-muted-foreground space-y-1">
                      <li>Transfer any remaining funds to a new, secure wallet immediately</li>
                      <li>Disconnect your wallet from all sites and apps</li>
                      <li>Revoke any suspicious contract approvals</li>
                      <li>Report the incident to relevant exchanges</li>
                      <li>Document everything for potential legal action</li>
                      <li>Reset affected devices and scan for malware</li>
                    </ol>
                  </div>
                </div>
              </section>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                <div className="flex flex-col items-center p-6 border rounded-md">
                  <BookOpen className="h-8 w-8 text-primary mb-2" />
                  <h3 className="text-lg font-medium mb-2">Test Your Knowledge</h3>
                  <p className="text-center text-muted-foreground mb-4">
                    Take our comprehensive wallet security quiz to test your understanding of key concepts.
                  </p>
                  <Button onClick={handleQuizStart}>
                    Start Security Quiz
                  </Button>
                </div>

                <div className="flex flex-col items-center p-6 border rounded-md">
                  <AlertCircle className="h-8 w-8 text-primary mb-2" />
                  <h3 className="text-lg font-medium mb-2">Phishing Simulator</h3>
                  <p className="text-center text-muted-foreground mb-4">
                    Test your ability to identify common phishing attempts with our interactive simulator.
                  </p>
                  <Button onClick={handlePhishingTest}>
                    Try Phishing Simulator
                  </Button>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
