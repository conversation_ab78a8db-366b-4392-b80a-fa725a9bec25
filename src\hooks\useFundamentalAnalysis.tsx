
import { useState } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { 
  getTopFundamentalCoins, 
  fetchFundamentalAnalysis, 
  fetchTokenHistory,
  fetchMetricsData
} from '@/services/api/fundamental';
import { toast } from '@/hooks/use-toast';

export interface FundamentalCoin {
  id: string;
  name: string;
  symbol: string;
  price: number;
  image: string;
  marketCap: number;
  change24h: number;
  tokenomics: any;
  developerData: any;
  onChainMetrics: any;
  fundamentalScore: {
    total: number;
    tokenomics: number;
    development: number;
    utility: number;
  } | null;
}

export interface HistoricalDataPoint {
  timestamp: number;
  price: number;
  volume: number;
}

export interface MetricsDataPoint {
  name: string;
  value: number;
  fullMark: number;
  description?: string;
}

export function useFundamentalAnalysis(limit = 30, customCoins: string[] = []) {
  const [selectedCoinId, setSelectedCoinId] = useState<string | null>(null);
  const queryClient = useQueryClient();
  
  // Fetch top coins by fundamental score
  const { 
    data: topCoins, 
    isLoading: isLoadingTop,
    error: topError,
    refetch: refetchTop
  } = useQuery({
    queryKey: ['top-fundamental-coins', limit],
    queryFn: () => getTopFundamentalCoins(limit),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
  
  // Fetch custom coins if provided
  const {
    data: customCoinData,
    isLoading: isLoadingCustom,
    error: customError,
    refetch: refetchCustom
  } = useQuery({
    queryKey: ['custom-fundamental-coins', customCoins],
    queryFn: () => customCoins.length > 0 ? fetchFundamentalAnalysis(customCoins) : Promise.resolve([]),
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: customCoins.length > 0
  });
  
  // Fetch historical data for selected coin
  const {
    data: historicalData,
    isLoading: isLoadingHistory,
    error: historyError
  } = useQuery({
    queryKey: ['coin-history', selectedCoinId],
    queryFn: () => selectedCoinId ? fetchTokenHistory(selectedCoinId) : Promise.resolve([]),
    staleTime: 15 * 60 * 1000, // 15 minutes
    enabled: !!selectedCoinId,
    meta: {
      onError: () => {
        toast({
          title: "Error fetching historical data",
          description: "Unable to load price history. Please try again later.",
          variant: "destructive"
        });
      }
    }
  });
  
  // Fetch metrics data for selected coin
  const {
    data: metricsData,
    isLoading: isLoadingMetrics,
    error: metricsError
  } = useQuery({
    queryKey: ['coin-metrics', selectedCoinId],
    queryFn: () => selectedCoinId ? fetchMetricsData(selectedCoinId) : Promise.resolve(null),
    staleTime: 15 * 60 * 1000, // 15 minutes
    enabled: !!selectedCoinId
  });
  
  // Get selected coin data
  const selectedCoin = selectedCoinId ? 
    [...(topCoins || []), ...(customCoinData || [])].find(coin => coin.id === selectedCoinId) : 
    null;
  
  // Refresh all data
  const refreshData = () => {
    refetchTop();
    if (customCoins.length > 0) {
      refetchCustom();
    }
    if (selectedCoinId) {
      // Force refetch of historical and metrics data
      queryClient.invalidateQueries({ queryKey: ['coin-history', selectedCoinId] });
      queryClient.invalidateQueries({ queryKey: ['coin-metrics', selectedCoinId] });
    }
  };
  
  // Calculate loading state
  const isLoading = isLoadingTop || isLoadingCustom;
  const isLoadingDetails = isLoadingHistory || isLoadingMetrics;
  
  console.log("Top coins data:", topCoins);
  console.log("Selected coin:", selectedCoin);
  
  return {
    topCoins: topCoins || [],
    customCoins: customCoinData || [],
    isLoading,
    error: topError || customError,
    selectedCoin,
    setSelectedCoinId,
    refreshData,
    historicalData: historicalData || [],
    metricsData: metricsData || null,
    isLoadingDetails
  };
}
