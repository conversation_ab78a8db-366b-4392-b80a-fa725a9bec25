
import axios from "axios";
import { fetchTopCoins, fetchGlobalData } from "./coinMarketData";
import { cacheResponse, handleApiError } from "./coinGeckoClient";
import { ChartPattern, AnomalyAlert, PredictionModel, SentimentSource } from "@/types/aiInsights";

// Generate chart pattern recognition data
export const generatePatternRecognitionData = async (coinId: string, days: number = 30): Promise<ChartPattern[]> => {
  try {
    // In a production environment, you would use a real pattern recognition API
    // For now, we'll generate realistic mock data based on actual market trends
    
    // Fetch real price data to inform our patterns
    const response = await axios.get(`https://api.coingecko.com/api/v3/coins/${coinId}/market_chart`, {
      params: {
        vs_currency: 'usd',
        days: days,
        interval: 'daily'
      }
    });
    
    const priceData = response.data.prices || [];
    
    // Generate patterns based on actual price movements
    const patterns: ChartPattern[] = [];
    
    // Check for head and shoulders pattern (simplified algorithm)
    const hasHeadAndShoulders = checkHeadAndShoulders(priceData);
    if (hasHeadAndShoulders) {
      patterns.push({
        id: `hs-${Date.now()}`,
        name: "Head and Shoulders",
        type: "bearish",
        confidence: 78,
        description: "Classical reversal pattern indicating potential downward movement",
        startIndex: 12,
        endIndex: 24,
        pricePoints: priceData.slice(12, 24).map(p => p[1])
      });
    }
    
    // Check for cup and handle pattern (simplified)
    const hasCupAndHandle = checkCupAndHandle(priceData);
    if (hasCupAndHandle) {
      patterns.push({
        id: `ch-${Date.now()}`,
        name: "Cup and Handle",
        type: "bullish",
        confidence: 82,
        description: "Bullish continuation pattern pointing to potential upward momentum",
        startIndex: 5,
        endIndex: 25,
        pricePoints: priceData.slice(5, 25).map(p => p[1])
      });
    }
    
    // Add some other common patterns based on price action
    if (priceData.length > 10) {
      const recentPrices = priceData.slice(priceData.length - 10).map(p => p[1]);
      
      // Simple uptrend check
      if (isUptrend(recentPrices)) {
        patterns.push({
          id: `ut-${Date.now()}`,
          name: "Ascending Triangle",
          type: "bullish",
          confidence: 65,
          description: "Bullish continuation pattern with higher lows and consistent highs",
          startIndex: priceData.length - 10,
          endIndex: priceData.length - 1,
          pricePoints: recentPrices
        });
      }
      
      // Simple downtrend check
      if (isDowntrend(recentPrices)) {
        patterns.push({
          id: `dt-${Date.now()}`,
          name: "Descending Triangle",
          type: "bearish",
          confidence: 68,
          description: "Bearish continuation pattern with lower highs and consistent lows",
          startIndex: priceData.length - 10,
          endIndex: priceData.length - 1,
          pricePoints: recentPrices
        });
      }
    }
    
    // If no patterns detected, add a "No Clear Pattern" entry
    if (patterns.length === 0) {
      patterns.push({
        id: `np-${Date.now()}`,
        name: "No Clear Pattern",
        type: "neutral",
        confidence: 60,
        description: "Price action currently shows no distinctive technical pattern",
        startIndex: 0,
        endIndex: priceData.length - 1,
        pricePoints: priceData.map(p => p[1])
      });
    }
    
    cacheResponse(`pattern_recognition_${coinId}_${days}`, patterns);
    return patterns;
  } catch (error) {
    return handleApiError(error, {
      key: `pattern_recognition_${coinId}_${days}`,
      data: []
    });
  }
};

// Generate anomaly detection data
export const generateAnomalyAlerts = async (limit: number = 5): Promise<AnomalyAlert[]> => {
  try {
    // Fetch real market data to generate meaningful anomalies
    const [topCoins, globalData] = await Promise.all([
      fetchTopCoins(20),
      fetchGlobalData()
    ]);
    
    const anomalies: AnomalyAlert[] = [];
    
    // Find coins with unusual price movements
    topCoins.forEach(coin => {
      const priceChange = coin.price_change_percentage_24h;
      const volumeMarketCapRatio = coin.total_volume / coin.market_cap;
      
      // Flag large price movements
      if (Math.abs(priceChange) > 15) {
        anomalies.push({
          id: `price-${coin.id}-${Date.now()}`,
          type: "price_movement",
          asset: coin.name,
          symbol: coin.symbol.toUpperCase(),
          severity: Math.abs(priceChange) > 25 ? "high" : "medium",
          description: `Unusual ${priceChange > 0 ? 'positive' : 'negative'} price movement of ${Math.abs(priceChange).toFixed(2)}% in 24h`,
          timestamp: new Date().toISOString(),
          metrics: {
            change: priceChange,
            volume: coin.total_volume,
            average: 2.5 // historical average (mock)
          }
        });
      }
      
      // Flag unusual trading volume
      if (volumeMarketCapRatio > 0.3) {
        anomalies.push({
          id: `volume-${coin.id}-${Date.now()}`,
          type: "trading_volume",
          asset: coin.name,
          symbol: coin.symbol.toUpperCase(),
          severity: volumeMarketCapRatio > 0.5 ? "high" : "medium",
          description: `Unusually high trading volume (${(volumeMarketCapRatio * 100).toFixed(2)}% of market cap)`,
          timestamp: new Date().toISOString(),
          metrics: {
            ratio: volumeMarketCapRatio,
            volume: coin.total_volume,
            average: 0.08 // historical average (mock)
          }
        });
      }
    });
    
    // Generate some whale movement anomalies
    const whaleMovementCoins = ['bitcoin', 'ethereum', 'binancecoin'].filter(
      id => Math.random() > 0.6
    );
    
    whaleMovementCoins.forEach(coinId => {
      const matchingCoin = topCoins.find(c => c.id === coinId);
      if (matchingCoin) {
        const movementAmount = (Math.random() * 150 + 50) * (coinId === 'bitcoin' ? 1 : coinId === 'ethereum' ? 10 : 100);
        const valueUsd = movementAmount * matchingCoin.current_price;
        
        anomalies.push({
          id: `whale-${coinId}-${Date.now()}`,
          type: "whale_movement",
          asset: matchingCoin.name,
          symbol: matchingCoin.symbol.toUpperCase(),
          severity: valueUsd > 100000000 ? "high" : "medium",
          description: `Whale wallet moved ${movementAmount.toFixed(2)} ${matchingCoin.symbol.toUpperCase()} ($${(valueUsd / 1000000).toFixed(2)}M)`,
          timestamp: new Date().toISOString(),
          metrics: {
            amount: movementAmount,
            value: valueUsd,
            wallet: `0x${Array(40).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('')}`
          }
        });
      }
    });
    
    // Sort by severity and limit
    const sortedAnomalies = anomalies
      .sort((a, b) => {
        if (a.severity === 'high' && b.severity !== 'high') return -1;
        if (a.severity !== 'high' && b.severity === 'high') return 1;
        return new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime();
      })
      .slice(0, limit);
    
    cacheResponse('anomaly_alerts', sortedAnomalies);
    return sortedAnomalies;
  } catch (error) {
    return handleApiError(error, {
      key: 'anomaly_alerts',
      data: []
    });
  }
};

// Generate sentiment analysis data
export const generateSentimentAnalysis = async (coinId: string = 'bitcoin'): Promise<SentimentSource[]> => {
  try {
    // In a production app, you would connect to a real sentiment analysis API
    // Here we'll generate realistic mock data
    
    const socialPlatforms = [
      'Twitter',
      'Reddit',
      'Telegram',
      'Discord',
      'YouTube'
    ];
    
    const newsSources = [
      'CoinDesk',
      'Cointelegraph',
      'The Block',
      'Decrypt',
      'Bloomberg Crypto'
    ];
    
    const sentimentSources: SentimentSource[] = [];
    
    // Generate social media sentiment
    socialPlatforms.forEach(platform => {
      // Randomize sentiment but make it somewhat realistic
      const baseScore = coinId === 'bitcoin' ? 65 : coinId === 'ethereum' ? 70 : 50;
      const randomVariation = Math.floor(Math.random() * 30) - 15;
      const sentimentScore = Math.max(0, Math.min(100, baseScore + randomVariation));
      
      sentimentSources.push({
        id: `${platform.toLowerCase()}-${Date.now()}`,
        name: platform,
        type: "social_media",
        sentiment: sentimentScore > 65 ? "positive" : sentimentScore < 35 ? "negative" : "neutral",
        sentimentScore: sentimentScore,
        volume: Math.floor(Math.random() * 10000) + 500,
        keyPhrases: generateKeyPhrases(sentimentScore),
        change24h: (Math.random() * 20) - 10,
        timeperiod: "24h"
      });
    });
    
    // Generate news sentiment
    newsSources.forEach(source => {
      const baseScore = coinId === 'bitcoin' ? 60 : coinId === 'ethereum' ? 65 : 55;
      const randomVariation = Math.floor(Math.random() * 20) - 10;
      const sentimentScore = Math.max(0, Math.min(100, baseScore + randomVariation));
      
      sentimentSources.push({
        id: `${source.toLowerCase().replace(/\s/g, '-')}-${Date.now()}`,
        name: source,
        type: "news",
        sentiment: sentimentScore > 65 ? "positive" : sentimentScore < 35 ? "negative" : "neutral",
        sentimentScore: sentimentScore,
        volume: Math.floor(Math.random() * 50) + 5,
        keyPhrases: generateKeyPhrases(sentimentScore),
        change24h: (Math.random() * 15) - 7.5,
        timeperiod: "24h"
      });
    });
    
    cacheResponse(`sentiment_analysis_${coinId}`, sentimentSources);
    return sentimentSources;
  } catch (error) {
    return handleApiError(error, {
      key: `sentiment_analysis_${coinId}`,
      data: []
    });
  }
};

// Generate prediction data with confidence intervals
export const generatePriceForecasts = async (coinId: string, days: number = 30): Promise<PredictionModel> => {
  try {
    // Fetch real historical data to inform our predictions
    const response = await axios.get(`https://api.coingecko.com/api/v3/coins/${coinId}/market_chart`, {
      params: {
        vs_currency: 'usd',
        days: days,
        interval: 'daily'
      }
    });
    
    const priceData = response.data.prices || [];
    
    // Extract just the prices
    const prices = priceData.map((p: any) => p[1]);
    
    // Generate forecasts
    const forecastDays = 7; // 7-day forecast
    
    // Use a simple model that adds some realistic randomness
    // In a real app, you'd use actual ML models
    const lastPrice = prices[prices.length - 1];
    const priceVolatility = calculateVolatility(prices);
    
    // Calculate a plausible trend based on recent price action
    const recentPrices = prices.slice(-5);
    const avgChange = calculateAverageChange(recentPrices);
    
    // Generate forecast with upper and lower bounds
    const forecast = [];
    const upperBound = [];
    const lowerBound = [];
    
    let currentForecast = lastPrice;
    
    for (let i = 0; i < forecastDays; i++) {
      // Add a bit of momentum and randomness
      const dayRandomness = ((Math.random() * 2) - 1) * priceVolatility * 0.5;
      const trend = avgChange * (1 - (i / forecastDays) * 0.2); // Diminishing trend effect
      
      currentForecast = currentForecast * (1 + (trend + dayRandomness) / 100);
      
      // Confidence intervals widen as we predict further out
      const confidenceInterval = priceVolatility * currentForecast * (i + 1) / 100;
      
      forecast.push({
        date: new Date(Date.now() + (i + 1) * 86400000).toISOString().split('T')[0],
        price: currentForecast
      });
      
      upperBound.push({
        date: new Date(Date.now() + (i + 1) * 86400000).toISOString().split('T')[0],
        price: currentForecast + confidenceInterval
      });
      
      lowerBound.push({
        date: new Date(Date.now() + (i + 1) * 86400000).toISOString().split('T')[0],
        price: Math.max(0, currentForecast - confidenceInterval) // Prices can't go below zero
      });
    }
    
    const predictionModel: PredictionModel = {
      coinId,
      historicalPrices: priceData.map((p: any) => ({
        date: new Date(p[0]).toISOString().split('T')[0],
        price: p[1]
      })),
      forecast,
      upperBound,
      lowerBound,
      metrics: {
        volatility: priceVolatility,
        r2Score: 0.78 + (Math.random() * 0.1), // R² score between 0.78-0.88
        mape: 3.5 + (Math.random() * 2), // Mean Absolute Percentage Error
        confidence: 0.68 + (Math.random() * 0.17) // Confidence level 0.68-0.85
      },
      modelDetails: {
        name: "Hybrid ARIMA-LSTM",
        features: [
          "Historical price data",
          "Volume profiles",
          "Social sentiment",
          "Market correlation factors",
          "On-chain metrics"
        ],
        lastUpdated: new Date().toISOString()
      }
    };
    
    cacheResponse(`price_forecast_${coinId}_${days}`, predictionModel);
    return predictionModel;
  } catch (error) {
    return handleApiError(error, {
      key: `price_forecast_${coinId}_${days}`,
      data: {
        coinId,
        historicalPrices: [],
        forecast: [],
        upperBound: [],
        lowerBound: [],
        metrics: {
          volatility: 0,
          r2Score: 0,
          mape: 0,
          confidence: 0
        },
        modelDetails: {
          name: "Hybrid ARIMA-LSTM",
          features: [],
          lastUpdated: new Date().toISOString()
        }
      }
    });
  }
};

// Calculate risk scores for assets
export const generateRiskAssessment = async (assets: string[] = []): Promise<any> => {
  try {
    // If no assets provided, use the top coins
    if (!assets.length) {
      const topCoins = await fetchTopCoins(10);
      assets = topCoins.map(coin => coin.id);
    }
    
    // Fetch data for the assets
    const coinPromises = assets.map(async (coinId) => {
      try {
        const response = await axios.get(`https://api.coingecko.com/api/v3/coins/${coinId}`);
        return response.data;
      } catch (error) {
        console.error(`Error fetching data for ${coinId}:`, error);
        return null;
      }
    });
    
    const coinsData = (await Promise.all(coinPromises)).filter(Boolean);
    
    // Generate risk assessments
    const riskAssessments = coinsData.map(coin => {
      // Calculate market risk
      const marketRisk = calculateMarketRisk(coin);
      
      // Calculate technical risk
      const technicalRisk = calculateTechnicalRisk(coin);
      
      // Calculate fundamental risk
      const fundamentalRisk = calculateFundamentalRisk(coin);
      
      // Calculate volatility risk
      const volatilityRisk = calculateVolatilityRisk(coin);
      
      // Calculate regulatory risk (mocked)
      const regulatoryRisk = (Math.random() * 50) + 25;
      
      // Calculate overall risk (weighted average)
      const overallRisk = (
        marketRisk * 0.25 +
        technicalRisk * 0.2 +
        fundamentalRisk * 0.2 +
        volatilityRisk * 0.25 +
        regulatoryRisk * 0.1
      );
      
      return {
        id: coin.id,
        name: coin.name,
        symbol: coin.symbol.toUpperCase(),
        overallRisk: {
          score: Math.round(overallRisk),
          label: getRiskLabel(overallRisk),
          color: getRiskColor(overallRisk)
        },
        factors: [
          {
            name: "Market Risk",
            score: Math.round(marketRisk),
            description: "Exposure to market conditions and systematic risk"
          },
          {
            name: "Technical Risk",
            score: Math.round(technicalRisk),
            description: "Security vulnerabilities and technical robustness"
          },
          {
            name: "Fundamental Risk",
            score: Math.round(fundamentalRisk),
            description: "Project fundamentals, team, and tokenomics"
          },
          {
            name: "Volatility Risk",
            score: Math.round(volatilityRisk),
            description: "Price volatility and market stability"
          },
          {
            name: "Regulatory Risk",
            score: Math.round(regulatoryRisk),
            description: "Exposure to potential regulatory actions"
          }
        ],
        recommendations: generateRiskRecommendations(overallRisk)
      };
    });
    
    cacheResponse('risk_assessment', riskAssessments);
    return riskAssessments;
  } catch (error) {
    return handleApiError(error, {
      key: 'risk_assessment',
      data: []
    });
  }
};

// Helper functions for pattern recognition
function checkHeadAndShoulders(priceData: any[]): boolean {
  // Simple check for a potential head and shoulders pattern
  // In a real implementation, this would be a much more sophisticated algorithm
  if (priceData.length < 25) return false;
  
  const prices = priceData.map(p => p[1]);
  const segment = prices.slice(prices.length - 25);
  
  // Very simplified check - not an actual pattern detection algorithm
  return Math.random() > 0.7; // 30% chance to detect this pattern for demo purposes
}

function checkCupAndHandle(priceData: any[]): boolean {
  // Simple check for potential cup and handle pattern
  if (priceData.length < 25) return false;
  
  const prices = priceData.map(p => p[1]);
  const segment = prices.slice(prices.length - 25);
  
  // Very simplified check - not an actual pattern detection algorithm
  return Math.random() > 0.75; // 25% chance to detect this pattern for demo purposes
}

function isUptrend(prices: number[]): boolean {
  if (prices.length < 5) return false;
  
  // Check if prices are generally trending upward
  const firstHalf = prices.slice(0, Math.floor(prices.length / 2));
  const secondHalf = prices.slice(Math.floor(prices.length / 2));
  
  const firstAvg = firstHalf.reduce((sum, price) => sum + price, 0) / firstHalf.length;
  const secondAvg = secondHalf.reduce((sum, price) => sum + price, 0) / secondHalf.length;
  
  return secondAvg > firstAvg * 1.02; // 2% higher on average
}

function isDowntrend(prices: number[]): boolean {
  if (prices.length < 5) return false;
  
  // Check if prices are generally trending downward
  const firstHalf = prices.slice(0, Math.floor(prices.length / 2));
  const secondHalf = prices.slice(Math.floor(prices.length / 2));
  
  const firstAvg = firstHalf.reduce((sum, price) => sum + price, 0) / firstHalf.length;
  const secondAvg = secondHalf.reduce((sum, price) => sum + price, 0) / secondHalf.length;
  
  return secondAvg < firstAvg * 0.98; // 2% lower on average
}

// Helper functions for sentiment analysis
function generateKeyPhrases(sentimentScore: number): string[] {
  const positiveTerms = [
    "bullish trajectory",
    "increased adoption",
    "positive momentum",
    "institutional interest",
    "strong fundamentals",
    "technical breakthrough",
    "strategic partnership"
  ];
  
  const neutralTerms = [
    "market consolidation",
    "price stabilization",
    "trading sideways",
    "mixed signals",
    "awaiting catalyst",
    "holding pattern"
  ];
  
  const negativeTerms = [
    "bearish sentiment",
    "profit-taking",
    "regulatory concerns",
    "technical weakness",
    "selling pressure",
    "decreasing volume",
    "whale dumping"
  ];
  
  let terms;
  if (sentimentScore > 65) {
    terms = positiveTerms;
  } else if (sentimentScore < 35) {
    terms = negativeTerms;
  } else {
    terms = neutralTerms;
  }
  
  // Select a random subset of these terms
  const numTerms = Math.floor(Math.random() * 3) + 1; // 1-3 terms
  const shuffled = [...terms].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, numTerms);
}

// Helper functions for predictions
function calculateVolatility(prices: number[]): number {
  if (prices.length < 2) return 0;
  
  // Calculate daily returns
  const returns = [];
  for (let i = 1; i < prices.length; i++) {
    returns.push((prices[i] - prices[i - 1]) / prices[i - 1]);
  }
  
  // Calculate standard deviation of returns
  const mean = returns.reduce((sum, r) => sum + r, 0) / returns.length;
  const sumSquaredDiff = returns.reduce((sum, r) => sum + Math.pow(r - mean, 2), 0);
  const stdDev = Math.sqrt(sumSquaredDiff / returns.length);
  
  // Annualized volatility
  return stdDev * Math.sqrt(365) * 100; // As a percentage
}

function calculateAverageChange(prices: number[]): number {
  if (prices.length < 2) return 0;
  
  let totalPctChange = 0;
  for (let i = 1; i < prices.length; i++) {
    totalPctChange += ((prices[i] - prices[i - 1]) / prices[i - 1]) * 100;
  }
  
  return totalPctChange / (prices.length - 1);
}

// Helper functions for risk assessment
function calculateMarketRisk(coin: any): number {
  // Calculate based on market cap, volume, and liquidity
  const marketCap = coin.market_data?.market_cap?.usd || 0;
  const volume = coin.market_data?.total_volume?.usd || 0;
  
  // Higher market cap = lower risk
  let marketCapFactor;
  if (marketCap > 10000000000) marketCapFactor = 20; // >$10B
  else if (marketCap > 1000000000) marketCapFactor = 35; // >$1B
  else if (marketCap > 100000000) marketCapFactor = 50; // >$100M
  else if (marketCap > 10000000) marketCapFactor = 70; // >$10M
  else marketCapFactor = 85; // <$10M
  
  // Higher volume/market cap ratio = lower risk (more liquid)
  const volumeToMarketCapRatio = marketCap > 0 ? volume / marketCap : 0;
  let liquidityFactor;
  if (volumeToMarketCapRatio > 0.3) liquidityFactor = 25;
  else if (volumeToMarketCapRatio > 0.1) liquidityFactor = 40;
  else if (volumeToMarketCapRatio > 0.05) liquidityFactor = 60;
  else liquidityFactor = 80;
  
  return (marketCapFactor * 0.7) + (liquidityFactor * 0.3);
}

function calculateTechnicalRisk(coin: any): number {
  // In a real implementation, you would analyze code quality, security audits, etc.
  // Here we use age and developer activity as proxies
  
  // Age of project (older = more reliable)
  const genesisDate = coin.genesis_date ? new Date(coin.genesis_date) : null;
  const now = new Date();
  const ageInDays = genesisDate ? (now.getTime() - genesisDate.getTime()) / (1000 * 60 * 60 * 24) : 0;
  
  let ageFactor;
  if (ageInDays > 1825) ageFactor = 20; // >5 years
  else if (ageInDays > 730) ageFactor = 35; // >2 years
  else if (ageInDays > 365) ageFactor = 50; // >1 year
  else if (ageInDays > 180) ageFactor = 65; // >6 months
  else ageFactor = 80; // <6 months
  
  // Developer activity (mocked for this example)
  const devActivity = Math.random() * 60 + 20;
  
  return (ageFactor * 0.6) + (devActivity * 0.4);
}

function calculateFundamentalRisk(coin: any): number {
  // Consider community, use case, and tokenomics
  
  // Community size proxy
  const communityScore = coin.community_score || 0;
  let communityFactor;
  if (communityScore > 75) communityFactor = 20;
  else if (communityScore > 50) communityFactor = 40;
  else if (communityScore > 25) communityFactor = 60;
  else communityFactor = 80;
  
  // Development score
  const developmentScore = coin.developer_score || 0;
  let developmentFactor;
  if (developmentScore > 75) developmentFactor = 20;
  else if (developmentScore > 50) developmentFactor = 40;
  else if (developmentScore > 25) developmentFactor = 60;
  else developmentFactor = 80;
  
  return (communityFactor * 0.5) + (developmentFactor * 0.5);
}

function calculateVolatilityRisk(coin: any): number {
  // Consider price volatility
  const ath = coin.market_data?.ath?.usd || 0;
  const currentPrice = coin.market_data?.current_price?.usd || 0;
  const athChangePercentage = coin.market_data?.ath_change_percentage?.usd || 0;
  
  // Volatility based on ATH drop
  let athFactor;
  if (athChangePercentage > -30) athFactor = 30;
  else if (athChangePercentage > -60) athFactor = 50;
  else if (athChangePercentage > -80) athFactor = 70;
  else athFactor = 85;
  
  // Price change percentage
  const priceChange24h = coin.market_data?.price_change_percentage_24h || 0;
  const priceChange7d = coin.market_data?.price_change_percentage_7d || 0;
  
  const shortTermVolatility = Math.abs(priceChange24h);
  const mediumTermVolatility = Math.abs(priceChange7d);
  
  let volatilityFactor;
  if ((shortTermVolatility + mediumTermVolatility) / 2 < 5) volatilityFactor = 30;
  else if ((shortTermVolatility + mediumTermVolatility) / 2 < 10) volatilityFactor = 45;
  else if ((shortTermVolatility + mediumTermVolatility) / 2 < 15) volatilityFactor = 60;
  else volatilityFactor = 75;
  
  return (athFactor * 0.4) + (volatilityFactor * 0.6);
}

function getRiskLabel(score: number): string {
  if (score < 25) return "Very Low";
  if (score < 40) return "Low";
  if (score < 60) return "Medium";
  if (score < 75) return "High";
  return "Very High";
}

function getRiskColor(score: number): string {
  if (score < 25) return "green";
  if (score < 40) return "emerald";
  if (score < 60) return "yellow";
  if (score < 75) return "orange";
  return "red";
}

function generateRiskRecommendations(riskScore: number): string[] {
  const lowRiskRecommendations = [
    "Suitable for core portfolio allocation",
    "Consider for long-term holdings",
    "Appropriate for diversification strategy"
  ];
  
  const mediumRiskRecommendations = [
    "Limit to moderate portfolio allocation",
    "Consider using stop-loss orders",
    "Monitor market conditions regularly"
  ];
  
  const highRiskRecommendations = [
    "Keep allocation to small percentage of portfolio",
    "Consider shorter time horizons",
    "Use strict risk management practices"
  ];
  
  if (riskScore < 40) return lowRiskRecommendations;
  if (riskScore < 70) return mediumRiskRecommendations;
  return highRiskRecommendations;
}
