
import { useState, useEffect } from "react";
import { Clock, Globe } from "lucide-react";

interface MarketStatusHeaderProps {
  stats: any;
}

export function MarketStatusHeader({ stats }: MarketStatusHeaderProps) {
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 1000);
    return () => clearInterval(timer);
  }, []);

  const formatCurrency = (value: number) => {
    if (value >= 1e12) return `$${(value / 1e12).toFixed(2)}T`;
    if (value >= 1e9) return `$${(value / 1e9).toFixed(2)}B`;
    if (value >= 1e6) return `$${(value / 1e6).toFixed(2)}M`;
    return `$${value.toLocaleString()}`;
  };

  return (
    <div className="bg-card border-b border-border px-6 py-3">
      <div className="flex items-center justify-between max-w-screen-2xl mx-auto">
        <div className="flex items-center space-x-8">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-green-500 font-semibold">MARKETS OPEN</span>
          </div>
          <div className="flex items-center space-x-2">
            <Clock className="h-4 w-4 text-muted-foreground" />
            <span className="text-muted-foreground font-mono">
              {currentTime.toLocaleTimeString()} UTC
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <Globe className="h-4 w-4 text-orange-500" />
            <span className="text-orange-500 font-semibold">GLOBAL CRYPTO</span>
          </div>
        </div>

        <div className="flex items-center space-x-6">
          <div className="text-right">
            <div className="text-xs text-muted-foreground">TOTAL MARKET CAP</div>
            <div className="text-lg font-bold text-foreground">
              {formatCurrency(stats?.totalMarketCap * 1e12 || 0)}
            </div>
          </div>
          <div className="text-right">
            <div className="text-xs text-muted-foreground">24H VOLUME</div>
            <div className="text-lg font-bold text-foreground">
              {formatCurrency(stats?.dailyVolume * 1e9 || 0)}
            </div>
          </div>
          <div className="text-right">
            <div className="text-xs text-muted-foreground">BTC DOMINANCE</div>
            <div className="text-lg font-bold text-orange-500">
              {stats?.btcDominance?.toFixed(1) || 0}%
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
