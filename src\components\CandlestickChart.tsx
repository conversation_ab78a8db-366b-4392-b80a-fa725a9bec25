import { useState } from "react";
import { cn } from "@/lib/utils";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  ReferenceLine,
  AreaChart,
  Area,
  BarChart,
  Bar,
  Legend,
  Brush,
  ComposedChart,
  Cell
} from "recharts";
import TechnicalIndicatorSelector, { TechnicalIndicator } from "./TechnicalIndicatorSelector";
import { ChartBarBig } from "lucide-react";

interface CandleData {
  time: string;
  date: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume?: number;
  predicted?: boolean;
  sma20?: number;
  sma50?: number;
  sma200?: number;
  rsi?: number;
  upperBand?: number;
  middleBand?: number;
  lowerBand?: number;
  macd?: number;
  signal?: number;
  histogram?: number;
  lowerBound?: number;
  upperBound?: number;
  prediction?: number;
  price?: number;
}

interface CandlestickChartProps {
  data: CandleData[];
  title: string;
  description?: string;
  timeframes?: string[];
  onTimeframeChange?: (timeframe: string) => void;
  isLoading?: boolean;
  height?: number;
  showTechnicalIndicators?: boolean;
}

export default function CandlestickChart({
  data,
  title,
  description,
  timeframes = ["1D", "7D", "30D", "90D", "1Y"],
  onTimeframeChange,
  isLoading = false,
  height = 400,
  showTechnicalIndicators = true,
}: CandlestickChartProps) {
  const [activeTimeframe, setActiveTimeframe] = useState(timeframes[0]);
  const [activeIndicators, setActiveIndicators] = useState<TechnicalIndicator[]>(["sma20"]);
  const [showSubChart, setShowSubChart] = useState<"none" | "rsi" | "macd" | "volume">("none");

  const hasPredictions = data.some(item => item.predicted || item.prediction !== undefined);

  const handleTimeframeChange = (timeframe: string) => {
    setActiveTimeframe(timeframe);
    if (onTimeframeChange) {
      onTimeframeChange(timeframe);
    }
  };

  const toggleIndicator = (indicator: TechnicalIndicator) => {
    setActiveIndicators(prev => {
      if (prev.includes(indicator)) {
        // Remove indicator
        return prev.filter(i => i !== indicator);
      } else {
        // Add indicator
        return [...prev, indicator];
      }
    });

    // Automatically show sub-chart if corresponding indicator is enabled
    if (indicator === "rsi" && !activeIndicators.includes("rsi")) {
      setShowSubChart("rsi");
    } else if (indicator === "macd" && !activeIndicators.includes("macd")) {
      setShowSubChart("macd");
    } else if (indicator === "volume" && !activeIndicators.includes("volume")) {
      setShowSubChart("volume");
    }
  };

  // Format data for the chart
  const chartData = data.map(item => ({
    ...item,
    date: item.date || item.time,
    price: item.close || item.price,
    // For RSI display purposes, ensure within 0-100 range
    displayRsi: item.rsi !== undefined ? Math.max(0, Math.min(100, item.rsi)) : undefined,
    // Add a color property for the histogram bars based on value
    histogramColor: item.histogram !== undefined && item.histogram >= 0 ? "rgb(var(--crypto-positive))" : "rgb(var(--crypto-negative))"
  }));

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      // Find the full data item for this label
      const dataPoint = chartData.find(d => d.date === label);

      if (!dataPoint) return null;

      const isPrediction = dataPoint.predicted || false;

      return (
        <div className="bg-card border border-border p-3 rounded-md shadow-md min-w-[200px]">
          <p className="font-medium mb-1">{label}</p>

          {isPrediction ? (
            <div className="mb-2 text-xs py-1 px-2 rounded-full bg-primary/20 text-primary inline-block">
              Prediction
            </div>
          ) : null}

          <div className="space-y-1 text-sm text-muted-foreground">
            <p>
              Price: <span className="text-foreground font-medium">
                ${dataPoint.price?.toLocaleString() || dataPoint.close?.toLocaleString()}
              </span>
            </p>

            {!isPrediction && dataPoint.open && (
              <>
                <p>Open: <span className="font-medium">${dataPoint.open.toLocaleString()}</span></p>
                <p>High: <span className="font-medium">${dataPoint.high.toLocaleString()}</span></p>
                <p>Low: <span className="font-medium">${dataPoint.low.toLocaleString()}</span></p>
                <p>Close: <span className="font-medium">${dataPoint.close.toLocaleString()}</span></p>
              </>
            )}

            {isPrediction && (
              <p>
                Confidence: <span className="font-medium">
                  ${dataPoint.low?.toLocaleString()} - ${dataPoint.high?.toLocaleString()}
                </span>
              </p>
            )}

            {activeIndicators.includes("sma20") && dataPoint.sma20 && (
              <p>SMA(20): <span className="font-medium text-blue-500">${dataPoint.sma20.toLocaleString()}</span></p>
            )}

            {activeIndicators.includes("sma50") && dataPoint.sma50 && (
              <p>SMA(50): <span className="font-medium text-purple-500">${dataPoint.sma50.toLocaleString()}</span></p>
            )}

            {activeIndicators.includes("sma200") && dataPoint.sma200 && (
              <p>SMA(200): <span className="font-medium text-pink-500">${dataPoint.sma200.toLocaleString()}</span></p>
            )}

            {activeIndicators.includes("rsi") && dataPoint.rsi && (
              <p>RSI: <span className="font-medium text-orange-500">{dataPoint.rsi.toFixed(2)}</span></p>
            )}

            {activeIndicators.includes("macd") && dataPoint.macd && (
              <div>
                <p>MACD: <span className="font-medium text-green-500">{dataPoint.macd.toFixed(2)}</span></p>
                <p>Signal: <span className="font-medium text-red-500">{dataPoint.signal?.toFixed(2)}</span></p>
              </div>
            )}

            {activeIndicators.includes("bollinger") && dataPoint.upperBand && (
              <div>
                <p>Upper Band: <span className="font-medium text-sky-500">${dataPoint.upperBand.toLocaleString()}</span></p>
                <p>Lower Band: <span className="font-medium text-sky-500">${dataPoint.lowerBand?.toLocaleString()}</span></p>
              </div>
            )}

            {activeIndicators.includes("volume") && dataPoint.volume && (
              <p>Volume: <span className="font-medium text-slate-500">{dataPoint.volume.toLocaleString()}</span></p>
            )}
          </div>
        </div>
      );
    }
    return null;
  };

  // Calculate chart heights
  const mainChartHeight = showSubChart === 'none' ? height : Math.round(height * 0.7);
  const subChartHeight = Math.round(height * 0.3);

  return (
    <Card className={cn("w-full", isLoading && "opacity-70")}>
      <CardHeader className="pb-2">
        <CardTitle className="flex justify-between items-center">
          <div>{title}</div>

          <div className="flex gap-1 text-sm">
            {timeframes.map((timeframe) => (
              <button
                key={timeframe}
                className={cn(
                  "px-3 py-1 rounded-full",
                  activeTimeframe === timeframe
                    ? "bg-primary text-primary-foreground"
                    : "text-muted-foreground hover:bg-secondary"
                )}
                onClick={() => handleTimeframeChange(timeframe)}
              >
                {timeframe}
              </button>
            ))}
          </div>
        </CardTitle>
        {description && <CardDescription>{description}</CardDescription>}

        {showTechnicalIndicators && (
          <div className="flex flex-wrap justify-between items-center mt-4 gap-2">
            <TechnicalIndicatorSelector
              activeIndicators={activeIndicators}
              onToggle={toggleIndicator}
            />

            <div className="flex gap-1.5">
              <button
                onClick={() => setShowSubChart(prev => prev === "rsi" ? "none" : "rsi")}
                className={cn(
                  "px-2 py-1 text-xs rounded-md transition-colors",
                  showSubChart === "rsi"
                    ? "bg-orange-500/20 text-orange-500"
                    : "bg-muted hover:bg-muted/80"
                )}
              >
                RSI
              </button>
              <button
                onClick={() => setShowSubChart(prev => prev === "macd" ? "none" : "macd")}
                className={cn(
                  "px-2 py-1 text-xs rounded-md transition-colors",
                  showSubChart === "macd"
                    ? "bg-green-500/20 text-green-500"
                    : "bg-muted hover:bg-muted/80"
                )}
              >
                MACD
              </button>
              <button
                onClick={() => setShowSubChart(prev => prev === "volume" ? "none" : "volume")}
                className={cn(
                  "px-2 py-1 text-xs rounded-md flex items-center gap-1 transition-colors",
                  showSubChart === "volume"
                    ? "bg-slate-500/20 text-slate-500"
                    : "bg-muted hover:bg-muted/80"
                )}
              >
                <ChartBarBig size={14} />
                Volume
              </button>
            </div>
          </div>
        )}
      </CardHeader>
      <CardContent>
        <div className={cn("w-full", showSubChart === 'none' ? `h-[${height}px]` : `h-[${height + 40}px]`)}>
          {isLoading ? (
            <div className="flex items-center justify-center h-full">
              <div className="animate-pulse">Loading chart data...</div>
            </div>
          ) : (
            <div className="flex flex-col">
              {/* Main chart */}
              <div style={{ height: mainChartHeight }}>
                <ResponsiveContainer width="100%" height="100%">
                  <ComposedChart
                    data={chartData}
                    margin={{
                      top: 10,
                      right: 30,
                      left: 20,
                      bottom: 5,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                    <XAxis
                      dataKey="date"
                      stroke="#6B7280"
                      tick={{ fontSize: 12 }}
                      tickMargin={10}
                    />
                    <YAxis
                      stroke="#6B7280"
                      domain={['auto', 'auto']}
                      tick={{ fontSize: 12 }}
                      tickFormatter={(value) => `$${value.toLocaleString()}`}
                    />
                    <Tooltip content={<CustomTooltip />} />
                    <Legend />

                    {/* Candlestick or price line */}
                    <Line
                      type="monotone"
                      dataKey="price"
                      stroke="#10B981"
                      strokeWidth={3}
                      dot={false}
                      isAnimationActive={false}
                      activeDot={{ r: 6 }}
                      name="Price"
                    />

                    {/* Technical Indicators */}
                    {activeIndicators.includes("sma20") && (
                      <Line
                        type="monotone"
                        dataKey="sma20"
                        stroke="#2563eb"
                        strokeWidth={2}
                        dot={false}
                        isAnimationActive={false}
                        name="SMA 20"
                      />
                    )}

                    {activeIndicators.includes("sma50") && (
                      <Line
                        type="monotone"
                        dataKey="sma50"
                        stroke="#7c3aed"
                        strokeWidth={2}
                        dot={false}
                        isAnimationActive={false}
                        name="SMA 50"
                      />
                    )}

                    {activeIndicators.includes("sma200") && (
                      <Line
                        type="monotone"
                        dataKey="sma200"
                        stroke="#be185d"
                        strokeWidth={2}
                        dot={false}
                        isAnimationActive={false}
                        name="SMA 200"
                      />
                    )}

                    {activeIndicators.includes("bollinger") && (
                      <>
                        <Line
                          type="monotone"
                          dataKey="upperBand"
                          stroke="#0ea5e9"
                          strokeWidth={1}
                          strokeDasharray="5 5"
                          dot={false}
                          isAnimationActive={false}
                          name="Upper Band"
                        />
                        <Line
                          type="monotone"
                          dataKey="middleBand"
                          stroke="#0ea5e9"
                          strokeWidth={1}
                          strokeOpacity={0.5}
                          dot={false}
                          isAnimationActive={false}
                          name="Middle Band"
                        />
                        <Line
                          type="monotone"
                          dataKey="lowerBand"
                          stroke="#0ea5e9"
                          strokeWidth={1}
                          strokeDasharray="5 5"
                          dot={false}
                          isAnimationActive={false}
                          name="Lower Band"
                        />
                        <Area
                          type="monotone"
                          dataKey="upperBand"
                          stroke="none"
                          fill="#0ea5e9"
                          fillOpacity={0.05}
                          activeDot={false}
                          isAnimationActive={false}
                        />
                        <Area
                          type="monotone"
                          dataKey="lowerBand"
                          stroke="none"
                          fill="#0ea5e9"
                          fillOpacity={0.05}
                          activeDot={false}
                          isAnimationActive={false}
                        />
                      </>
                    )}

                    {/* Predictions */}
                    {hasPredictions && (
                      <>
                        <Line
                          type="monotone"
                          dataKey="prediction"
                          stroke="#6366F1"
                          strokeWidth={2}
                          strokeDasharray="5 5"
                          dot={{ r: 4, fill: "#6366F1" }}
                          activeDot={{ r: 6 }}
                          name="Prediction"
                          isAnimationActive={false}
                        />
                        <Area
                          type="monotone"
                          dataKey="upperBound"
                          stroke="none"
                          fill="#6366F1"
                          fillOpacity={0.1}
                          activeDot={false}
                          isAnimationActive={false}
                        />
                        <Area
                          type="monotone"
                          dataKey="lowerBound"
                          stroke="none"
                          fill="#6366F1"
                          fillOpacity={0.1}
                          activeDot={false}
                          isAnimationActive={false}
                        />
                      </>
                    )}
                  </ComposedChart>
                </ResponsiveContainer>
              </div>

              {/* Sub-chart for RSI */}
              {showSubChart === "rsi" && (
                <div style={{ height: subChartHeight, marginTop: 20 }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={chartData}
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                      <XAxis
                        dataKey="date"
                        stroke="#6B7280"
                        tick={{ fontSize: 10 }}
                      />
                      <YAxis
                        domain={[0, 100]}
                        tickCount={6}
                        stroke="#6B7280"
                        tick={{ fontSize: 10 }}
                      />
                      <ReferenceLine y={70} stroke="#ef4444" strokeDasharray="3 3" />
                      <ReferenceLine y={30} stroke="#10B981" strokeDasharray="3 3" />
                      <Tooltip />
                      <Line
                        type="monotone"
                        dataKey="displayRsi"
                        stroke="#ea580c"
                        strokeWidth={2}
                        dot={false}
                        name="RSI"
                        isAnimationActive={false}
                      />
                      <Legend />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              )}

              {/* Sub-chart for MACD */}
              {showSubChart === "macd" && (
                <div style={{ height: subChartHeight, marginTop: 20 }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <ComposedChart
                      data={chartData}
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                      <XAxis
                        dataKey="date"
                        stroke="#6B7280"
                        tick={{ fontSize: 10 }}
                      />
                      <YAxis
                        stroke="#6B7280"
                        tick={{ fontSize: 10 }}
                      />
                      <Tooltip />
                      <Legend />
                      <Bar
                        dataKey="histogram"
                        name="Histogram"
                        isAnimationActive={false}
                        fillOpacity={0.8}
                      >
                        {chartData.map((entry, index) => (
                          <Cell
                            key={`cell-${index}`}
                            fill={entry.histogram >= 0 ? "#10B981" : "#ef4444"}
                          />
                        ))}
                      </Bar>
                      <Line
                        type="monotone"
                        dataKey="macd"
                        stroke="#16a34a"
                        strokeWidth={2}
                        dot={false}
                        name="MACD"
                        isAnimationActive={false}
                      />
                      <Line
                        type="monotone"
                        dataKey="signal"
                        stroke="#ef4444"
                        strokeWidth={2}
                        dot={false}
                        name="Signal"
                        isAnimationActive={false}
                      />
                    </ComposedChart>
                  </ResponsiveContainer>
                </div>
              )}

              {/* Sub-chart for Volume */}
              {showSubChart === "volume" && (
                <div style={{ height: subChartHeight, marginTop: 20 }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={chartData}
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                      <XAxis
                        dataKey="date"
                        stroke="#6B7280"
                        tick={{ fontSize: 10 }}
                      />
                      <YAxis
                        stroke="#6B7280"
                        tick={{ fontSize: 10 }}
                        tickFormatter={(value) => value.toLocaleString()}
                      />
                      <Tooltip />
                      <Legend />
                      <Bar
                        dataKey="volume"
                        fill="#64748b"
                        name="Volume"
                        isAnimationActive={false}
                      />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
