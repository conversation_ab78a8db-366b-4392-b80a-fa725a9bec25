
import { cacheResponse, handleApiError } from "../coinGeckoClient";
import { ProtocolHealth } from "./types";

// Fetch protocol health metrics
export const fetchProtocolHealthData = async (): Promise<ProtocolHealth[]> => {
  try {
    // In real app, would fetch from DefiLlama, Dune Analytics, etc.
    // For now, use simulated data based on actual protocols
    
    const protocolData = [
      { name: "Aave", category: "Lending", baseRisk: 2, userBase: 450000 },
      { name: "Compound", category: "Lending", baseRisk: 3, userBase: 320000 },
      { name: "Uniswap", category: "DEX", baseRisk: 2, userBase: 1200000 },
      { name: "Curve", category: "DEX", baseRisk: 3, userBase: 380000 },
      { name: "MakerDAO", category: "CDP", baseRisk: 2, userBase: 290000 },
      { name: "Lido", category: "Liquid Staking", baseRisk: 3, userBase: 850000 },
      { name: "dYdX", category: "Derivatives", baseRisk: 4, userBase: 210000 },
      { name: "Convex", category: "Yield", baseRisk: 4, userBase: 180000 }
    ];
    
    // Generate TVL trend for last 30 days
    const generateTVLTrend = (baseTVL: number) => {
      const days = 30;
      const trend = [];
      let currentTVL = baseTVL;
      
      for (let i = 0; i < days; i++) {
        const date = new Date();
        date.setDate(date.getDate() - (days - i));
        
        // Random daily change with slight upward bias
        const dailyChange = (Math.random() * 0.06 - 0.025) * currentTVL;
        currentTVL += dailyChange;
        
        trend.push({
          date: date.toISOString().split('T')[0],
          tvl: Math.max(0, currentTVL)
        });
      }
      
      return trend;
    };
    
    // Generate user growth trend
    const generateUserTrend = (baseUsers: number) => {
      const days = 30;
      const trend = [];
      let currentUsers = baseUsers * 0.9; // Start a bit lower than current
      
      for (let i = 0; i < days; i++) {
        const date = new Date();
        date.setDate(date.getDate() - (days - i));
        
        // Random daily change with growth bias
        const dailyChange = (Math.random() * 0.02 + 0.002) * currentUsers;
        currentUsers += dailyChange;
        
        trend.push({
          date: date.toISOString().split('T')[0],
          users: Math.floor(currentUsers)
        });
      }
      
      return trend;
    };
    
    // Generate realistic audit data
    const generateAudits = (protocolName: string, riskLevel: number) => {
      const auditFirms = ["ChainSecurity", "OpenZeppelin", "Trail of Bits", "Certik", "PeckShield", "Quantstamp"];
      const auditCount = Math.max(1, 5 - riskLevel);
      const audits = [];
      
      const now = new Date();
      
      for (let i = 0; i < auditCount; i++) {
        const monthsAgo = Math.floor(Math.random() * 18) + 1;
        const auditDate = new Date();
        auditDate.setMonth(now.getMonth() - monthsAgo);
        
        const issuesScale = riskLevel - 1; // Higher risk means more issues found
        
        audits.push({
          firm: auditFirms[i % auditFirms.length],
          date: auditDate.toISOString().split('T')[0],
          status: "Completed",
          issues: {
            critical: Math.round(Math.random() * issuesScale * 0.5),
            high: Math.round(Math.random() * issuesScale + issuesScale * 0.5),
            medium: Math.round(Math.random() * 3 + issuesScale),
            low: Math.round(Math.random() * 5 + issuesScale * 1.5)
          }
        });
      }
      
      return audits;
    };
    
    // Generate exploit history if any
    const generateExploits = (riskLevel: number) => {
      const exploits = [];
      const hasExploit = riskLevel > 2 && Math.random() > 0.7;
      
      if (hasExploit) {
        const monthsAgo = Math.floor(Math.random() * 24) + 1;
        const exploitDate = new Date();
        exploitDate.setMonth(exploitDate.getMonth() - monthsAgo);
        
        exploits.push({
          date: exploitDate.toISOString().split('T')[0],
          description: "Smart contract vulnerability exploited",
          amount: Math.round(Math.random() * 10 + 1) * 1000000,
          fixed: true
        });
      }
      
      return exploits;
    };
    
    // Build comprehensive health data
    const healthData: ProtocolHealth[] = protocolData.map((protocol, index) => {
      const tvlBase = (10 - index) * 500000000 + Math.random() * **********;
      const tvlTrend = generateTVLTrend(tvlBase);
      const currentTvl = tvlTrend[tvlTrend.length - 1].tvl;
      const previousTvl = tvlTrend[tvlTrend.length - 2].tvl;
      const tvlChange = ((currentTvl - previousTvl) / previousTvl) * 100;
      
      const userTrend = generateUserTrend(protocol.userBase);
      const currentUsers = userTrend[userTrend.length - 1].users;
      const previousUsers = userTrend[userTrend.length - 8].users;
      const userGrowth = ((currentUsers - previousUsers) / previousUsers) * 100;
      
      const securityScore = 10 - protocol.baseRisk;
      const audits = generateAudits(protocol.name, protocol.baseRisk);
      const exploits = generateExploits(protocol.baseRisk);
      
      return {
        id: protocol.name.toLowerCase().replace(/\s/g, '-'),
        name: protocol.name,
        category: protocol.category,
        tvl: currentTvl,
        tvlChange24h: tvlChange,
        tvlTrend,
        userCount: currentUsers,
        userGrowth,
        userTrend,
        securityScore,
        audits,
        bugBounty: securityScore > 6,
        exploits
      };
    });
    
    cacheResponse("protocol_health", healthData);
    return healthData;
    
  } catch (error) {
    return handleApiError(error, {
      key: "protocol_health",
      data: []
    });
  }
};
