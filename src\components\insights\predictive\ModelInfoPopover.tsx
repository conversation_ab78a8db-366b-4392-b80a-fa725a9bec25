
import { PredictionModel } from "@/types/aiInsights";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { HelpCircle } from "lucide-react";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";

interface ModelInfoPopoverProps {
  modelDetails: PredictionModel['modelDetails'];
}

export function ModelInfoPopover({ modelDetails }: ModelInfoPopoverProps) {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="outline" size="sm">
          <HelpCircle className="h-4 w-4 mr-1" />
          Model Info
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80">
        <div className="grid gap-2">
          <h4 className="font-medium">{modelDetails.name}</h4>
          <div className="text-sm text-muted-foreground">
            <p className="mb-2">This model uses the following features:</p>
            <ul className="list-disc pl-4 space-y-1">
              {modelDetails.features.map((feature, index) => (
                <li key={index}>{feature}</li>
              ))}
            </ul>
            <p className="mt-2 text-xs">
              Last updated: {new Date(modelDetails.lastUpdated).toLocaleString()}
            </p>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}
