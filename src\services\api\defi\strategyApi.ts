
import { coinGeckoAxios, cacheResponse, handleApiError } from "../coinGeckoClient";
import { Strategy, Allocation } from "./types";

// Generate investment strategies based on real-time data
export const generateInvestmentStrategies = async (): Promise<Strategy[]> => {
  try {
    // Get top assets from CoinGecko
    const topCoinsResponse = await coinGeckoAxios.get("/coins/markets", {
      params: {
        vs_currency: 'usd',
        order: 'market_cap_desc',
        per_page: 20,
        page: 1,
        sparkline: false
      }
    });
    
    // Get stablecoins
    const stablecoinsResponse = await coinGeckoAxios.get("/coins/markets", {
      params: {
        vs_currency: 'usd',
        category: 'stablecoins',
        order: 'market_cap_desc',
        per_page: 5,
        page: 1,
        sparkline: false
      }
    });
    
    // Generate color for allocation chart
    const getColor = (index: number) => {
      const colors = [
        "#B6509E", "#00D395", "#875FE4", "#FF007A", "#1E40AF", 
        "#60A5FA", "#7DD3FC", "#3B82F6", "#2563EB", "#FF5A5A"
      ];
      return colors[index % colors.length];
    };
    
    // Low risk strategy - mostly stablecoins with some blue chips
    const lowRiskAllocation: Allocation[] = [
      ...stablecoinsResponse.data.slice(0, 3).map((coin: any, index: number) => ({
        name: coin.name,
        percentage: index === 0 ? 40 : 20,
        color: getColor(index)
      })),
      ...topCoinsResponse.data.slice(0, 2).map((coin: any, index: number) => ({
        name: coin.name,
        percentage: 10,
        color: getColor(index + 3)
      }))
    ];
    
    // Medium risk - balanced portfolio
    const mediumRiskAllocation: Allocation[] = topCoinsResponse.data
      .slice(0, 4)
      .map((coin: any, index: number) => ({
        name: coin.name,
        percentage: index === 0 ? 40 : index === 1 ? 30 : 15,
        color: getColor(index)
      }));
    
    // High risk - smaller caps with higher growth potential
    const highRiskAllocation: Allocation[] = [
      ...topCoinsResponse.data.slice(0, 2).map((coin: any, index: number) => ({
        name: coin.name,
        percentage: 20,
        color: getColor(index)
      })),
      ...topCoinsResponse.data.slice(5, 9).map((coin: any, index: number) => ({
        name: coin.name,
        percentage: 15,
        color: getColor(index + 2)
      }))
    ];
    
    const strategies: Strategy[] = [
      {
        id: "strat-1",
        name: "Conservative Yield Strategy",
        description: "Low-risk strategy focused on stablecoin yield and blue-chip cryptocurrencies",
        riskLevel: "Low",
        expectedApy: 5.2,
        timeframe: "6+ months",
        protocolCount: 3,
        allocation: lowRiskAllocation
      },
      {
        id: "strat-2",
        name: "Balanced Growth Portfolio",
        description: "Mix of established cryptocurrencies optimized for moderate growth and reasonable volatility",
        riskLevel: "Medium",
        expectedApy: 12.8,
        timeframe: "3-12 months",
        protocolCount: 4,
        allocation: mediumRiskAllocation
      },
      {
        id: "strat-3",
        name: "Aggressive Growth Strategy",
        description: "High potential return strategy focused on emerging projects and sector rotation",
        riskLevel: "High",
        expectedApy: 28.5,
        timeframe: "1-3 months",
        protocolCount: 5,
        allocation: highRiskAllocation
      }
    ];
    
    cacheResponse("defi_strategies", strategies);
    return strategies;
  } catch (error) {
    return handleApiError(error, {
      key: "defi_strategies",
      data: []
    });
  }
};
