
import { useState } from "react";
import { NewsItem } from "@/hooks/useNewsSentiment";
import { Card, CardContent } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Search, Globe, TrendingUp, TrendingDown, AlertTriangle } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";

interface NewsAggregatorProps {
  data: NewsItem[];
  isLoading: boolean;
}

export default function NewsAggregator({ data, isLoading }: NewsAggregatorProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedSource, setSelectedSource] = useState<string | null>(null);
  const [selectedAsset, setSelectedAsset] = useState<string | null>(null);

  // Extract unique sources and assets
  const sources = [...new Set(data.map(item => item.source))];
  const assets = [...new Set(data.flatMap(item => item.relatedAssets))];

  // Filter news items
  const filteredNews = data.filter(item => {
    const matchesSearch = searchQuery === "" || 
      item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.summary.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesSource = selectedSource === null || item.source === selectedSource;
    const matchesAsset = selectedAsset === null || item.relatedAssets.includes(selectedAsset);
    
    return matchesSearch && matchesSource && matchesAsset;
  });

  // Get sentiment icon based on sentiment value
  const getSentimentIcon = (sentiment: 'positive' | 'neutral' | 'negative') => {
    switch (sentiment) {
      case 'positive':
        return <TrendingUp className="h-4 w-4 text-green-500" />;
      case 'negative':
        return <TrendingDown className="h-4 w-4 text-red-500" />;
      default:
        return <Globe className="h-4 w-4 text-gray-500" />;
    }
  };

  // Get impact badge based on impact value
  const getImpactBadge = (impact: 'high' | 'medium' | 'low') => {
    switch (impact) {
      case 'high':
        return <Badge variant="destructive" className="ml-2">High Impact</Badge>;
      case 'medium':
        return <Badge variant="outline" className="ml-2">Medium Impact</Badge>;
      case 'low':
        return <Badge variant="outline" className="ml-2 opacity-70">Low Impact</Badge>;
      default:
        return null;
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3, 4].map(i => (
          <Card key={i} className="overflow-hidden">
            <CardContent className="p-0">
              <div className="p-4">
                <Skeleton className="h-6 w-3/4 mb-2" />
                <Skeleton className="h-4 w-1/4 mb-4" />
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-5/6" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row gap-3 mb-6">
        {/* Search input */}
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <input
            type="text"
            placeholder="Search news..."
            className="pl-9 pr-4 py-2 w-full rounded-md border border-border focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary transition-colors"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        
        {/* Filter options */}
        <div className="flex flex-wrap gap-2">
          <select 
            className="px-3 py-2 rounded-md border border-border focus:border-primary focus:outline-none bg-background"
            value={selectedSource || ""}
            onChange={(e) => setSelectedSource(e.target.value || null)}
          >
            <option value="">All Sources</option>
            {sources.map(source => (
              <option key={source} value={source}>{source}</option>
            ))}
          </select>
          
          <select 
            className="px-3 py-2 rounded-md border border-border focus:border-primary focus:outline-none bg-background"
            value={selectedAsset || ""}
            onChange={(e) => setSelectedAsset(e.target.value || null)}
          >
            <option value="">All Assets</option>
            {assets.map(asset => (
              <option key={asset} value={asset}>{asset}</option>
            ))}
          </select>
        </div>
      </div>

      <Tabs defaultValue="all">
        <TabsList className="mb-4">
          <TabsTrigger value="all">All News</TabsTrigger>
          <TabsTrigger value="positive">Positive</TabsTrigger>
          <TabsTrigger value="neutral">Neutral</TabsTrigger>
          <TabsTrigger value="negative">Negative</TabsTrigger>
        </TabsList>
        
        <TabsContent value="all" className="space-y-4">
          {renderNewsItems(filteredNews)}
        </TabsContent>
        
        <TabsContent value="positive" className="space-y-4">
          {renderNewsItems(filteredNews.filter(item => item.sentiment === 'positive'))}
        </TabsContent>
        
        <TabsContent value="neutral" className="space-y-4">
          {renderNewsItems(filteredNews.filter(item => item.sentiment === 'neutral'))}
        </TabsContent>
        
        <TabsContent value="negative" className="space-y-4">
          {renderNewsItems(filteredNews.filter(item => item.sentiment === 'negative'))}
        </TabsContent>
      </Tabs>

      {filteredNews.length === 0 && (
        <div className="flex flex-col items-center justify-center py-12">
          <AlertTriangle className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium">No results found</h3>
          <p className="text-muted-foreground">Try adjusting your search or filters</p>
        </div>
      )}
    </div>
  );

  function renderNewsItems(items: NewsItem[]) {
    return items.map(item => (
      <Card key={item.id} className="overflow-hidden">
        <CardContent className="p-0">
          <div className="p-4">
            <div className="flex items-start justify-between">
              <div>
                <h3 className="font-semibold text-lg">{item.title}</h3>
                <div className="flex items-center text-sm text-muted-foreground mt-1">
                  <span>{item.source}</span>
                  <span className="mx-2">•</span>
                  <span>{new Date(item.published).toLocaleDateString()}</span>
                  <span className="mx-2">•</span>
                  <div className="flex items-center">
                    {getSentimentIcon(item.sentiment)}
                    <span className="ml-1 capitalize">{item.sentiment}</span>
                  </div>
                  {getImpactBadge(item.impact)}
                </div>
              </div>
              <div className="flex gap-1">
                {item.relatedAssets.map(asset => (
                  <Badge key={asset} variant="secondary">{asset}</Badge>
                ))}
              </div>
            </div>
            <p className="mt-3 text-muted-foreground">{item.summary}</p>
            <div className="mt-3">
              <a 
                href={item.url} 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-sm text-primary hover:underline flex items-center"
              >
                Read more <Globe className="ml-1 h-3 w-3" />
              </a>
            </div>
          </div>
        </CardContent>
      </Card>
    ));
  }
}
