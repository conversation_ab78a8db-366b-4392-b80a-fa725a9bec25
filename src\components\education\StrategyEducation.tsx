
import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Separator } from '@/components/ui/separator';
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import { StrategyBuilder } from './StrategyBuilder';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { ArrowRight, TrendingUp, TrendingDown, Clock, ArrowUpDown, ArrowDownCircle, ArrowUpCircle } from 'lucide-react';

export default function StrategyEducation({ loading = false }: { loading?: boolean }) {
  const [activeTab, setActiveTab] = useState("overview");
  const [selectedStrategy, setSelectedStrategy] = useState<string | null>(null);
  const [quizStarted, setQuizStarted] = useState(false);
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [score, setScore] = useState(0);
  const [quizCompleted, setQuizCompleted] = useState(false);

  // Sample historical performance data for strategies
  const strategyPerformanceData = {
    "dca": [
      { month: 'Jan', value: 1000, marketValue: 1000 },
      { month: 'Feb', value: 1050, marketValue: 1020 },
      { month: 'Mar', value: 1080, marketValue: 980 },
      { month: 'Apr', value: 1120, marketValue: 950 },
      { month: 'May', value: 1160, marketValue: 1050 },
      { month: 'Jun', value: 1210, marketValue: 1150 },
      { month: 'Jul', value: 1260, marketValue: 1250 },
      { month: 'Aug', value: 1320, marketValue: 1200 },
      { month: 'Sep', value: 1380, marketValue: 1100 },
      { month: 'Oct', value: 1450, marketValue: 1050 },
      { month: 'Nov', value: 1520, marketValue: 1150 },
      { month: 'Dec', value: 1600, marketValue: 1300 },
    ],
    "value": [
      { month: 'Jan', value: 1000, marketValue: 1000 },
      { month: 'Feb', value: 980, marketValue: 1020 },
      { month: 'Mar', value: 1030, marketValue: 980 },
      { month: 'Apr', value: 1080, marketValue: 950 },
      { month: 'May', value: 1150, marketValue: 1050 },
      { month: 'Jun', value: 1200, marketValue: 1150 },
      { month: 'Jul', value: 1280, marketValue: 1250 },
      { month: 'Aug', value: 1350, marketValue: 1200 },
      { month: 'Sep', value: 1430, marketValue: 1100 },
      { month: 'Oct', value: 1520, marketValue: 1050 },
      { month: 'Nov', value: 1590, marketValue: 1150 },
      { month: 'Dec', value: 1680, marketValue: 1300 },
    ],
    "momentum": [
      { month: 'Jan', value: 1000, marketValue: 1000 },
      { month: 'Feb', value: 1040, marketValue: 1020 },
      { month: 'Mar', value: 1010, marketValue: 980 },
      { month: 'Apr', value: 950, marketValue: 950 },
      { month: 'May', value: 1020, marketValue: 1050 },
      { month: 'Jun', value: 1180, marketValue: 1150 },
      { month: 'Jul', value: 1320, marketValue: 1250 },
      { month: 'Aug', value: 1350, marketValue: 1200 },
      { month: 'Sep', value: 1280, marketValue: 1100 },
      { month: 'Oct', value: 1220, marketValue: 1050 },
      { month: 'Nov', value: 1310, marketValue: 1150 },
      { month: 'Dec', value: 1520, marketValue: 1300 },
    ],
    "hedging": [
      { month: 'Jan', value: 1000, marketValue: 1000 },
      { month: 'Feb', value: 1010, marketValue: 1020 },
      { month: 'Mar', value: 1000, marketValue: 980 },
      { month: 'Apr', value: 980, marketValue: 950 },
      { month: 'May', value: 1020, marketValue: 1050 },
      { month: 'Jun', value: 1080, marketValue: 1150 },
      { month: 'Jul', value: 1150, marketValue: 1250 },
      { month: 'Aug', value: 1180, marketValue: 1200 },
      { month: 'Sep', value: 1200, marketValue: 1100 },
      { month: 'Oct', value: 1230, marketValue: 1050 },
      { month: 'Nov', value: 1280, marketValue: 1150 },
      { month: 'Dec', value: 1340, marketValue: 1300 },
    ]
  };

  // Quiz questions
  const quizQuestions = [
    {
      question: "What is Dollar-Cost Averaging (DCA)?",
      options: [
        "Investing all your money at once when the market is low",
        "Investing a fixed amount of money at regular intervals, regardless of price",
        "Only buying assets when they reach a specific target price",
        "Gradually selling your assets over time to maximize profits"
      ],
      correctAnswer: 1,
      explanation: "Dollar-Cost Averaging involves investing a fixed amount at regular intervals regardless of market conditions. This strategy reduces the impact of volatility and the risk of making poorly timed investment decisions."
    },
    {
      question: "Which investment strategy primarily focuses on assets that are priced below their intrinsic value?",
      options: [
        "Momentum investing",
        "Growth investing",
        "Value investing",
        "Index investing"
      ],
      correctAnswer: 2,
      explanation: "Value investing focuses on identifying and investing in assets that appear to be priced below their intrinsic value. Value investors look for strong fundamentals and believe the market has undervalued the asset."
    },
    {
      question: "What is the primary goal of portfolio diversification?",
      options: [
        "To maximize returns by concentrating investments in the best-performing assets",
        "To reduce risk by spreading investments across various asset classes",
        "To minimize taxes by investing in tax-advantaged accounts",
        "To ensure your portfolio only contains blue-chip stocks"
      ],
      correctAnswer: 1,
      explanation: "Diversification aims to reduce risk by spreading investments across different asset classes, sectors, or geographic regions. This helps protect your portfolio from severe losses if one particular investment performs poorly."
    },
    {
      question: "What strategy involves buying assets that have been performing well recently?",
      options: [
        "Contrarian investing",
        "Momentum investing",
        "Value investing",
        "Income investing"
      ],
      correctAnswer: 1,
      explanation: "Momentum investing is based on the idea that assets that have performed well in the recent past will continue to perform well in the near future. Investors following this strategy buy assets showing upward price momentum."
    },
    {
      question: "Which risk management strategy involves opening positions that would offset potential losses in your core holdings?",
      options: [
        "Stop-loss orders",
        "Portfolio rebalancing",
        "Hedging",
        "Asset allocation"
      ],
      correctAnswer: 2,
      explanation: "Hedging involves taking an offsetting position to protect against potential losses in your main investment. For example, buying put options on stocks you own, or diversifying with assets that typically move in opposite directions."
    }
  ];

  if (loading) {
    return (
      <Card className="animate-pulse">
        <CardHeader className="pb-2">
          <div className="h-5 w-3/4 bg-muted rounded mb-1"></div>
          <div className="h-4 w-full bg-muted rounded"></div>
        </CardHeader>
        <CardContent>
          <div className="h-[400px] bg-muted rounded mb-2"></div>
        </CardContent>
      </Card>
    );
  }

  const handleStrategySelect = (strategy: string) => {
    setSelectedStrategy(strategy);
    toast.info(`${strategy.charAt(0).toUpperCase() + strategy.slice(1)} strategy selected`);
  };

  const handleQuizStart = () => {
    setQuizStarted(true);
    setCurrentQuestion(0);
    setScore(0);
    setQuizCompleted(false);
    toast.info("Investment Strategy Quiz started! Test your knowledge.");
  };

  const handleAnswerSubmit = (selectedAnswer: number) => {
    const isCorrect = selectedAnswer === quizQuestions[currentQuestion].correctAnswer;

    if (isCorrect) {
      setScore(score + 1);
      toast.success("Correct answer!");
    } else {
      toast.error("Not quite right!");
    }

    // Move to next question or end quiz
    if (currentQuestion < quizQuestions.length - 1) {
      setTimeout(() => {
        setCurrentQuestion(currentQuestion + 1);
      }, 1500);
    } else {
      setTimeout(() => {
        setQuizCompleted(true);
      }, 1500);
    }
  };

  // Quiz component
  if (quizStarted) {
    return (
      <Card className="max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle>Investment Strategy Quiz</CardTitle>
          <CardDescription>
            {!quizCompleted
              ? `Question ${currentQuestion + 1} of ${quizQuestions.length}`
              : "Quiz Completed"}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {!quizCompleted ? (
            <div className="space-y-4">
              <div className="text-xl font-medium">{quizQuestions[currentQuestion].question}</div>
              <div className="space-y-2">
                {quizQuestions[currentQuestion].options.map((option, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    className="w-full justify-start text-left h-auto py-3 px-4"
                    onClick={() => handleAnswerSubmit(index)}
                  >
                    {option}
                  </Button>
                ))}
              </div>
              <div className="pt-4">
                <p className="text-muted-foreground text-sm">
                  Select the best answer. Your score will be calculated at the end.
                </p>
              </div>
            </div>
          ) : (
            <div className="space-y-6 py-4">
              <div className="text-center">
                <div className="text-3xl font-bold mb-2">
                  Your Score: {score}/{quizQuestions.length}
                </div>
                <div className="text-muted-foreground">
                  {score === quizQuestions.length ?
                    "Perfect! You're an investment strategy expert!" :
                    score >= quizQuestions.length / 2 ?
                    "Good job! You have a solid understanding of investment strategies." :
                    "Keep learning! Investment concepts take time to master."}
                </div>
              </div>

              <div className="space-y-4 mt-6">
                <h3 className="font-medium text-lg">Review:</h3>
                {quizQuestions.map((q, idx) => (
                  <div key={idx} className="p-4 border rounded-md">
                    <div>
                      <p className="font-medium">{q.question}</p>
                      <p className="text-sm text-muted-foreground mt-1">Correct answer: {q.options[q.correctAnswer]}</p>
                      <p className="text-sm mt-1">{q.explanation}</p>
                    </div>
                  </div>
                ))}
              </div>

              <div className="flex justify-center pt-4">
                <Button onClick={() => setQuizStarted(false)} className="mr-2">
                  Return to Strategies
                </Button>
                <Button onClick={handleQuizStart} variant="outline">
                  Retry Quiz
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Investment Strategy Builder</CardTitle>
          <CardDescription>
            Learn about different investment strategies and test them with historical data
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid grid-cols-4 mb-6">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="strategies">Strategies</TabsTrigger>
              <TabsTrigger value="comparison">Comparison</TabsTrigger>
              <TabsTrigger value="builder">Strategy Builder</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              <section>
                <h3 className="text-lg font-medium mb-2">What are Investment Strategies?</h3>
                <p className="text-muted-foreground">
                  Investment strategies are systematic approaches to capital allocation based on goals, risk tolerance, and time horizons.
                  An effective strategy is essential for navigating the volatile cryptocurrency markets and achieving your financial objectives.
                </p>

                <div className="my-4 p-4 bg-muted rounded-md">
                  <h4 className="font-medium mb-2">Key Elements of an Investment Strategy</h4>
                  <ul className="list-disc pl-5 space-y-1">
                    <li><span className="font-medium">Goals:</span> Clearly defined objectives (growth, income, preservation)</li>
                    <li><span className="font-medium">Time Horizon:</span> Short-term, medium-term, or long-term investment perspective</li>
                    <li><span className="font-medium">Risk Tolerance:</span> Your comfort level with potential losses</li>
                    <li><span className="font-medium">Asset Allocation:</span> How you distribute investments across different asset classes</li>
                    <li><span className="font-medium">Entry and Exit Points:</span> When to buy and when to sell</li>
                    <li><span className="font-medium">Rebalancing:</span> How often to adjust your portfolio to maintain desired allocations</li>
                  </ul>
                </div>
              </section>

              <Separator />

              <section>
                <h3 className="text-lg font-medium mb-2">Why You Need a Strategy</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="p-4 border rounded-md">
                    <div className="flex items-start gap-2">
                      <TrendingDown className="mt-1 text-red-500" size={18} />
                      <div>
                        <h4 className="font-medium mb-1">Emotional Control</h4>
                        <p className="text-sm text-muted-foreground">
                          A well-defined strategy helps eliminate emotional decision-making during market volatility, preventing panic selling or FOMO buying.
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="p-4 border rounded-md">
                    <div className="flex items-start gap-2">
                      <Clock className="mt-1 text-blue-500" size={18} />
                      <div>
                        <h4 className="font-medium mb-1">Time Efficiency</h4>
                        <p className="text-sm text-muted-foreground">
                          Following a strategy reduces the need for constant market monitoring and stress over short-term price movements.
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="p-4 border rounded-md">
                    <div className="flex items-start gap-2">
                      <ArrowUpDown className="mt-1 text-green-500" size={18} />
                      <div>
                        <h4 className="font-medium mb-1">Risk Management</h4>
                        <p className="text-sm text-muted-foreground">
                          Strategies incorporate diversification and position sizing to minimize exposure to any single asset or market event.
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="p-4 border rounded-md">
                    <div className="flex items-start gap-2">
                      <TrendingUp className="mt-1 text-purple-500" size={18} />
                      <div>
                        <h4 className="font-medium mb-1">Measurable Results</h4>
                        <p className="text-sm text-muted-foreground">
                          A consistent approach allows you to evaluate performance and refine your methods over time.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </section>

              <div className="flex justify-center mt-6">
                <Button onClick={() => setActiveTab("strategies")}>
                  Explore Investment Strategies <ArrowRight className="ml-1 h-4 w-4" />
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="strategies" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card
                  className={`cursor-pointer hover:border-primary/50 transition-colors ${selectedStrategy === "dca" ? "border-primary" : ""}`}
                  onClick={() => handleStrategySelect("dca")}
                >
                  <CardHeader>
                    <div className="flex justify-between items-center">
                      <CardTitle className="text-base">Dollar-Cost Averaging (DCA)</CardTitle>
                      <Badge>Beginner-Friendly</Badge>
                    </div>
                    <CardDescription>Systematic, scheduled investments regardless of price</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <p className="text-sm text-muted-foreground">
                      DCA involves investing a fixed amount at regular intervals, regardless of the asset's price.
                      This reduces the impact of volatility and eliminates the need to time the market.
                    </p>
                    <div className="pt-2">
                      <h5 className="text-sm font-medium">Best For:</h5>
                      <ul className="list-disc pl-5 text-sm text-muted-foreground">
                        <li>Long-term investors</li>
                        <li>Beginners</li>
                        <li>Risk-averse individuals</li>
                        <li>Volatile markets</li>
                      </ul>
                    </div>
                  </CardContent>
                </Card>

                <Card
                  className={`cursor-pointer hover:border-primary/50 transition-colors ${selectedStrategy === "value" ? "border-primary" : ""}`}
                  onClick={() => handleStrategySelect("value")}
                >
                  <CardHeader>
                    <div className="flex justify-between items-center">
                      <CardTitle className="text-base">Value Investing</CardTitle>
                      <Badge variant="outline">Intermediate</Badge>
                    </div>
                    <CardDescription>Finding undervalued assets based on fundamentals</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <p className="text-sm text-muted-foreground">
                      Value investing focuses on identifying cryptocurrencies trading below their "intrinsic value" based on
                      fundamentals like utility, adoption metrics, developer activity, and network effects.
                    </p>
                    <div className="pt-2">
                      <h5 className="text-sm font-medium">Best For:</h5>
                      <ul className="list-disc pl-5 text-sm text-muted-foreground">
                        <li>Research-oriented investors</li>
                        <li>Long-term holders</li>
                        <li>Those with fundamental analysis skills</li>
                        <li>Contrarian thinkers</li>
                      </ul>
                    </div>
                  </CardContent>
                </Card>

                <Card
                  className={`cursor-pointer hover:border-primary/50 transition-colors ${selectedStrategy === "momentum" ? "border-primary" : ""}`}
                  onClick={() => handleStrategySelect("momentum")}
                >
                  <CardHeader>
                    <div className="flex justify-between items-center">
                      <CardTitle className="text-base">Momentum Investing</CardTitle>
                      <Badge variant="secondary">Advanced</Badge>
                    </div>
                    <CardDescription>Following market trends and momentum</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <p className="text-sm text-muted-foreground">
                      Momentum investors buy assets that have shown upward price movement, based on the belief that assets
                      that have performed well will continue to perform well in the short to medium term.
                    </p>
                    <div className="pt-2">
                      <h5 className="text-sm font-medium">Best For:</h5>
                      <ul className="list-disc pl-5 text-sm text-muted-foreground">
                        <li>Active traders</li>
                        <li>Technical analysis enthusiasts</li>
                        <li>Strong trending markets</li>
                        <li>Risk-tolerant investors</li>
                      </ul>
                    </div>
                  </CardContent>
                </Card>

                <Card
                  className={`cursor-pointer hover:border-primary/50 transition-colors ${selectedStrategy === "hedging" ? "border-primary" : ""}`}
                  onClick={() => handleStrategySelect("hedging")}
                >
                  <CardHeader>
                    <div className="flex justify-between items-center">
                      <CardTitle className="text-base">Hedging Strategies</CardTitle>
                      <Badge variant="secondary">Advanced</Badge>
                    </div>
                    <CardDescription>Protection against downside risk</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <p className="text-sm text-muted-foreground">
                      Hedging involves taking opposing positions to reduce risk exposure. In crypto, this might mean
                      using options, futures, or inversely correlated assets to protect against potential losses.
                    </p>
                    <div className="pt-2">
                      <h5 className="text-sm font-medium">Best For:</h5>
                      <ul className="list-disc pl-5 text-sm text-muted-foreground">
                        <li>Large portfolio holders</li>
                        <li>Conservative investors</li>
                        <li>High volatility periods</li>
                        <li>Institutional investors</li>
                      </ul>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {selectedStrategy && (
                <div className="mt-6">
                  <h3 className="text-lg font-medium mb-4 capitalize">{selectedStrategy} Strategy Details</h3>

                  {selectedStrategy === "dca" && (
                    <div className="space-y-4">
                      <p className="text-muted-foreground">
                        Dollar-cost averaging eliminates the stress of timing the market by investing a fixed amount at regular intervals.
                        This allows you to purchase more of an asset when prices are low and less when prices are high, potentially
                        lowering your average purchase price over time.
                      </p>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                        <div className="p-4 border rounded-md">
                          <h4 className="font-medium mb-2">Implementation Steps</h4>
                          <ol className="list-decimal pl-5 space-y-1 text-muted-foreground">
                            <li>Determine your total investment amount</li>
                            <li>Choose your investment frequency (weekly, monthly, etc.)</li>
                            <li>Calculate how much to invest each period</li>
                            <li>Set up automatic purchases if possible</li>
                            <li>Stick to your schedule regardless of market conditions</li>
                            <li>Periodically review and adjust your strategy as needed</li>
                          </ol>
                        </div>

                        <div className="p-4 border rounded-md">
                          <h4 className="font-medium mb-2">Pros & Cons</h4>
                          <div className="space-y-3">
                            <div>
                              <div className="flex items-center gap-1">
                                <ArrowUpCircle className="text-green-500" size={16} />
                                <h5 className="font-medium text-sm">Pros</h5>
                              </div>
                              <ul className="list-disc pl-5 text-sm text-muted-foreground">
                                <li>Reduces impact of volatility</li>
                                <li>Eliminates need for market timing</li>
                                <li>Emotionally easier during downturns</li>
                                <li>Simple to automate and maintain</li>
                              </ul>
                            </div>
                            <div>
                              <div className="flex items-center gap-1">
                                <ArrowDownCircle className="text-red-500" size={16} />
                                <h5 className="font-medium text-sm">Cons</h5>
                              </div>
                              <ul className="list-disc pl-5 text-sm text-muted-foreground">
                                <li>May underperform lump-sum in bull markets</li>
                                <li>Requires discipline over long periods</li>
                                <li>Transaction fees can add up</li>
                                <li>May miss opportunities for tactical allocation</li>
                              </ul>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="h-80 mt-6">
                        <h4 className="font-medium mb-3">Historical Performance</h4>
                        <ResponsiveContainer width="100%" height="100%">
                          <LineChart data={strategyPerformanceData.dca}>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="month" />
                            <YAxis />
                            <Tooltip />
                            <Legend />
                            <Line type="monotone" dataKey="value" stroke="#8884d8" name="DCA Portfolio" />
                            <Line type="monotone" dataKey="marketValue" stroke="#82ca9d" name="Market Average" />
                          </LineChart>
                        </ResponsiveContainer>
                      </div>
                    </div>
                  )}

                  {selectedStrategy === "value" && (
                    <div className="space-y-4">
                      <p className="text-muted-foreground">
                        Value investing in crypto focuses on finding projects that are undervalued relative to their
                        long-term potential. This requires thorough analysis of fundamentals, utility, adoption metrics,
                        and the strength of the development team and community.
                      </p>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                        <div className="p-4 border rounded-md">
                          <h4 className="font-medium mb-2">Evaluation Metrics</h4>
                          <ul className="list-disc pl-5 space-y-1 text-muted-foreground">
                            <li>Network value to transactions ratio (NVT)</li>
                            <li>Developer activity and roadmap progress</li>
                            <li>User adoption and growth metrics</li>
                            <li>Token economics and distribution</li>
                            <li>Competitive positioning in the ecosystem</li>
                            <li>Real-world utility and use cases</li>
                          </ul>
                        </div>

                        <div className="p-4 border rounded-md">
                          <h4 className="font-medium mb-2">Pros & Cons</h4>
                          <div className="space-y-3">
                            <div>
                              <div className="flex items-center gap-1">
                                <ArrowUpCircle className="text-green-500" size={16} />
                                <h5 className="font-medium text-sm">Pros</h5>
                              </div>
                              <ul className="list-disc pl-5 text-sm text-muted-foreground">
                                <li>Can identify promising projects early</li>
                                <li>Based on fundamentals, not market sentiment</li>
                                <li>Often aligns with long-term innovation trends</li>
                                <li>May outperform during market corrections</li>
                              </ul>
                            </div>
                            <div>
                              <div className="flex items-center gap-1">
                                <ArrowDownCircle className="text-red-500" size={16} />
                                <h5 className="font-medium text-sm">Cons</h5>
                              </div>
                              <ul className="list-disc pl-5 text-sm text-muted-foreground">
                                <li>Requires in-depth research and analysis</li>
                                <li>Undervalued assets can remain undervalued</li>
                                <li>Challenging in a nascent, speculative market</li>
                                <li>May miss momentum-driven market rallies</li>
                              </ul>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="h-80 mt-6">
                        <h4 className="font-medium mb-3">Historical Performance</h4>
                        <ResponsiveContainer width="100%" height="100%">
                          <LineChart data={strategyPerformanceData.value}>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="month" />
                            <YAxis />
                            <Tooltip />
                            <Legend />
                            <Line type="monotone" dataKey="value" stroke="#8884d8" name="Value Portfolio" />
                            <Line type="monotone" dataKey="marketValue" stroke="#82ca9d" name="Market Average" />
                          </LineChart>
                        </ResponsiveContainer>
                      </div>
                    </div>
                  )}

                  {selectedStrategy === "momentum" && (
                    <div className="space-y-4">
                      <p className="text-muted-foreground">
                        Momentum investing capitalizes on the continuation of existing market trends. This strategy
                        involves buying assets showing strong upward price movement and selling those with downward trends.
                        It's more active and requires regular portfolio adjustments.
                      </p>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                        <div className="p-4 border rounded-md">
                          <h4 className="font-medium mb-2">Key Technical Indicators</h4>
                          <ul className="list-disc pl-5 space-y-1 text-muted-foreground">
                            <li>Relative Strength Index (RSI)</li>
                            <li>Moving Average Convergence Divergence (MACD)</li>
                            <li>Moving Averages (50-day, 200-day)</li>
                            <li>Volume indicators and trends</li>
                            <li>Market sentiment metrics</li>
                            <li>On-chain activity indicators</li>
                          </ul>
                        </div>

                        <div className="p-4 border rounded-md">
                          <h4 className="font-medium mb-2">Pros & Cons</h4>
                          <div className="space-y-3">
                            <div>
                              <div className="flex items-center gap-1">
                                <ArrowUpCircle className="text-green-500" size={16} />
                                <h5 className="font-medium text-sm">Pros</h5>
                              </div>
                              <ul className="list-disc pl-5 text-sm text-muted-foreground">
                                <li>Can generate significant returns during bull runs</li>
                                <li>Takes advantage of market psychology</li>
                                <li>Works well in trending markets</li>
                                <li>Systematic approach based on data</li>
                              </ul>
                            </div>
                            <div>
                              <div className="flex items-center gap-1">
                                <ArrowDownCircle className="text-red-500" size={16} />
                                <h5 className="font-medium text-sm">Cons</h5>
                              </div>
                              <ul className="list-disc pl-5 text-sm text-muted-foreground">
                                <li>Higher risk of false signals</li>
                                <li>Requires more frequent trading</li>
                                <li>Can lead to buying at local tops</li>
                                <li>Struggles in choppy or ranging markets</li>
                              </ul>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="h-80 mt-6">
                        <h4 className="font-medium mb-3">Historical Performance</h4>
                        <ResponsiveContainer width="100%" height="100%">
                          <LineChart data={strategyPerformanceData.momentum}>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="month" />
                            <YAxis />
                            <Tooltip />
                            <Legend />
                            <Line type="monotone" dataKey="value" stroke="#8884d8" name="Momentum Portfolio" />
                            <Line type="monotone" dataKey="marketValue" stroke="#82ca9d" name="Market Average" />
                          </LineChart>
                        </ResponsiveContainer>
                      </div>
                    </div>
                  )}

                  {selectedStrategy === "hedging" && (
                    <div className="space-y-4">
                      <p className="text-muted-foreground">
                        Hedging strategies aim to protect your portfolio against downside risk while maintaining exposure to
                        upside potential. This typically involves using derivatives like options and futures, or investing in
                        assets that tend to move in opposite directions to your core holdings.
                      </p>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                        <div className="p-4 border rounded-md">
                          <h4 className="font-medium mb-2">Common Hedging Techniques</h4>
                          <ul className="list-disc pl-5 space-y-1 text-muted-foreground">
                            <li>Put options on portfolio assets</li>
                            <li>Cash allocation for volatility cushion</li>
                            <li>Inverse crypto ETFs or products</li>
                            <li>Diversification across unrelated asset classes</li>
                            <li>Stablecoin position management</li>
                            <li>Strategic use of stop-loss orders</li>
                          </ul>
                        </div>

                        <div className="p-4 border rounded-md">
                          <h4 className="font-medium mb-2">Pros & Cons</h4>
                          <div className="space-y-3">
                            <div>
                              <div className="flex items-center gap-1">
                                <ArrowUpCircle className="text-green-500" size={16} />
                                <h5 className="font-medium text-sm">Pros</h5>
                              </div>
                              <ul className="list-disc pl-5 text-sm text-muted-foreground">
                                <li>Reduces portfolio volatility</li>
                                <li>Provides downside protection</li>
                                <li>Allows maintaining core positions during uncertainty</li>
                                <li>Can improve risk-adjusted returns</li>
                              </ul>
                            </div>
                            <div>
                              <div className="flex items-center gap-1">
                                <ArrowDownCircle className="text-red-500" size={16} />
                                <h5 className="font-medium text-sm">Cons</h5>
                              </div>
                              <ul className="list-disc pl-5 text-sm text-muted-foreground">
                                <li>May reduce overall returns in bull markets</li>
                                <li>Additional costs for hedging instruments</li>
                                <li>Requires more advanced market knowledge</li>
                                <li>Not all crypto assets have hedging instruments</li>
                              </ul>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="h-80 mt-6">
                        <h4 className="font-medium mb-3">Historical Performance</h4>
                        <ResponsiveContainer width="100%" height="100%">
                          <LineChart data={strategyPerformanceData.hedging}>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="month" />
                            <YAxis />
                            <Tooltip />
                            <Legend />
                            <Line type="monotone" dataKey="value" stroke="#8884d8" name="Hedged Portfolio" />
                            <Line type="monotone" dataKey="marketValue" stroke="#82ca9d" name="Market Average" />
                          </LineChart>
                        </ResponsiveContainer>
                      </div>
                    </div>
                  )}
                </div>
              )}

              <div className="flex justify-center mt-6">
                <Button onClick={handleQuizStart}>
                  Take Investment Strategy Quiz <ArrowRight className="ml-1 h-4 w-4" />
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="comparison">
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium mb-2">Strategy Performance Comparison</h3>
                  <p className="text-muted-foreground mb-4">
                    Different strategies perform differently depending on market conditions. This comparison
                    shows how each approach might perform relative to the overall market over time.
                  </p>
                </div>

                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" allowDuplicatedCategory={false} />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Line data={strategyPerformanceData.dca} type="monotone" dataKey="value" name="DCA Strategy" stroke="#8884d8" />
                      <Line data={strategyPerformanceData.value} type="monotone" dataKey="value" name="Value Strategy" stroke="#82ca9d" />
                      <Line data={strategyPerformanceData.momentum} type="monotone" dataKey="value" name="Momentum Strategy" stroke="#ff7300" />
                      <Line data={strategyPerformanceData.hedging} type="monotone" dataKey="value" name="Hedging Strategy" stroke="#0088fe" />
                      <Line data={strategyPerformanceData.dca} type="monotone" dataKey="marketValue" name="Market Average" stroke="#999" />
                    </LineChart>
                  </ResponsiveContainer>
                </div>

                <div className="mt-6">
                  <h3 className="text-lg font-medium mb-4">Strategy Comparison Matrix</h3>
                  <div className="overflow-x-auto">
                    <table className="min-w-full border-collapse">
                      <thead>
                        <tr className="border-b">
                          <th className="py-2 px-4 text-left">Strategy</th>
                          <th className="py-2 px-4 text-left">Risk Level</th>
                          <th className="py-2 px-4 text-left">Time Commitment</th>
                          <th className="py-2 px-4 text-left">Ideal Market</th>
                          <th className="py-2 px-4 text-left">Best For</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr className="border-b">
                          <td className="py-3 px-4 font-medium">Dollar-Cost Averaging</td>
                          <td className="py-3 px-4">
                            <Badge variant="outline" className="bg-green-50">Low</Badge>
                          </td>
                          <td className="py-3 px-4">Minimal</td>
                          <td className="py-3 px-4">Any, but especially volatile</td>
                          <td className="py-3 px-4">Beginners, passive investors</td>
                        </tr>
                        <tr className="border-b">
                          <td className="py-3 px-4 font-medium">Value Investing</td>
                          <td className="py-3 px-4">
                            <Badge variant="outline" className="bg-yellow-50">Medium</Badge>
                          </td>
                          <td className="py-3 px-4">High (research)</td>
                          <td className="py-3 px-4">Correction phases, early bull</td>
                          <td className="py-3 px-4">Researchers, fundamentalists</td>
                        </tr>
                        <tr className="border-b">
                          <td className="py-3 px-4 font-medium">Momentum Investing</td>
                          <td className="py-3 px-4">
                            <Badge variant="outline" className="bg-red-50">High</Badge>
                          </td>
                          <td className="py-3 px-4">High (active)</td>
                          <td className="py-3 px-4">Strong bull or bear trends</td>
                          <td className="py-3 px-4">Active traders, technical analysts</td>
                        </tr>
                        <tr className="border-b">
                          <td className="py-3 px-4 font-medium">Hedging</td>
                          <td className="py-3 px-4">
                            <Badge variant="outline" className="bg-blue-50">Variable</Badge>
                          </td>
                          <td className="py-3 px-4">Medium-High</td>
                          <td className="py-3 px-4">Uncertain, high volatility</td>
                          <td className="py-3 px-4">Experienced investors, institutions</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>

                <div className="mt-8">
                  <h3 className="text-lg font-medium mb-4">Finding the Right Strategy for You</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="p-4 border rounded-md">
                      <h4 className="font-medium mb-2">For Beginners</h4>
                      <p className="text-sm text-muted-foreground">
                        If you're new to crypto investing, start with a simple DCA approach. It's systematic,
                        requires minimal technical knowledge, and helps you build positions while learning.
                      </p>
                    </div>

                    <div className="p-4 border rounded-md">
                      <h4 className="font-medium mb-2">For Intermediate Investors</h4>
                      <p className="text-sm text-muted-foreground">
                        Consider a hybrid approach that combines DCA with some value investing principles.
                        Research fundamentals but still maintain regular investments to reduce timing risk.
                      </p>
                    </div>

                    <div className="p-4 border rounded-md">
                      <h4 className="font-medium mb-2">For Advanced Investors</h4>
                      <p className="text-sm text-muted-foreground">
                        You might incorporate all strategies: a core portfolio using value principles,
                        momentum trading for shorter-term opportunities, and hedging techniques to manage risk.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="builder">
              <StrategyBuilder />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
