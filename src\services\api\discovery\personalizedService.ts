
import { fetchTopCoins } from "../coinMarketData";
import { generateAIResponse } from "../deepSeekClient";
import { cacheResponse, handleApiError } from "../coinGeckoClient";

interface PersonalizedRecommendation {
  coins: any[];
  aiInsights: string;
  riskAssessment: string;
  rationale: string;
}

// Generate personalized recommendations using AI
export const generatePersonalizedRecommendations = async (riskProfile: string): Promise<PersonalizedRecommendation> => {
  try {
    const topCoins = await fetchTopCoins(20);
    
    // Filter coins based on risk profile
    const filteredCoins = filterCoinsByRiskProfile(topCoins, riskProfile);

    // Generate AI insights
    const coinData = filteredCoins.slice(0, 5).map(coin => ({
      name: coin.name,
      symbol: coin.symbol,
      price: coin.current_price,
      change24h: coin.price_change_percentage_24h,
      marketCap: coin.market_cap,
      volume: coin.total_volume
    }));

    const aiPrompt = createPersonalizedPrompt(coinData, riskProfile);
    const aiResponse = await generateAIResponse(aiPrompt, { temperature: 0.3, maxTokens: 800 });
    
    // Parse AI response or provide fallback
    const insights = parseAIInsights(aiResponse, riskProfile);

    const result = {
      coins: filteredCoins.slice(0, 6),
      aiInsights: insights.insights,
      riskAssessment: insights.riskAssessment,
      rationale: insights.rationale
    };

    cacheResponse(`personalized_${riskProfile}`, result);
    return result;
  } catch (error) {
    return handleApiError(error, {
      key: `personalized_${riskProfile}`,
      data: {
        coins: [],
        aiInsights: "Unable to generate personalized insights at this time.",
        riskAssessment: "Risk assessment unavailable.",
        rationale: "Please try again later."
      }
    });
  }
};

// Filter coins based on risk profile
const filterCoinsByRiskProfile = (coins: any[], riskProfile: string): any[] => {
  switch (riskProfile) {
    case "conservative":
      return coins.filter(coin => 
        coin.market_cap_rank <= 20 && 
        Math.abs(coin.price_change_percentage_24h || 0) <= 5
      );
    case "moderate":
      return coins.filter(coin => 
        coin.market_cap_rank <= 50 && 
        Math.abs(coin.price_change_percentage_24h || 0) <= 15
      );
    case "aggressive":
      return coins.filter(coin => 
        coin.market_cap_rank > 20 && 
        Math.abs(coin.price_change_percentage_24h || 0) >= 5
      );
    default:
      return coins.slice(0, 10);
  }
};

// Create AI prompt for personalized recommendations
const createPersonalizedPrompt = (coinData: any[], riskProfile: string): string => {
  return `
    Analyze these cryptocurrency opportunities for a ${riskProfile} risk investor:
    ${JSON.stringify(coinData, null, 2)}
    
    Provide:
    1. Personalized investment insights (2-3 sentences)
    2. Risk assessment for this portfolio mix
    3. Rationale for recommendations based on current market conditions
    
    Focus on actionable insights and be concise.
  `;
};

// Parse AI response into structured insights
const parseAIInsights = (aiResponse: string | null, riskProfile: string) => {
  let insights = "Based on current market analysis, these cryptocurrencies show strong potential.";
  let riskAssessment = `${riskProfile} risk profile selection shows balanced exposure.`;
  let rationale = "Recommendations based on market momentum, development activity, and historical performance.";
  
  if (aiResponse) {
    const lines = aiResponse.split('\n').filter(line => line.trim());
    insights = lines.find(line => line.includes('insights') || line.includes('Insights')) || insights;
    riskAssessment = lines.find(line => line.includes('risk') || line.includes('Risk')) || riskAssessment;
    rationale = lines.find(line => line.includes('rationale') || line.includes('Rationale')) || rationale;
  }

  return { insights, riskAssessment, rationale };
};
