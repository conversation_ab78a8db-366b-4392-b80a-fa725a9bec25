
import { SentimentData } from "@/hooks/useNewsSentiment";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { LineChart, Line, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { TrendingUp, TrendingDown } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";

interface SentimentAnalysisProps {
  data: SentimentData;
  isLoading: boolean;
}

export default function SentimentAnalysis({ data, isLoading }: SentimentAnalysisProps) {
  // Helper function to get color based on sentiment value
  const getSentimentColor = (value: number) => {
    if (value >= 0.3) return "rgb(var(--crypto-positive))";  // Green for positive
    if (value <= -0.3) return "rgb(var(--crypto-negative))"; // Red for negative
    return "rgb(var(--chart-3))";                           // Amber for neutral
  };

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Market Sentiment</CardTitle>
          </CardHeader>
          <CardContent>
            <Skeleton className="h-[200px] w-full" />
          </CardContent>
        </Card>
        <Card className="md:col-span-2">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Sentiment Trend</CardTitle>
          </CardHeader>
          <CardContent>
            <Skeleton className="h-[200px] w-full" />
          </CardContent>
        </Card>
        <Card className="md:col-span-3">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Asset Sentiment Comparison</CardTitle>
          </CardHeader>
          <CardContent>
            <Skeleton className="h-[250px] w-full" />
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      {/* Overall Market Sentiment Gauge */}
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">Market Sentiment</CardTitle>
        </CardHeader>
        <CardContent className="flex flex-col items-center justify-center">
          <div className="flex items-center justify-center mb-4">
            {data.overallMarketSentiment >= 0 ? (
              <TrendingUp className={`h-12 w-12 ${data.overallMarketSentiment > 0.3 ? 'text-green-500' : 'text-amber-500'}`} />
            ) : (
              <TrendingDown className="h-12 w-12 text-red-500" />
            )}
          </div>
          <div className="text-4xl font-bold">
            {(data.overallMarketSentiment * 100).toFixed(0)}%
          </div>
          <div className="text-sm text-muted-foreground mt-2">
            {data.overallMarketSentiment >= 0.5 ? "Very Positive" :
             data.overallMarketSentiment >= 0.2 ? "Positive" :
             data.overallMarketSentiment >= -0.2 ? "Neutral" :
             data.overallMarketSentiment >= -0.5 ? "Negative" : "Very Negative"}
          </div>

          <div className="w-full bg-secondary h-2 rounded-full mt-4">
            <div
              className="h-full rounded-full"
              style={{
                width: `${((data.overallMarketSentiment + 1) / 2) * 100}%`,
                backgroundColor: getSentimentColor(data.overallMarketSentiment)
              }}
            ></div>
          </div>
          <div className="w-full flex justify-between text-xs text-muted-foreground mt-1">
            <span>-100%</span>
            <span>0%</span>
            <span>+100%</span>
          </div>
        </CardContent>
      </Card>

      {/* Sentiment Trend Chart */}
      <Card className="md:col-span-2">
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">Sentiment Trend</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-[200px] w-full">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={data.sentimentTrend}>
                <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                <XAxis dataKey="date" stroke="#6B7280" />
                <YAxis domain={[-1, 1]} stroke="#6B7280" />
                <Tooltip
                  contentStyle={{ backgroundColor: '#1F2937', borderColor: '#374151', borderRadius: '0.375rem' }}
                  formatter={(value: number) => [`${(value * 100).toFixed(0)}%`, 'Sentiment']}
                  labelFormatter={(label) => `Date: ${label}`}
                />
                <Legend />
                <Line
                  type="monotone"
                  dataKey="value"
                  name="Market Sentiment"
                  stroke="#3B82F6"
                  strokeWidth={2}
                  dot={{ r: 4 }}
                  activeDot={{ r: 6 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      {/* Asset Sentiment Comparison */}
      <Card className="md:col-span-3">
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">Asset Sentiment Comparison</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-[250px] w-full">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={data.sentimentByAsset}
                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                <XAxis dataKey="asset" stroke="#6B7280" />
                <YAxis domain={[-1, 1]} stroke="#6B7280" />
                <Tooltip
                  contentStyle={{ backgroundColor: '#1F2937', borderColor: '#374151', borderRadius: '0.375rem' }}
                  formatter={(value: number) => [`${(value * 100).toFixed(0)}%`, 'Sentiment']}
                />
                <Legend />
                <Bar
                  dataKey="current"
                  name="Current Sentiment"
                  radius={[4, 4, 0, 0]}
                  fill="#3B82F6"
                />
                <Bar
                  dataKey="change24h"
                  name="24h Change"
                  radius={[4, 4, 0, 0]}
                  fill="#EC4899"
                />
              </BarChart>
            </ResponsiveContainer>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mt-4">
            {data.sentimentByAsset.slice(0, 4).map((item) => (
              <Card key={item.asset} className="border-l-4" style={{ borderLeftColor: getSentimentColor(item.current) }}>
                <CardContent className="p-4">
                  <div className="flex justify-between items-center">
                    <div className="font-semibold text-lg">{item.asset}</div>
                    <div
                      className={`text-sm ${
                        item.change24h > 0
                          ? 'text-green-500'
                          : item.change24h < 0
                            ? 'text-red-500'
                            : 'text-gray-500'
                      } flex items-center`}
                    >
                      {item.change24h > 0 ? (
                        <TrendingUp className="h-3 w-3 mr-1" />
                      ) : item.change24h < 0 ? (
                        <TrendingDown className="h-3 w-3 mr-1" />
                      ) : null}
                      {(item.change24h * 100).toFixed(0)}%
                    </div>
                  </div>
                  <div className="text-2xl font-bold mt-1">
                    {(item.current * 100).toFixed(0)}%
                  </div>
                  <div className="text-xs text-muted-foreground capitalize mt-1">
                    {item.sentiment} sentiment
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
