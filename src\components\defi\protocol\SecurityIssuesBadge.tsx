
// React import removed - using new JSX transform
import { Badge } from "@/components/ui/badge";

interface SecurityIssuesBadgeProps {
  audit: {
    issues?: {
      critical: number;
      high: number;
      medium: number;
      low: number;
    }
  } | null;
}

const SecurityIssuesBadge = ({ audit }: SecurityIssuesBadgeProps) => {
  if (!audit || !audit.issues) return <Badge variant="outline" className="bg-crypto-positive/20 text-crypto-positive">N/A</Badge>;

  const { critical, high, medium, low } = audit.issues;

  if (critical === 0 && high === 0 && medium === 0 && low === 0) {
    return <Badge variant="outline" className="bg-crypto-positive/20 text-crypto-positive">None</Badge>;
  }

  return (
    <div className="flex gap-2">
      {critical > 0 && <Badge variant="outline" className="bg-crypto-negative/20 text-crypto-negative">C: {critical}</Badge>}
      {high > 0 && <Badge variant="outline" className="bg-warning/20 text-warning">H: {high}</Badge>}
      {medium > 0 && <Badge variant="outline" className="bg-chart-3/20 text-chart-3">M: {medium}</Badge>}
      {low > 0 && <Badge variant="outline" className="bg-info/20 text-info">L: {low}</Badge>}
    </div>
  );
};

export default SecurityIssuesBadge;
