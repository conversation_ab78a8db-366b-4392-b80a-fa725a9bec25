
import { cacheResponse, handleApiError } from "../coinGeckoClient";
import { Bridge } from "./types";

// Fetch cross-chain bridge comparison data
export const fetchCrossChainBridges = async (): Promise<Bridge[]> => {
  try {
    // In real app, this would call specific bridge APIs or aggregate data sources
    
    const bridgesList = [
      { name: "Arbitrum Bridge", networks: ["ethereum", "arbitrum"], security: 9 },
      { name: "Optimism Gateway", networks: ["ethereum", "optimism"], security: 9 },
      { name: "Polygon PoS Bridge", networks: ["ethereum", "polygon"], security: 8 },
      { name: "Binance Bridge", networks: ["ethereum", "bsc"], security: 7 },
      { name: "Hop Protocol", networks: ["ethereum", "polygon", "arbitrum", "optimism"], security: 8 },
      { name: "Stargate", networks: ["ethereum", "polygon", "bsc", "arbitrum", "optimism", "avalanche"], security: 7 },
      { name: "Across Protocol", networks: ["ethereum", "polygon", "arbitrum", "optimism"], security: 7 },
      { name: "Synapse", networks: ["ethereum", "polygon", "bsc", "avalanche"], security: 6 },
      { name: "cBridge", networks: ["ethereum", "polygon", "bsc", "arbitrum", "optimism", "avalanche"], security: 6 },
      { name: "Multichain", networks: ["ethereum", "polygon", "bsc", "avalanche"], security: 5 }
    ];
    
    // Network display names mapping
    const networkNames: Record<string, string> = {
      "ethereum": "Ethereum",
      "polygon": "Polygon",
      "bsc": "BNB Chain",
      "arbitrum": "Arbitrum",
      "optimism": "Optimism",
      "avalanche": "Avalanche"
    };
    
    // Generate realistic bridge data
    const bridges: Bridge[] = bridgesList.map(bridge => {
      // Calculate fee structure based on security and supported networks
      const percentageFee = (0.1 + (10 - bridge.security) * 0.05) / 100;
      
      // Fixed fees vary by network
      const fixedFees: Record<string, number> = {};
      bridge.networks.forEach(network => {
        switch (network) {
          case "ethereum":
            fixedFees[network] = 5 + Math.random() * 15;
            break;
          case "polygon":
            fixedFees[network] = 0.1 + Math.random() * 0.4;
            break;
          case "bsc":
            fixedFees[network] = 0.2 + Math.random() * 0.5;
            break;
          case "arbitrum":
            fixedFees[network] = 1 + Math.random() * 3;
            break;
          case "optimism":
            fixedFees[network] = 0.5 + Math.random() * 2;
            break;
          default:
            fixedFees[network] = 1 + Math.random() * 5;
        }
      });
      
      // Speed estimates (in minutes)
      const speed: Record<string, number> = {};
      bridge.networks.forEach(network => {
        if (network === "ethereum") {
          speed[network] = 10 + Math.floor(Math.random() * 20);
        } else {
          speed[network] = 2 + Math.floor(Math.random() * 8);
        }
      });
      
      // Security details
      const securityDetails = {
        audited: bridge.security > 5,
        auditors: bridge.security > 7 ? ["OpenZeppelin", "Trail of Bits"] : ["Certik"],
        tvlLocked: (bridge.security * 200 + Math.random() * 500) * 1000000,
        incidentHistory: bridge.security < 7 ? [
          { 
            date: "2023-04-15", 
            description: "Minor delay in transaction processing", 
            severity: "Low" 
          }
        ] : []
      };
      
      // Add a serious incident for one random bridge with lower security
      if (bridge.security <= 6 && Math.random() > 0.7) {
        securityDetails.incidentHistory.push({
          date: "2022-07-22",
          description: "Temporary bridge outage due to contract issue",
          severity: "Medium"
        });
      }
      
      // User rating based on security and speed
      const speedAvg = Object.values(speed).reduce((sum, val) => sum + val, 0) / Object.values(speed).length;
      const userRating = Math.min(5, Math.max(2.5, (bridge.security / 2) + (5 - speedAvg) / 10));
      
      // 24h volume based on security, networks and userRating
      const volume24h = bridge.networks.length * userRating * bridge.security * 1000000 * (0.5 + Math.random());
      
      return {
        id: bridge.name.toLowerCase().replace(/\s/g, '-'),
        name: bridge.name,
        supportedNetworks: bridge.networks.map(n => networkNames[n] || n),
        fees: {
          percentage: percentageFee,
          fixed: fixedFees
        },
        speed,
        securityScore: bridge.security,
        securityDetails,
        userRating: Math.round(userRating * 10) / 10,
        volume24h
      };
    });
    
    cacheResponse("bridges", bridges);
    return bridges;
    
  } catch (error) {
    return handleApiError(error, {
      key: "bridges",
      data: []
    });
  }
};
