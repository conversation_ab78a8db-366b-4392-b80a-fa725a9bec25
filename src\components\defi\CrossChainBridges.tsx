
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent, CardDescription } from "@/components/ui/card";
import { useDefiData } from "@/hooks/useDefiData";
import { Network } from "lucide-react";
import BridgeSelector from "./bridges/BridgeSelector";
import BridgeComparisonTable from "./bridges/BridgeComparisonTable";
import BridgeDetails from "./bridges/BridgeDetails";
import BridgeComparisonChart from "./bridges/BridgeComparisonChart";

export function CrossChainBridges({ isLoading }: { isLoading: boolean }) {
  const { bridges } = useDefiData();
  const [sourceChain, setSourceChain] = useState<string>("Ethereum");
  const [targetChain, setTargetChain] = useState<string>("Polygon");
  const [amount, setAmount] = useState<string>("1000");
  const [sortField, setSortField] = useState<string>("securityScore");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc");
  const [selectedBridge, setSelectedBridge] = useState<string | null>(null);

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Network className="h-5 w-5" /> Cross-Chain Bridge Explorer
          </CardTitle>
          <CardDescription>
            Compare fees, speed, and security of different cross-chain bridges
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <BridgeSelector.LoadingSkeleton />
          ) : (
            <BridgeSelector
              bridges={bridges || []}
              sourceChain={sourceChain}
              setSourceChain={setSourceChain}
              targetChain={targetChain}
              setTargetChain={setTargetChain}
              amount={amount}
              setAmount={setAmount}
            />
          )}
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <BridgeComparisonTable
            isLoading={isLoading}
            sourceChain={sourceChain}
            targetChain={targetChain}
            amount={amount}
            bridges={bridges || []}
            sortField={sortField}
            sortDirection={sortDirection}
            selectedBridge={selectedBridge}
            onSortChange={setSortField}
            onDirectionChange={setSortDirection}
            onBridgeSelect={setSelectedBridge}
          />

          <BridgeComparisonChart
            isLoading={isLoading}
            sourceChain={sourceChain}
            bridges={bridges || []}
          />
        </div>

        <div className="lg:col-span-1">
          <BridgeDetails
            isLoading={isLoading}
            sourceChain={sourceChain}
            targetChain={targetChain}
            amount={amount}
            bridges={bridges || []}
            selectedBridge={selectedBridge}
          />
        </div>
      </div>
    </div>
  );
}
