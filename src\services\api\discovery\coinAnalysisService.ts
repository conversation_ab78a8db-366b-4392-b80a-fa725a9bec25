
import { fetchCoinData } from "../coinMarketData";
import { generateAIResponse } from "../deepSeekClient";
import { handleApiError } from "../coinGeckoClient";

// Get detailed coin analysis with AI insights
export const getCoinAnalysis = async (coinId: string) => {
  try {
    const coinData = await fetchCoinData(coinId);
    
    const analysisPrompt = createAnalysisPrompt(coinData);
    const aiAnalysis = await generateAIResponse(analysisPrompt, { temperature: 0.3 });
    
    return {
      coinData,
      aiAnalysis: aiAnalysis || "Analysis temporarily unavailable."
    };
  } catch (error) {
    return handleApiError(error, {
      key: `analysis_${coinId}`,
      data: null
    });
  }
};

// Create analysis prompt for AI
const createAnalysisPrompt = (coinData: any): string => {
  return `
    Provide a brief analysis of ${coinData.name} (${coinData.symbol}):
    Price: $${coinData.market_data?.current_price?.usd}
    Market Cap Rank: ${coinData.market_cap_rank}
    24h Change: ${coinData.market_data?.price_change_percentage_24h}%
    
    Focus on:
    1. Recent momentum and trend analysis
    2. Key technical levels
    3. Fundamental strength indicators
    
    Keep it concise (3-4 sentences).
  `;
};
