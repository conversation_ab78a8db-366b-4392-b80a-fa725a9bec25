{"files": [], "references": [{"path": "./tsconfig.app.json"}, {"path": "./tsconfig.node.json"}], "compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "strict": true, "noImplicitAny": true, "noUnusedParameters": true, "skipLibCheck": true, "allowJs": false, "noUnusedLocals": true, "strictNullChecks": true, "exactOptionalPropertyTypes": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true}}