
import { coinGeckoAxios, handleApiError, cacheResponse } from "../coinGeckoClient";
import { MetricsDataPoint } from "@/hooks/useFundamentalAnalysis";
import { fetchTokenHistory } from "./historicalService";

// Fetch detailed metrics data for visualizations
export const fetchMetricsData = async (coinId: string): Promise<{
  fundamentalMetrics: MetricsDataPoint[];
  marketMetrics: MetricsDataPoint[];
  tradingVolume: { date: string; volume: number }[];
}> => {
  try {
    // Combine multiple API calls to gather comprehensive data
    const [coinData, marketData, volumeData] = await Promise.all([
      coinGeckoAxios.get(`/coins/${coinId}`).catch(() => ({ data: null })),
      coinGeckoAxios.get(`/coins/${coinId}/market_chart`, {
        params: {
          vs_currency: 'usd',
          days: 30
        }
      }).catch(() => ({ data: { total_volumes: [] } })),
      fetchTokenHistory(coinId, 14).catch(() => [])
    ]);
    
    // Extract fundamental metrics from coin data
    const fundamentalMetrics: MetricsDataPoint[] = [];
    
    if (coinData?.data) {
      // Developer activity metric
      const devScore = (coinData.data.developer_data?.stars || 0) / 1000 + 
                      (coinData.data.developer_data?.forks || 0) / 500 + 
                      (coinData.data.developer_data?.pull_request_contributors || 0) / 20;
                      
      fundamentalMetrics.push({
        name: "Developer Activity",
        value: Math.min(10, Math.max(1, devScore * 2)),
        fullMark: 10,
        description: "Based on GitHub stars, commits and contributors"
      });
      
      // Community engagement
      const communityScore = (coinData.data.community_data?.twitter_followers || 0) / 100000 + 
                            (coinData.data.community_data?.reddit_subscribers || 0) / 50000;
                            
      fundamentalMetrics.push({
        name: "Community",
        value: Math.min(10, Math.max(1, communityScore * 2)),
        fullMark: 10,
        description: "Based on social media following and engagement"
      });
      
      // Liquidity
      const liquidityScore = coinData.data.market_data?.total_volume?.usd / 
                          (coinData.data.market_data?.market_cap?.usd || 1) * 10;
                          
      fundamentalMetrics.push({
        name: "Liquidity",
        value: Math.min(10, Math.max(1, liquidityScore * 5)),
        fullMark: 10,
        description: "Volume to market cap ratio, higher is better"
      });
      
      // Adoption metric
      const adoptionScore = Math.log10(coinData.data.market_data?.market_cap?.usd || 1) - 5;
      
      fundamentalMetrics.push({
        name: "Adoption",
        value: Math.min(10, Math.max(1, adoptionScore)),
        fullMark: 10,
        description: "Based on market capitalization"
      });
      
      // Supply economics
      const supplyScore = coinData.data.market_data?.max_supply ? 
                         (coinData.data.market_data?.circulating_supply / coinData.data.market_data?.max_supply) * 10 : 
                         5;
                         
      fundamentalMetrics.push({
        name: "Supply Economics",
        value: Math.min(10, Math.max(1, 10 - supplyScore)),
        fullMark: 10,
        description: "Scarcity and token distribution"
      });
    }
    
    // Generate market-specific metrics
    const marketMetrics: MetricsDataPoint[] = [
      {
        name: "Price Stability",
        value: Math.min(10, Math.max(1, 10 - Math.abs(coinData?.data?.market_data?.price_change_percentage_24h || 0) / 3)),
        fullMark: 10,
        description: "Lower price volatility scores higher"
      },
      {
        name: "Market Sentiment",
        value: Math.min(10, Math.max(1, 5 + (coinData?.data?.market_data?.price_change_percentage_7d || 0) / 5)),
        fullMark: 10,
        description: "Based on recent price trends"
      },
      {
        name: "Exchange Coverage",
        value: Math.min(10, Math.max(1, (coinData?.data?.tickers?.length || 0) / 5)),
        fullMark: 10,
        description: "Number of exchanges listing the token"
      }
    ];
    
    // Process volume data for chart
    let tradingVolume: { date: string; volume: number }[] = [];
    
    if (volumeData.length > 0) {
      // Group by date and aggregate volume
      const volumeByDate = new Map<string, number>();
      
      volumeData.forEach(dataPoint => {
        const date = new Date(dataPoint.timestamp).toLocaleDateString();
        const currentVolume = volumeByDate.get(date) || 0;
        volumeByDate.set(date, currentVolume + dataPoint.volume);
      });
      
      // Convert to array format
      tradingVolume = Array.from(volumeByDate.entries()).map(([date, volume]) => ({
        date,
        volume
      }));
      
      // Sort by date
      tradingVolume.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
    } else if (marketData?.data?.total_volumes) {
      // Use market chart data if history fetch failed
      tradingVolume = marketData.data.total_volumes
        .filter((_: any, index: number) => index % 3 === 0)  // Sample every 3rd point
        .map(([timestamp, volume]: [number, number]) => ({
          date: new Date(timestamp).toLocaleDateString(),
          volume
        }));
    }
    
    const result = {
      fundamentalMetrics,
      marketMetrics,
      tradingVolume
    };
    
    cacheResponse(`metrics_${coinId}`, result);
    return result;
  } catch (error) {
    console.error("Error fetching metrics data:", error);
    return handleApiError(error, {
      key: `metrics_${coinId}`,
      data: {
        fundamentalMetrics: [],
        marketMetrics: [],
        tradingVolume: []
      }
    });
  }
};
