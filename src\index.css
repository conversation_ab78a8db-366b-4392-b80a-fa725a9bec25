
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Light theme colors - Clean and professional */
    --background: 255 255 255;
    --foreground: 15 23 42;
    --card: 255 255 255;
    --card-foreground: 15 23 42;
    --popover: 255 255 255;
    --popover-foreground: 15 23 42;
    --primary: 234 88 12;
    --primary-foreground: 255 255 255;
    --secondary: 248 250 252;
    --secondary-foreground: 51 65 85;
    --muted: 248 250 252;
    --muted-foreground: 100 116 139;
    --accent: 248 250 252;
    --accent-foreground: 51 65 85;
    --destructive: 239 68 68;
    --destructive-foreground: 255 255 255;
    --border: 226 232 240;
    --input: 255 255 255;
    --ring: 234 88 12;
    --radius: 0.5rem;
    --chart-1: 234 88 12;
    --chart-2: 59 130 246;
    --chart-3: 16 185 129;
    --chart-4: 245 158 11;
    --chart-5: 139 92 246;
  }

  .dark {
    /* Dark theme colors - Consistent dark palette */
    --background: 15 23 42;
    --foreground: 248 250 252;
    --card: 30 41 59;
    --card-foreground: 248 250 252;
    --popover: 30 41 59;
    --popover-foreground: 248 250 252;
    --primary: 234 88 12;
    --primary-foreground: 255 255 255;
    --secondary: 51 65 85;
    --secondary-foreground: 226 232 240;
    --muted: 51 65 85;
    --muted-foreground: 148 163 184;
    --accent: 51 65 85;
    --accent-foreground: 226 232 240;
    --destructive: 239 68 68;
    --destructive-foreground: 255 255 255;
    --border: 71 85 105;
    --input: 51 65 85;
    --ring: 234 88 12;
    --chart-1: 234 88 12;
    --chart-2: 59 130 246;
    --chart-3: 16 185 129;
    --chart-4: 245 158 11;
    --chart-5: 139 92 246;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
    font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply text-foreground;
  }

  /* Ensure consistent scrollbar styling */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-muted;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/30 rounded;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/50;
  }
}
