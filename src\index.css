
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Light theme colors - WCAG AA compliant */
    --background: 255 255 255;
    --foreground: 17 24 39;
    --card: 255 255 255;
    --card-foreground: 17 24 39;
    --popover: 255 255 255;
    --popover-foreground: 17 24 39;
    --primary: 37 99 235;
    --primary-foreground: 255 255 255;
    --secondary: 243 244 246;
    --secondary-foreground: 55 65 81;
    --muted: 243 244 246;
    --muted-foreground: 107 114 128;
    --accent: 243 244 246;
    --accent-foreground: 55 65 81;
    --destructive: 220 38 38;
    --destructive-foreground: 255 255 255;
    --border: 229 231 235;
    --input: 255 255 255;
    --ring: 37 99 235;
    --radius: 0.5rem;

    /* Chart colors - High contrast and accessible */
    --chart-1: 37 99 235;   /* Blue */
    --chart-2: 16 185 129;  /* Emerald */
    --chart-3: 245 158 11;  /* Amber */
    --chart-4: 239 68 68;   /* Red */
    --chart-5: 139 92 246;  /* Violet */
    --chart-6: 236 72 153;  /* Pink */

    /* Crypto-specific colors */
    --crypto-positive: 16 185 129;
    --crypto-negative: 239 68 68;
    --crypto-neutral: 107 114 128;
  }

  .dark {
    /* Dark theme colors - WCAG AA compliant */
    --background: 17 24 39;
    --foreground: 243 244 246;
    --card: 31 41 55;
    --card-foreground: 243 244 246;
    --popover: 31 41 55;
    --popover-foreground: 243 244 246;
    --primary: 59 130 246;
    --primary-foreground: 17 24 39;
    --secondary: 55 65 81;
    --secondary-foreground: 209 213 219;
    --muted: 55 65 81;
    --muted-foreground: 156 163 175;
    --accent: 55 65 81;
    --accent-foreground: 209 213 219;
    --destructive: 248 113 113;
    --destructive-foreground: 17 24 39;
    --border: 75 85 99;
    --input: 55 65 81;
    --ring: 59 130 246;

    /* Chart colors - High contrast for dark mode */
    --chart-1: 96 165 250;  /* Light Blue */
    --chart-2: 52 211 153;  /* Light Emerald */
    --chart-3: 251 191 36;  /* Light Amber */
    --chart-4: 248 113 113; /* Light Red */
    --chart-5: 167 139 250; /* Light Violet */
    --chart-6: 244 114 182; /* Light Pink */

    /* Crypto-specific colors for dark mode */
    --crypto-positive: 52 211 153;
    --crypto-negative: 248 113 113;
    --crypto-neutral: 156 163 175;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
    font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply text-foreground;
  }

  /* Ensure consistent scrollbar styling */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-muted;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/30 rounded;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/50;
  }
}
