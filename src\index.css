
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Light theme - Modern, clean, professional */
    --background: 255 255 255;
    --foreground: 15 23 42;
    --card: 255 255 255;
    --card-foreground: 15 23 42;
    --popover: 255 255 255;
    --popover-foreground: 15 23 42;
    --primary: 59 130 246;
    --primary-foreground: 255 255 255;
    --secondary: 248 250 252;
    --secondary-foreground: 51 65 85;
    --muted: 248 250 252;
    --muted-foreground: 100 116 139;
    --accent: 248 250 252;
    --accent-foreground: 51 65 85;
    --destructive: 239 68 68;
    --destructive-foreground: 255 255 255;
    --border: 226 232 240;
    --input: 255 255 255;
    --ring: 59 130 246;
    --radius: 0.5rem;

    /* Chart colors - Professional and accessible */
    --chart-1: 59 130 246;   /* Blue */
    --chart-2: 16 185 129;   /* Emerald */
    --chart-3: 245 158 11;   /* Amber */
    --chart-4: 239 68 68;    /* Red */
    --chart-5: 139 92 246;   /* Violet */
    --chart-6: 236 72 153;   /* Pink */

    /* Crypto-specific colors */
    --crypto-positive: 34 197 94;
    --crypto-negative: 239 68 68;
    --crypto-neutral: 100 116 139;
  }

  .dark {
    /* Dark theme - Modern, elegant, easy on eyes */
    --background: 2 6 23;
    --foreground: 248 250 252;
    --card: 15 23 42;
    --card-foreground: 248 250 252;
    --popover: 15 23 42;
    --popover-foreground: 248 250 252;
    --primary: 96 165 250;
    --primary-foreground: 2 6 23;
    --secondary: 30 41 59;
    --secondary-foreground: 203 213 225;
    --muted: 30 41 59;
    --muted-foreground: 148 163 184;
    --accent: 30 41 59;
    --accent-foreground: 203 213 225;
    --destructive: 248 113 113;
    --destructive-foreground: 2 6 23;
    --border: 51 65 85;
    --input: 30 41 59;
    --ring: 96 165 250;

    /* Chart colors - Vibrant for dark mode */
    --chart-1: 96 165 250;   /* Light Blue */
    --chart-2: 52 211 153;   /* Light Emerald */
    --chart-3: 251 191 36;   /* Light Amber */
    --chart-4: 248 113 113;  /* Light Red */
    --chart-5: 167 139 250;  /* Light Violet */
    --chart-6: 244 114 182;  /* Light Pink */

    /* Crypto-specific colors for dark mode */
    --crypto-positive: 52 211 153;
    --crypto-negative: 248 113 113;
    --crypto-neutral: 148 163 184;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
    font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply text-foreground;
  }

  /* Ensure consistent scrollbar styling */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-muted;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/30 rounded;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/50;
  }
}
