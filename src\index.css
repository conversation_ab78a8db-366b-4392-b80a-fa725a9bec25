
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Light theme - Modern, accessible, HSLuv-based palette */
    --background: 255 255 255;
    --foreground: 16 24 40;
    --card: 255 255 255;
    --card-foreground: 16 24 40;
    --popover: 255 255 255;
    --popover-foreground: 16 24 40;
    --primary: 37 99 235;
    --primary-foreground: 255 255 255;
    --secondary: 248 250 252;
    --secondary-foreground: 51 65 85;
    --muted: 241 245 249;
    --muted-foreground: 100 116 139;
    --accent: 241 245 249;
    --accent-foreground: 51 65 85;
    --destructive: 220 38 38;
    --destructive-foreground: 255 255 255;
    --border: 226 232 240;
    --input: 255 255 255;
    --ring: 37 99 235;
    --radius: 0.5rem;

    /* Chart colors - WCAG AAA compliant, perceptually uniform */
    --chart-1: 37 99 235;    /* Blue-55 */
    --chart-2: 5 150 105;    /* Emerald-55 */
    --chart-3: 217 119 6;    /* Amber-55 */
    --chart-4: 220 38 38;    /* Red-55 */
    --chart-5: 124 58 237;   /* Violet-55 */
    --chart-6: 219 39 119;   /* Pink-55 */

    /* Crypto-specific colors - High contrast for financial data */
    --crypto-positive: 5 150 105;
    --crypto-negative: 220 38 38;
    --crypto-neutral: 100 116 139;
  }

  .dark {
    /* Dark theme - Modern, accessible, HSLuv-based palette */
    --background: 3 7 18;
    --foreground: 248 250 252;
    --card: 15 23 42;
    --card-foreground: 248 250 252;
    --popover: 15 23 42;
    --popover-foreground: 248 250 252;
    --primary: 96 165 250;
    --primary-foreground: 3 7 18;
    --secondary: 30 41 59;
    --secondary-foreground: 203 213 225;
    --muted: 30 41 59;
    --muted-foreground: 148 163 184;
    --accent: 30 41 59;
    --accent-foreground: 203 213 225;
    --destructive: 248 113 113;
    --destructive-foreground: 3 7 18;
    --border: 51 65 85;
    --input: 30 41 59;
    --ring: 96 165 250;

    /* Chart colors - WCAG AAA compliant for dark backgrounds */
    --chart-1: 96 165 250;   /* Blue-35 */
    --chart-2: 52 211 153;   /* Emerald-35 */
    --chart-3: 251 191 36;   /* Amber-35 */
    --chart-4: 248 113 113;  /* Red-35 */
    --chart-5: 167 139 250;  /* Violet-35 */
    --chart-6: 244 114 182;  /* Pink-35 */

    /* Crypto-specific colors for dark mode */
    --crypto-positive: 52 211 153;
    --crypto-negative: 248 113 113;
    --crypto-neutral: 148 163 184;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
    font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply text-foreground;
  }

  /* Ensure consistent scrollbar styling */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-muted;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/30 rounded;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/50;
  }
}
