
import { coinGeckoAxios, handleApiError, cacheResponse } from "../coinGeckoClient";

// Fetch development activity metrics
export const fetchDevelopmentActivity = async (coinId: string) => {
  try {
    const response = await coinGeckoAxios.get(`/coins/${coinId}`, {
      params: {
        localization: false,
        tickers: false,
        market_data: false,
        community_data: false,
        developer_data: true,
      }
    });
    
    const { developer_data } = response.data;
    
    cacheResponse(`development_${coinId}`, developer_data);
    return developer_data;
  } catch (error) {
    return handleApiError(error, {
      key: `development_${coinId}`,
      data: null
    });
  }
};
