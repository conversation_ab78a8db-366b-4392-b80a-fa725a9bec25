
import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Loader2, ExternalLink, Search } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableHeader, TableBody, TableRow, TableHead, TableCell } from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { fetchExchanges } from "@/services/api/marketInsightsApi";

export default function ExchangesList() {
  const [searchQuery, setSearchQuery] = useState("");
  
  const { data: exchanges, isLoading } = useQuery({
    queryKey: ['exchanges'],
    queryFn: () => fetchExchanges(),
    staleTime: 60 * 60 * 1000, // 1 hour
  });
  
  const filteredExchanges = exchanges?.filter((exchange: any) => 
    exchange.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <Card>
      <CardHeader>
        <CardTitle>Cryptocurrency Exchanges</CardTitle>
        <div className="relative max-w-sm">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search exchanges..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex items-center justify-center h-60">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          </div>
        ) : exchanges ? (
          <div className="border rounded-md">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-12">#</TableHead>
                  <TableHead>Exchange</TableHead>
                  <TableHead className="text-right">Trust Score</TableHead>
                  <TableHead className="text-right">Volume (24h BTC)</TableHead>
                  <TableHead className="text-right">Country</TableHead>
                  <TableHead className="text-right">Founded</TableHead>
                  <TableHead className="w-10"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredExchanges?.slice(0, 20).map((exchange: any, idx: number) => (
                  <TableRow key={exchange.id}>
                    <TableCell>{exchange.trust_score_rank || idx + 1}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <div className="w-6 h-6">
                          <img 
                            src={exchange.image} 
                            alt={exchange.name} 
                            className="w-full h-full object-contain"
                            onError={(e) => {
                              (e.target as HTMLImageElement).style.display = 'none';
                            }}
                          />
                        </div>
                        <span className="font-medium">{exchange.name}</span>
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <TrustScoreBadge score={exchange.trust_score} />
                    </TableCell>
                    <TableCell className="text-right font-mono">
                      {exchange.trade_volume_24h_btc?.toLocaleString(undefined, {
                        maximumFractionDigits: 2
                      })} BTC
                    </TableCell>
                    <TableCell className="text-right">
                      {exchange.country || "Global"}
                    </TableCell>
                    <TableCell className="text-right">
                      {exchange.year_established || "N/A"}
                    </TableCell>
                    <TableCell>
                      <a 
                        href={exchange.url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-muted-foreground hover:text-primary transition-colors"
                      >
                        <ExternalLink className="h-4 w-4" />
                      </a>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        ) : (
          <div className="text-center py-12 text-muted-foreground">
            No exchange data available
          </div>
        )}
      </CardContent>
    </Card>
  );
}

const TrustScoreBadge = ({ score }: { score: number }) => {
  let colorClass = "bg-red-500/10 text-red-500";
  
  if (score >= 8) {
    colorClass = "bg-green-500/10 text-green-500";
  } else if (score >= 6) {
    colorClass = "bg-yellow-500/10 text-yellow-500";
  } else if (score >= 4) {
    colorClass = "bg-orange-500/10 text-orange-500";
  }
  
  return (
    <span className={`px-2 py-0.5 rounded-full text-xs font-medium ${colorClass}`}>
      {score}/10
    </span>
  );
};
