
import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Loader2 } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>att<PERSON>,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer,
  Tooltip,
  Legend,
  ZAxis
} from "recharts";
import { fetchTopCoins, fetchCoinData } from "@/services/api/coinMarketData";
import { generateCorrelationData } from "@/services/api/marketInsightsApi";

export function PriceCorrelation() {
  const [baseAsset, setBaseAsset] = useState("bitcoin");
  const [timeframe, setTimeframe] = useState("30");

  const { data: topCoins, isLoading: isLoadingCoins } = useQuery({
    queryKey: ['topCoins'],
    queryFn: () => fetchTopCoins(20),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const { data: correlationData, isLoading: isLoadingCorrelation } = useQuery({
    queryKey: ['correlation', baseAsset, timeframe],
    queryFn: () => generateCorrelationData(baseAsset, parseInt(timeframe)),
    staleTime: 30 * 60 * 1000, // 30 minutes
    enabled: !!baseAsset && !!timeframe,
  });

  const isLoading = isLoadingCoins || isLoadingCorrelation;

  // Created custom domain for better visualization
  const getAxisDomain = (data: any[], key: string) => {
    if (!data || data.length === 0) return [-100, 100];
    const values = data.map(item => item[key]);
    const min = Math.floor(Math.min(...values) - 5);
    const max = Math.ceil(Math.max(...values) + 5);
    return [min, max];
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Asset Price Correlation</CardTitle>
        <CardDescription>
          Compare price movements between cryptocurrencies to identify correlations
        </CardDescription>

        <div className="flex flex-col sm:flex-row gap-4 mt-4">
          <div className="w-full sm:w-1/3">
            <label className="block text-sm font-medium text-muted-foreground mb-2">
              Base Asset
            </label>
            <Select
              value={baseAsset}
              onValueChange={setBaseAsset}
              disabled={isLoadingCoins}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select base asset" />
              </SelectTrigger>
              <SelectContent>
                {topCoins?.slice(0, 10).map((coin: any) => (
                  <SelectItem key={coin.id} value={coin.id}>
                    {coin.name} ({coin.symbol.toUpperCase()})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="w-full sm:w-1/3">
            <label className="block text-sm font-medium text-muted-foreground mb-2">
              Timeframe
            </label>
            <Select value={timeframe} onValueChange={setTimeframe}>
              <SelectTrigger>
                <SelectValue placeholder="Select timeframe" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7">7 Days</SelectItem>
                <SelectItem value="14">14 Days</SelectItem>
                <SelectItem value="30">30 Days</SelectItem>
                <SelectItem value="90">90 Days</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex items-center justify-center h-80">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          </div>
        ) : correlationData ? (
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <ScatterChart
                margin={{
                  top: 20,
                  right: 20,
                  bottom: 20,
                  left: 20,
                }}
              >
                <CartesianGrid strokeDasharray="3 3" stroke="rgba(0,0,0,0.1)" />
                <XAxis
                  type="number"
                  dataKey="baseChange"
                  name="Base Asset Change"
                  unit="%"
                  domain={getAxisDomain(correlationData, 'baseChange')}
                  label={{
                    value: `${baseAsset.charAt(0).toUpperCase() + baseAsset.slice(1)} Price Change (%)`,
                    position: 'bottom',
                    style: { textAnchor: 'middle' }
                  }}
                />
                <YAxis
                  type="number"
                  dataKey="comparedChange"
                  name="Compared Asset Change"
                  unit="%"
                  domain={getAxisDomain(correlationData, 'comparedChange')}
                  label={{
                    value: 'Asset Price Change (%)',
                    angle: -90,
                    position: 'left',
                    style: { textAnchor: 'middle' }
                  }}
                />
                <ZAxis type="number" range={[60, 300]} />
                <Tooltip
                  content={({ active, payload }) => {
                    if (active && payload && payload.length) {
                      const data = payload[0].payload;
                      return (
                        <div className="bg-background border border-border p-3 rounded-md shadow-md">
                          <p className="font-medium">{data.assetName} ({data.assetSymbol})</p>
                          <p className="text-sm text-muted-foreground">
                            {baseAsset.charAt(0).toUpperCase() + baseAsset.slice(1)} change: <span className="font-medium">{data.baseChange.toFixed(2)}%</span>
                          </p>
                          <p className="text-sm text-muted-foreground">
                            {data.assetName} change: <span className="font-medium">{data.comparedChange.toFixed(2)}%</span>
                          </p>
                          <p className="text-xs mt-1">Correlation: <span className="font-medium">{data.correlation.toFixed(2)}</span></p>
                        </div>
                      );
                    }
                    return null;
                  }}
                />
                <Legend />
                <Scatter
                  name="Price Correlation"
                  data={correlationData}
                  fill="#8884d8"
                  fillOpacity={0.6}
                  shape={(props) => {
                    const { cx, cy, r } = props;
                    const fill = props.payload.correlation > 0.7 ? "#22c55e" :
                              props.payload.correlation > 0.3 ? "#f59e0b" :
                              props.payload.correlation > 0 ? "#3b82f6" : "#ef4444";
                    return <circle cx={cx} cy={cy} r={r} fill={fill} fillOpacity={0.6} />;
                  }}
                />
              </ScatterChart>
            </ResponsiveContainer>
          </div>
        ) : (
          <div className="text-center py-12 text-muted-foreground">
            No correlation data available
          </div>
        )}

        <div className="flex items-center justify-center gap-6 mt-6">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-green-500 opacity-60"></div>
            <span className="text-sm">Strong Positive</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-yellow-500 opacity-60"></div>
            <span className="text-sm">Moderate Positive</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-blue-500 opacity-60"></div>
            <span className="text-sm">Weak Correlation</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-red-500 opacity-60"></div>
            <span className="text-sm">Negative Correlation</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
