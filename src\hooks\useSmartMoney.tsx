
import { useState, useEffect } from "react";
import axios from "axios";
import { handleApiError, cacheResponse } from "@/services/api/coinGeckoClient";

// Types for smart money data
type Movement = {
  id: string;
  wallet: string;
  label: string;
  action: "Buy" | "Sell" | "Transfer";
  asset: string;
  amount: number;
  valueUSD: number;
  timeAgo: string;
};

type Distribution = {
  name: string;
  percentage: number;
  change: number;
  color: string;
};

type TimeframeType = "24h" | "7d" | "30d";

// Function to fetch whale movements from whale alert API
const fetchWhaleMovements = async (timeframe: TimeframeType): Promise<Movement[]> => {
  try {
    // In reality you'd use a whale tracking API like Whale Alert
    // For this demo, we'll simulate with etherscan API for large transactions
    // Get data from etherscan API for large transactions
    const response = await axios.get(`https://api.etherscan.io/api`, {
      params: {
        module: 'account',
        action: 'txlist',
        address: '******************************************', // WETH contract
        startblock: 0,
        endblock: ********,
        page: 1,
        offset: 20,
        sort: 'desc',
        apikey: 'YourEtherscanAPIKey' // Note: this should be in env vars in production
      }
    });
    
    // Process the results
    const now = new Date();
    let limitDate: Date;
    
    switch(timeframe) {
      case '7d':
        limitDate = new Date(now.setDate(now.getDate() - 7));
        break;
      case '30d':
        limitDate = new Date(now.setDate(now.getDate() - 30));
        break;
      default:
        limitDate = new Date(now.setDate(now.getDate() - 1));
    }
    
    // Generate movements with realistic data
    // In production, this would come from Whale Alert API or similar
    const movements: Movement[] = [
      {
        id: "1",
        wallet: "0x3a68...1e94",
        label: "Binance",
        action: "Buy",
        asset: "Bitcoin (BTC)",
        amount: 213.45,
        valueUSD: 12423451,
        timeAgo: "2 hours ago"
      },
      {
        id: "2",
        wallet: "0x7b21...8f12",
        label: "Whale",
        action: "Sell",
        asset: "Ethereum (ETH)",
        amount: 1546.78,
        valueUSD: 3765234,
        timeAgo: "3 hours ago"
      },
      {
        id: "3",
        wallet: "0xfe32...9d21",
        label: "Exchange",
        action: "Transfer",
        asset: "Solana (SOL)",
        amount: 42578,
        valueUSD: 1870432,
        timeAgo: "5 hours ago"
      },
      {
        id: "4",
        wallet: "0x6c8a...3f45",
        label: "Jump Trading",
        action: "Buy",
        asset: "Arbitrum (ARB)",
        amount: 192837,
        valueUSD: 643291,
        timeAgo: "6 hours ago"
      },
      {
        id: "5",
        wallet: "0x9d2f...5e72",
        label: "Whale",
        action: "Buy",
        asset: "Chainlink (LINK)",
        amount: 76542,
        valueUSD: 1023458,
        timeAgo: "8 hours ago"
      },
      {
        id: "6",
        wallet: "0x2e4a...1c87",
        label: "Wintermute",
        action: "Sell",
        asset: "Polygon (MATIC)",
        amount: 531000,
        valueUSD: 432198,
        timeAgo: "12 hours ago"
      },
      {
        id: "7",
        wallet: "0x8f1d...7b34",
        label: "Individual",
        action: "Buy",
        asset: "Avalanche (AVAX)",
        amount: 12467,
        valueUSD: 278943,
        timeAgo: "14 hours ago"
      }
    ];
    
    // Add more data for longer timeframes
    if (timeframe === "7d" || timeframe === "30d") {
      movements.push({
        id: "8",
        wallet: "0x4f2e...9c31",
        label: "Coinbase",
        action: "Buy",
        asset: "Cardano (ADA)",
        amount: 3500000,
        valueUSD: 1456000,
        timeAgo: "2 days ago"
      },
      {
        id: "9",
        wallet: "0x1a3c...7d45",
        label: "Whale",
        action: "Transfer",
        asset: "XRP (XRP)",
        amount: 6700000,
        valueUSD: 2354000,
        timeAgo: "3 days ago"
      });
    }
    
    // Add even more data for 30d
    if (timeframe === "30d") {
      movements.push({
        id: "10",
        wallet: "0x5d7e...2b18",
        label: "Jump Trading",
        action: "Buy",
        asset: "Dogecoin (DOGE)",
        amount: 43000000,
        valueUSD: 5123000,
        timeAgo: "2 weeks ago"
      },
      {
        id: "11",
        wallet: "0x8c3f...6a92",
        label: "Individual",
        action: "Sell",
        asset: "Polkadot (DOT)",
        amount: 870000,
        valueUSD: 6432000,
        timeAgo: "3 weeks ago"
      });
    }
    
    cacheResponse(`whale_movements_${timeframe}`, movements);
    return movements;
  } catch (error) {
    console.error("Error fetching whale movements:", error);
    return handleApiError(error, {
      key: `whale_movements_${timeframe}`,
      data: []
    });
  }
};

// Function to fetch wallet distribution data
const fetchWalletDistribution = async (): Promise<Distribution[]> => {
  try {
    // In a real implementation, we'd use data from an on-chain analytics API
    // For this demo, we'll return realistic but static data
    const distribution: Distribution[] = [
      { name: "Ethereum (ETH)", percentage: 42, change: 5.2, color: "bg-blue-500" },
      { name: "Bitcoin (BTC)", percentage: 38, change: -2.1, color: "bg-amber-500" },
      { name: "Solana (SOL)", percentage: 15, change: 12.7, color: "bg-green-500" },
      { name: "Avalanche (AVAX)", percentage: 5, change: 1.3, color: "bg-purple-500" }
    ];
    
    cacheResponse("wallet_distribution", distribution);
    return distribution;
  } catch (error) {
    return handleApiError(error, {
      key: "wallet_distribution",
      data: []
    });
  }
};

export function useSmartMoney() {
  const [isLoading, setIsLoading] = useState(true);
  const [timeframe, setTimeframe] = useState<TimeframeType>("24h");
  const [movements, setMovements] = useState<Movement[]>([]);
  const [walletDistribution, setWalletDistribution] = useState<Distribution[]>([]);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const [movementsData, distributionData] = await Promise.all([
        fetchWhaleMovements(timeframe),
        fetchWalletDistribution()
      ]);
      
      setMovements(movementsData);
      setWalletDistribution(distributionData);
    } catch (err: any) {
      console.error('Error fetching smart money data:', err);
      setError(err.message || 'Failed to load smart money data.');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [timeframe]);

  return {
    movements,
    walletDistribution,
    isLoading,
    timeframe,
    setTimeframe,
    error,
    refresh: fetchData
  };
}
