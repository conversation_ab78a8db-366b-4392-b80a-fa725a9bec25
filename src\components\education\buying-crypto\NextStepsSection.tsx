
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { <PERSON> } from "react-router-dom";
import { CheckCircle, ExternalLink, Shield } from "lucide-react";

interface NextStepsSectionProps {
  onReset: () => void;
}

export function NextStepsSection({ onReset }: NextStepsSectionProps) {
  return (
    <>
      <div className="mt-6 rounded-lg overflow-hidden h-64">
        <img
          src="https://images.unsplash.com/photo-1642104704074-907c0698cbd9?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80"
          alt="Person analyzing cryptocurrency investment strategies on a laptop"
          className="w-full h-full object-cover"
        />
      </div>

      <div className="mt-6">
        <h3 className="text-xl font-medium mb-4">Next Steps</h3>
        <div className="grid md:grid-cols-3 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-base flex items-center">
                <CheckCircle className="mr-2 h-4 w-4 text-primary" />
                Choose Your Platform
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm">Based on your fee analysis and personal priorities, select an exchange that offers the best balance of costs and features for your needs.</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-base flex items-center">
                <ExternalLink className="mr-2 h-4 w-4 text-primary" />
                Compare Markets
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm">Use our market insights tool to research current prices and trends before making your purchase.</p>
              <Button asChild variant="link" className="px-0 mt-2">
                <Link to="/market-insights">Market Insights</Link>
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-base flex items-center">
                <Shield className="mr-2 h-4 w-4 text-primary" />
                Secure Your Assets
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm">After purchasing, learn how to properly secure your cryptocurrency with our wallet security guide.</p>
              <Button asChild variant="link" className="px-0 mt-2">
                <Link to="/education/tutorial/wallet-security">Wallet Security</Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>

      <div className="mt-8 flex justify-between">
        <Button variant="outline" onClick={onReset}>
          Start Over
        </Button>
        <Button variant="default">
          Complete Tutorial
        </Button>
      </div>
    </>
  );
}
